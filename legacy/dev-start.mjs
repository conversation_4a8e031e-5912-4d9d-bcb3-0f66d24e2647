#!/usr/bin/env node

/**
 * 跨平台开发启动脚本
 * 用于在不同平台上启动 EchoWave Windows Client
 */

import { spawn } from 'child_process';
import { DEV_CONFIG, devLog, devWarn } from './dev-config.mjs';

function main() {
    devLog('Starting EchoWave Windows Client in development mode...');
    devLog('Platform:', process.platform);
    devLog('Architecture:', process.arch);
    
    const config = DEV_CONFIG.getCurrentPlatformConfig();
    
    if (config.mockMode) {
        devWarn('Running in MOCK MODE - Windows-specific features will be simulated');
        devLog('Mock responses:', config.mockResponses);
    }
    
    // 设置环境变量
    const env = {
        ...process.env,
        NODE_ENV: 'development',
        ELECTRON_IS_DEV: '1',
        DEV_PLATFORM: process.platform,
        MOCK_MODE: config.mockMode ? '1' : '0'
    };
    
    // 根据平台选择启动命令
    let command, args;
    
    if (process.platform === 'win32') {
        command = 'npm';
        args = ['run', 'dev'];
    } else {
        // macOS 和 Linux
        command = 'npm';
        args = ['run', 'dev'];
        
        devWarn('Note: This application is designed for Windows.');
        devWarn('Some features may not work correctly on this platform.');
        devWarn('This is intended for development and testing purposes only.');
    }
    
    devLog(`Executing: ${command} ${args.join(' ')}`);
    
    // 启动应用
    const child = spawn(command, args, {
        env,
        stdio: 'inherit',
        shell: true
    });
    
    child.on('error', (error) => {
        console.error('Failed to start application:', error);
        process.exit(1);
    });
    
    child.on('exit', (code) => {
        devLog(`Application exited with code ${code}`);
        process.exit(code);
    });
    
    // 处理进程信号
    process.on('SIGINT', () => {
        devLog('Received SIGINT, shutting down...');
        child.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
        devLog('Received SIGTERM, shutting down...');
        child.kill('SIGTERM');
    });
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
