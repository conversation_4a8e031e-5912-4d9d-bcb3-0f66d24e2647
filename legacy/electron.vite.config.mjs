import { resolve } from "path";
import { defineConfig, externalizeDepsPlugin } from "electron-vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";

export default defineConfig({
    main: {
        plugins: [externalizeDepsPlugin()],
        define: {
            "import.meta.env.APP_ENV": JSON.stringify(
                process.env.APP_ENV ||
                    process.env.NODE_ENV ||
                    process.env.VITE_MODE,
            ),
        },
    },
    preload: {
        plugins: [externalizeDepsPlugin()],
    },
    renderer: {
        base: "./",
        resolve: {
            alias: {
                "@renderer": resolve(__dirname, "src/renderer/src"),
                "@": resolve(__dirname, "src/renderer/src"),
                "@assets": resolve(__dirname, "src/renderer/src/assets"),
                "@images": resolve(__dirname, "src/renderer/src/assets/images"),
                "@filters": resolve(__dirname, "src/renderer/src/filters"),
                "@components": resolve(
                    __dirname,
                    "src/renderer/src/components",
                ),
                "@mixins": resolve(__dirname, "src/renderer/src/mixins"),
                "@views": resolve(__dirname, "src/renderer/src/views"),
                "@store": resolve(__dirname, "src/renderer/src/store"),
                "@modules": resolve(__dirname, "sky_modules"),
                "@services": resolve(__dirname, "src/renderer/src/services"),
                "@utils": resolve(__dirname, "src/renderer/src/utils"),
                "@srcUtils": resolve(__dirname, "src/utils"),
                "@hooks": resolve(__dirname, "src/renderer/src/hooks"),
            },
        },
        css: {
            // css预处理器
            preprocessorOptions: {
                scss: {
                    charset: false,
                    additionalData: `@use "@assets/scss/color.scss" as *;`, // 每一个页面都会加载
                    // additionalData: '@import "./src/assets/scss/color.scss";', // 每一个页面都会加载
                    silenceDeprecations: ["legacy-js-api"],
                },
            },
        },
        plugins: [
            vue(),
            AutoImport({
                imports: ["vue", "vue-router"], //安装两行后你会发现在组件中不用再导入ref，reactive等
                eslintrc: {
                    enabled: true, // Default `false`
                    filepath: "./.eslintrc-auto-import.json", // Default `./.eslintrc-auto-import.json`
                    globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
                },
                dts: "src/renderer/src/auto-import.d.ts", //存放的位置
            }),
            Components({
                dirs: ["src/renderer/src/components/common"], // 引入组件的,包括自定义组件
                dts: "src/renderer/src/components.d.ts", // 存放的位置
            }),
        ],
        server: {
            proxy: {
                "/api/": {
                    target: "http://api.echowave.cn:8090/api/",
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, ""),
                },
            },
        },
    },
});
