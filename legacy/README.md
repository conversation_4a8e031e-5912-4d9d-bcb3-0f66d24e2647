# 回声涌现客户端
Electron/虚拟机内Agent服务

# 补充说明
## electron安装
中途会访问github，所以要代理全部流量
## electron打包(build:win)
同样要访问github，另外第一次打包需要管理员权限，否则会报 errorOut=ERROR: Cannot create symbolic link 的错误
## Agent打包(packageL1)
需要提前在环境安装好rollup，即npm install rollup -g
打包结果生成在./BuildL1
## 网络检测
http://api.echowave.cn:8090
## node版本
v22.13.0
## wsl msi安装包下载地址
wget -Uri https://github.com/microsoft/WSL/releases/download/2.4.13/wsl.2.4.13.0.x64.msi -O ./wsl.2.4.13.0.x64.msi
注：目前使用版本为wsl.2.4.13.0.x64.msi，放根目录下即可。如果以后有升级，请修改package.json中的extraFiles以及src/main/index.mjs中的config.wslVer