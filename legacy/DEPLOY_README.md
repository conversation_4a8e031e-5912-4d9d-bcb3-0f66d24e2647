# EchoWave Client 批量部署和自动接单指南

本指南介绍如何使用命令行批量部署EchoWave Client并设置自动接单功能。

## 功能概述

EchoWave Client包含以下自动化部署功能：

1. **系统托盘集成** - 应用在Windows系统托盘中显示而非任务栏
2. **静默安装** - 支持通过命令行无人值守安装
3. **自动登录** - 使用命令行参数提供的凭据自动登录
4. **自动接单** - 登录后自动开始接单流程
5. **批量部署** - 支持同时部署到多台计算机

## 单机安装和自动接单

### 使用批处理脚本

使用提供的`install-and-run.bat`脚本进行安装和自动接单：

```batch
install-and-run.bat 用户名 密码 安装程序路径
```

例如：

```batch
install-and-run.bat user123 pass123 "C:\Downloads\echowave-app-setup.exe"
```

脚本会执行以下操作：
1. 静默安装应用程序
2. 自动查找应用安装目录
3. 使用提供的用户名和密码启动应用
4. 自动登录并开始接单

### 手动命令行安装

1. 静默安装应用：

```batch
echowave-app-setup.exe /S
```

2. 启动应用并自动接单：

```batch
"安装目录\EchoWaveClient.exe" --username="user123" --password="pass123" --autoStart=true
```

## 批量部署

### 使用PowerShell脚本

1. 准备计算机列表文件`computers.txt`，每行一个计算机名称或IP地址。文件示例：
   ```
   localhost
   computer1
   *************
   ```

2. 运行PowerShell脚本进行批量部署：

```powershell
.\batch-deploy.ps1 -InstallerPath "C:\Downloads\echowave-app-setup.exe" -Username "user123" -Password "pass123" -ComputerListFile "computers.txt"
```

如果不指定计算机列表文件，将只在本地计算机上安装。

### 权限要求

- 执行批量部署需要管理员权限
- 远程部署需要目标计算机的管理员访问权限
- 确保Windows防火墙允许远程PowerShell执行

## 命令行参数说明

EchoWave Client支持以下命令行参数：

- `--username="用户名"`: 指定登录用户名
- `--password="密码"`: 指定登录密码
- `--autoStart=true`: 启动后自动开始接单

## 系统托盘功能

应用程序启动后会在Windows系统托盘区域显示图标，而不会出现在任务栏中。用户可以：

1. 点击托盘图标：切换应用窗口的显示/隐藏状态
2. 右键点击托盘图标：显示上下文菜单，提供以下选项：
   - 显示窗口：显示应用主窗口
   - 隐藏窗口：隐藏应用主窗口
   - 退出：完全关闭应用程序

当点击窗口关闭按钮时，应用不会退出，而是隐藏到托盘。只有通过托盘菜单的"退出"选项才能完全关闭应用。

## 自动化流程说明

完整的自动化流程包括：

1. **静默安装**：无需用户交互完成应用安装
2. **自动登录**：使用命令行参数中的用户名和密码自动填充登录表单
3. **自动接单**：登录成功后自动开始接单流程
4. **任务引擎启动**：如果任务引擎(WSL)未运行，会自动启动它

### 自动接单逻辑说明

为避免多次触发自动接单功能，应用实现了以下控制机制：

1. 在应用启动时，检查是否有`--autoStart=true`参数，如有则启动自动接单
2. 接单状态使用全局标志进行跟踪，避免重复触发
3. 当登录成功后，如有自动接单参数，且未已经启动接单，则自动开始接单
4. 停止接单后，标志位会被重置，允许再次启动接单

## 注意事项

1. 确保目标计算机满足EchoWave Client的系统要求
2. 批量部署前先在单台机器上测试安装和自动接单功能
3. 如果需要卸载应用，可以使用Windows控制面板或以下命令：

```batch
"C:\Program Files\EchoWaveClient\Uninstall EchoWaveClient.exe" /S
```

## 故障排除

如果遇到问题，请检查：

1. 应用日志文件
2. 确保WSL和任务引擎正确安装
3. 确保网络连接正常
4. 检查用户名和密码是否正确
5. 如果系统托盘图标未显示，请确认资源目录中存在icon.png文件