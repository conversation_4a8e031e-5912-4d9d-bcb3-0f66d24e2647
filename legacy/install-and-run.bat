@echo off
chcp 437 > nul
echo EchoWave Client Installation and Auto-Start Script
echo ==================================
@REM  .\install-and-run.bat 13011129301 123456 "C:\Users\<USER>\Desktop\dist\EchoWave Setup 0.4.0.exe"
REM Get parameters
set "username=%~1"
set "password=%~2"
set "installer_path=%~3"

REM Check parameters
if "%installer_path%"=="" (
  echo Usage: install-and-run.bat [username] [password] [installer_path]
  echo Example: install-and-run.bat user123 pass123 "C:\Downloads\echowave-app-setup.exe"
  exit /b 1
)

REM Check if installer exists
if not exist "%installer_path%" (
  echo Error: Installer %installer_path% does not exist
  exit /b 1
)

echo Installing EchoWave Client...
echo Installer path: %installer_path%
echo Username: %username%
echo Password: [HIDDEN]

REM Silent installation
echo Running installer silently...
start /wait "" "%installer_path%" /S

if %errorlevel% neq 0 (
  echo Error: Installation failed with error code %errorlevel%
  exit /b %errorlevel%
)

echo Installation complete
echo Waiting for installation to finalize...

REM Wait for installation to complete
timeout /t 5 /nobreak

REM Find the installation directory
set "INSTALL_DIR="
echo Searching for installation directory...

if exist "%ProgramFiles%\EchoWave\EchoWave.exe" (
  set "INSTALL_DIR=%ProgramFiles%\EchoWave"
  echo Found in Program Files
) else if exist "%ProgramFiles(x86)%\EchoWave\EchoWave.exe" (
  set "INSTALL_DIR=%ProgramFiles(x86)%\EchoWave"
  echo Found in Program Files (x86)
) else if exist "%LocalAppData%\Programs\EchoWave\EchoWave.exe" (
  set "INSTALL_DIR=%LocalAppData%\Programs\EchoWave"
  echo Found in Local AppData
)

if "%INSTALL_DIR%"=="" (
  echo Error: Cannot find EchoWaveClient installation directory
  echo Checking for possible locations...
  if exist "%ProgramFiles%" dir /b "%ProgramFiles%\E*"
  if exist "%ProgramFiles(x86)%" dir /b "%ProgramFiles(x86)%\E*"
  if exist "%LocalAppData%\Programs" dir /b "%LocalAppData%\Programs\E*"
  exit /b 1
)

REM Start application and auto-start task
echo Starting application with auto-login and auto-start option...
echo Installation directory: %INSTALL_DIR%
echo Command: "%INSTALL_DIR%\EchoWave.exe" --username="%username%" --password="[HIDDEN]" --autoStart=true

start "" "%INSTALL_DIR%\EchoWave.exe" --username="%username%" --password="%password%" --autoStart=true

echo Command executed. Application is starting...
echo.
echo IMPORTANT INFORMATION:
echo 1. The application will run in the system tray (notification area)
echo 2. Auto-login will attempt to fill the login form with your credentials
echo 3. After successful login, the app will monitor environment checks automatically
echo 4. Task acceptance will start automatically once all 6 environment checks pass
echo.
echo Waiting 30 seconds for the application to start up...

REM Wait for the application to start and begin environment checks
timeout /t 30 /nobreak

echo Done! The application is now running with intelligent auto-start.
echo.
echo What happens next:
echo - The app will automatically check 6 environment requirements
echo - Once all checks pass, task acceptance will start automatically
echo - You can monitor progress by checking the system tray icon
echo.
echo If you need to check the application:
echo - Look for the EchoWave icon in the system tray
echo - Right-click the icon and select "Show Window"
echo - The app will show environment check progress and auto-start when ready 