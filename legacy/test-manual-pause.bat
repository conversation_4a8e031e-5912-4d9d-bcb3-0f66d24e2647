@echo off
chcp 437 > nul
echo EchoWave Manual Pause Protection Test Script
echo ============================================

REM Test parameters
set "username=13011129301"
set "password=123456"
set "app_path=C:\Users\<USER>\AppData\Local\Programs\EchoWave\EchoWave.exe"

echo Testing manual pause protection functionality...
echo Username: %username%
echo Password: [HIDDEN]
echo App path: %app_path%
echo.

REM Check if app exists
if not exist "%app_path%" (
  echo Error: EchoWave application not found at %app_path%
  echo Please check the installation path.
  pause
  exit /b 1
)

echo Starting EchoWave with auto-start enabled...
echo.
echo Test Procedure:
echo 1. App will start and auto-login
echo 2. Environment checks will be monitored
echo 3. Auto-start will begin when checks pass
echo 4. MANUALLY CLICK "暂停接单" button in the app
echo 5. Verify that auto-start is disabled
echo 6. Check console logs for "User has manually paused" messages
echo.

start "" "%app_path%" --username="%username%" --password="%password%" --autoStart=true

echo Application started with auto-start enabled.
echo.
echo TESTING STEPS:
echo 1. Wait for the app to auto-start task acceptance
echo 2. Click the "暂停接单" (Pause Tasks) button manually
echo 3. Check console logs - you should see:
echo    "🚫 User manually paused task acceptance, auto-start disabled until app restart"
echo 4. Try to trigger auto-start again - it should be ignored
echo 5. Only restarting the app will re-enable auto-start
echo.
echo Expected Behavior:
echo - After manual pause, auto-start should be completely disabled
echo - Console should show "User has manually paused" messages
echo - Auto-start will only work again after app restart
echo.
echo Press any key to exit this script (app will continue running)...
pause > nul 