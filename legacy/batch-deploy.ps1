# EchoWave Client 批量部署脚本
param (
  [string]$ComputerListFile = "computers.txt",
  [string]$InstallerPath,
  [string]$Username,
  [string]$Password
)

# 检查参数
if (-not $InstallerPath) {
  Write-Host "错误: 请提供安装程序路径" -ForegroundColor Red
  Write-Host "用法: .\batch-deploy.ps1 -InstallerPath <安装程序路径> -Username <用户名> -Password <密码> [-ComputerListFile <计算机列表文件>]"
  Write-Host "示例: .\batch-deploy.ps1 -InstallerPath 'C:\Downloads\echowave-app-setup.exe' -Username 'user123' -Password 'pass123'"
  exit 1
}

if (-not (Test-Path $InstallerPath)) {
  Write-Host "错误: 安装程序 $InstallerPath 不存在" -ForegroundColor Red
  exit 1
}

# 如果计算机列表文件不存在，则使用本地计算机
if (-not (Test-Path $ComputerListFile)) {
  Write-Host "警告: 计算机列表文件 $ComputerListFile 不存在，将只在本地计算机上安装" -ForegroundColor Yellow
  $computers = @("localhost")
} else {
  $computers = Get-Content $ComputerListFile
}

Write-Host "开始批量部署 EchoWave Client..." -ForegroundColor Green
Write-Host "安装程序路径: $InstallerPath" -ForegroundColor Cyan
Write-Host "用户名: $Username" -ForegroundColor Cyan
Write-Host "计算机数量: $($computers.Count)" -ForegroundColor Cyan

foreach ($computer in $computers) {
  Write-Host "`n正在处理计算机: $computer" -ForegroundColor Yellow
  
  if ($computer -eq "localhost" -or $computer -eq $env:COMPUTERNAME) {
    # 本地安装
    Write-Host "在本地计算机上安装..." -ForegroundColor Cyan
    
    # 静默安装
    Start-Process -FilePath $InstallerPath -ArgumentList "/S" -Wait
    
    # 查找安装目录
    $installPaths = @(
      "C:\Program Files\EchoWaveClient\EchoWaveClient.exe",
      "C:\Program Files (x86)\EchoWaveClient\EchoWaveClient.exe",
      "$env:LOCALAPPDATA\Programs\EchoWaveClient\EchoWaveClient.exe"
    )
    
    $exePath = $null
    foreach ($path in $installPaths) {
      if (Test-Path $path) {
        $exePath = $path
        break
      }
    }
    
    if (-not $exePath) {
      Write-Host "  错误: 无法找到安装的可执行文件" -ForegroundColor Red
      return
    }
    
    # 启动应用并自动接单
    Write-Host "  启动应用: $exePath" -ForegroundColor Cyan
    Start-Process -FilePath $exePath -ArgumentList "--username=`"$Username`"", "--password=`"$Password`"", "--autoStart=true"
  } else {
    # 远程安装
    Write-Host "在远程计算机 $computer 上安装..." -ForegroundColor Cyan
    
    try {
      # 测试连接
      if (-not (Test-Connection -ComputerName $computer -Count 1 -Quiet)) {
        Write-Host "  无法连接到计算机 $computer，跳过" -ForegroundColor Red
        continue
      }
      
      # 创建远程临时目录
      $remoteTempDir = "\\$computer\C$\Temp\EchoWaveInstall"
      if (-not (Test-Path $remoteTempDir)) {
        New-Item -Path $remoteTempDir -ItemType Directory -Force | Out-Null
      }
      
      # 复制安装程序到远程计算机
      Copy-Item -Path $InstallerPath -Destination "$remoteTempDir\echowave-app-setup.exe" -Force
      
      # 创建远程安装脚本
      $scriptContent = @"
@echo off
chcp 437 > nul
echo Installing EchoWave Client...
start /wait "" "$remoteTempDir\echowave-app-setup.exe" /S
echo Installation complete
timeout /t 5 /nobreak

REM Find the installation directory
set "INSTALL_DIR="
if exist "%ProgramFiles%\EchoWaveClient\EchoWaveClient.exe" (
  set "INSTALL_DIR=%ProgramFiles%\EchoWaveClient"
) else if exist "%ProgramFiles(x86)%\EchoWaveClient\EchoWaveClient.exe" (
  set "INSTALL_DIR=%ProgramFiles(x86)%\EchoWaveClient"
) else if exist "%LocalAppData%\Programs\EchoWaveClient\EchoWaveClient.exe" (
  set "INSTALL_DIR=%LocalAppData%\Programs\EchoWaveClient"
)

if "%INSTALL_DIR%"=="" (
  echo Error: Cannot find EchoWaveClient installation directory
  exit /b 1
)

echo Starting application with auto-start option...
echo Installation directory: %INSTALL_DIR%
start "" "%INSTALL_DIR%\EchoWaveClient.exe" --username="$Username" --password="$Password" --autoStart=true
echo Application started
"@
      
      Set-Content -Path "$remoteTempDir\install.bat" -Value $scriptContent
      
      # 执行远程安装脚本
      Invoke-Command -ComputerName $computer -ScriptBlock {
        Start-Process -FilePath "C:\Temp\EchoWaveInstall\install.bat" -WorkingDirectory "C:\Temp\EchoWaveInstall"
      }
      
      Write-Host "  安装命令已发送到 $computer" -ForegroundColor Green
    } catch {
      Write-Host "  在 $computer 上安装失败: $_" -ForegroundColor Red
    }
  }
}

Write-Host "`n批量部署完成!" -ForegroundColor Green 