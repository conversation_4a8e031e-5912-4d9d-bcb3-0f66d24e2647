# EchoWave 自动接单功能

## 功能概述

EchoWave客户端现在支持自动接单功能，它会自动监控6项环境检查的状态，一旦所有检查都通过就立即开始接单，无需固定等待时间。

## 主要特性

### 环境监控
- 实时监控6项环境检查状态
- 每5秒检查一次环境状态
- 自动重新触发环境检查（每5次检查触发一次）
- 最多尝试60次（总共5分钟）

### ⚡ 快速响应
- 移除了之前安装bat脚本固定的2分钟等待时间
- 环境检查通过后立即开始接单
- 支持网络状态检查
- 避免重复启动接单

### 🛡️ 用户控制优先
- 用户手动暂停接单后，自动接单功能将被禁用
- 只有重启应用程序才能重新启用自动接单
- 保证用户始终拥有最终控制权

### 🔄 多种启动方式
1. **命令行启动**：使用 `--autoStart=true` 参数
2. **开机自启动**：开机后自动检测并启动
3. **安装后启动**：安装完成后自动启动

## 环境检查项目

系统会监控以下6项环境检查：

1. **操作系统检查** (platformCheckResult)
2. **操作系统版本检查** (osCheckResult) 
3. **PowerShell检查** (powershellCheckResult)
4. **虚拟化检查** (virtualCheckResult)
5. **WSL2检查** (wslCheckResult)
6. **镜像引擎检查** (mirrorCheckResult)

## 使用方法

### 1. 命令行启动
```bash
EchoWave.exe --username="your_username" --password="your_password" --autoStart=true
```

### 2. 批处理脚本安装
```bash
install-and-run.bat username password "path\to\installer.exe"
```

### 3. 开机自启动
- 在托盘菜单中启用"开机自启动"
- 开机后应用会自动启动并监控环境检查

## 工作流程

```mermaid
graph TD
    A[应用启动] --> B[检查登录状态]
    B --> C{已登录?}
    C -->|是| D[检查用户是否手动暂停过]
    C -->|否| E[尝试自动登录]
    E --> F{登录成功?}
    F -->|是| D
    F -->|否| G[等待手动登录]
    G --> D
    D --> H{用户手动暂停过?}
    H -->|是| I[禁用自动接单]
    H -->|否| J[启动智能监听器]
    J --> K[每5秒检查环境状态]
    K --> L{6项检查都通过?}
    L -->|是| M[立即开始接单]
    L -->|否| N{达到最大尝试次数?}
    N -->|是| O[停止监听]
    N -->|否| P[继续监听]
    P --> K
    M --> Q[接单成功]
    Q --> R{用户点击暂停?}
    R -->|是| S[设置手动暂停标志]
    R -->|否| Q
    S --> T[停止所有自动接单]
```

## 日志输出

智能监听器会输出详细的日志信息：

```
🚀 Starting intelligent auto-start watcher...
🔍 Environment check attempt 1/60
📊 Current environment status: 4/6 checks passed
🎯 Task button status: accept
🌐 Network status: Connected
⏳ Environment checks not ready: 4/6 passed, waiting...
📋 Detailed environment status:
  - platformCheckResult: ✅
  - osCheckResult: ✅
  - powershellCheckResult: ✅
  - virtualCheckResult: ❌
  - wslCheckResult: ❌
  - mirrorCheckResult: ✅
🔄 Triggering environment re-check...
```

## 配置参数

### 监听器配置
- **检查间隔**：5秒
- **最大尝试次数**：60次（总共5分钟）
- **重新检查频率**：每5次检查触发一次环境重新检查

### 超时设置
- **应用启动等待**：15秒
- **登录后等待**：10秒
- **前端就绪等待**：3秒

## 故障排除

### 常见问题

1. **环境检查一直不通过**
   - 检查WSL2是否正确安装
   - 确认BIOS虚拟化已开启
   - 验证任务引擎是否正确安装

2. **自动登录失败**
   - 检查用户名和密码是否正确
   - 确认网络连接正常
   - 查看控制台日志获取详细错误信息

3. **接单按钮找不到**
   - 确认已成功登录到主页面
   - 检查页面是否完全加载
   - 验证所有环境检查是否通过

4. **自动接单不工作**
   - 检查是否曾经手动点击过"暂停接单"
   - 如果手动暂停过，需要重启应用程序
   - 查看控制台是否有"User has manually paused"的日志

### 调试方法

1. **查看控制台日志**
   - 开发环境会自动打开DevTools
   - 生产环境可通过托盘菜单查看

2. **检查托盘状态**
   - 右键点击托盘图标
   - 选择"显示窗口"查看当前状态

3. **手动测试**
   - 使用 `test-intelligent-autostart.bat` 脚本测试

## 更新说明

### v2.0 智能监听器版本
- ✅ 移除固定2分钟等待时间
- ✅ 添加智能环境检查监听器
- ✅ 支持实时状态监控
- ✅ 优化开机自启动检测
- ✅ 改进错误处理和重试机制
- ✅ 增强日志输出和调试信息
- ✅ 添加用户手动暂停保护机制

### 与v1.0的区别
| 功能 | v1.0 | v2.0 |
|------|------|------|
| 等待时间 | 固定2分钟 | 智能监听，即时响应 |
| 环境检查 | 一次性检查 | 持续监控 |
| 重试机制 | 简单重试 | 智能重试和重新检查 |
| 日志输出 | 基础日志 | 详细状态日志 |
| 错误处理 | 基础处理 | 完善的错误恢复 |
| 用户控制 | 无保护机制 | 手动暂停后禁用自动接单 |

## 技术实现

### 前端监听器
- 使用 `setInterval` 每5秒检查环境状态
- 监控 `checkTrueNum` 和 `netStatus` 变量
- 自动清理监听器避免内存泄漏

### 主进程优化
- 移除复杂的按钮点击逻辑
- 简化自动接单触发机制
- 改进开机自启动检测

### 状态管理
- 防止重复启动监听器
- 正确处理组件卸载
- 智能重置和恢复机制 