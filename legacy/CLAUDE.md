# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

EchoWave Windows Client 是一个基于 Electron 的桌面应用程序，用于连接和管理虚拟机内的 Agent 服务。项目采用中文作为主要开发语言，支持多环境部署和自动更新机制。

## 技术栈

- **主框架**: Electron (v35.2.1) + Vue 3.5.13 + TypeScript
- **UI 组件**: Element Plus (v2.9.9)
- **构建工具**: electron-vite + Vite 6.2.6
- **打包工具**: electron-builder (v25.1.8)
- **代码格式化**: Prettier (v3.5.3)
- **后端模块**: Node.js ESM 模块，支持 WSL 2 集成
- **网络服务**: 内置 HTTP 服务器 + Axios 请求拦截

## 开发命令

```bash
# 开发模式
npm run dev

# 构建应用
npm run build

# 格式化代码
npm run prettier

# 测试
npm run test

# 打包命令
npm run build:unpack          # 打包不解压
npm run build:win             # 打包 Windows 版本
npm run build:win:dev         # 开发环境打包
npm run build:win:test        # 测试环境打包  
npm run build:win:prod        # 生产环境打包

# 后端模块打包（需要全局安装 rollup）
npm run packageL1Prod         # 生产版本
npm run packageL1Bench        # 基准测试版本

# WSL MSI 包下载
npm run getMSI:WSL
```

## 多环境配置

项目支持 3 种环境配置，通过 `APP_ENV` 环境变量控制：

### 开发环境 (development/dev/debug)
- 更新服务器: `http://************:8000/releases`
- API 地址: `http://api.echowave.cn:8090/api/`
- 门户网站: `http://localhost:3000/supplierRevenue`
- 客户端名称: "EchoWave 客户端(开发版)"

### 测试环境 (testing/test)
- 更新服务器: `http://************/releases`
- API 地址: `http://api.echowave.cn:8090/api/`
- 门户网站: `http://api.echowave.cn:8090/supplierRevenue`
- 客户端名称: "EchoWave 客户端(测试版)"

### 生产环境 (production/prod/release)
- 更新服务器: `http://cdn.echowave.cn/releases`
- API 地址: `http://api.echowave.cn:8090/api/`
- 门户网站: `http://api.echowave.cn:8090/supplierRevenue`
- 客户端名称: "EchoWave 客户端"

## 项目架构

### 核心模块分层

1. **主进程** (`src/main/index.mjs`)
   - 应用生命周期管理和单实例控制
   - 多环境配置加载 (`config` 对象)
   - 自动更新机制集成 (electron-updater)
   - 系统托盘和窗口管理
   - WSL 配置优化 (`limitedWSLConfig`)
   - 用户凭据和设置持久化

2. **渲染进程** (`src/renderer/`)
   - Vue 3 + TypeScript + Composition API
   - 自动更新 UI 组件 (`auto-update.vue`)
   - Hooks 模式状态管理 (`useCheckUpdate.ts`)
   - 路由和组件管理

3. **BackL0** - 底层 API 和操作模块
   - 系统检测和 WSL 管理
   - 镜像操作和节点状态监控
   - IPC 通信接口 (`InitAPI.mjs`)

4. **externals/vir-detect** - 原生代码模块
   - 虚拟化检测
   - WSL 检测
   - 机器 ID 生成

5. **BackL1** - HTTP 服务层
   - 内置 HTTP 服务器 (端口 11121)
   - RESTful API 接口

6. **Utils** - 工具模块
   - WSL 配置管理 (`limitedWSLConfig.mjs`)
   - 日志系统和加密支持

## 自动更新架构

### 主进程更新管理
- **更新服务器**: 支持多环境配置
- **自动检查**: 每 2 分钟检查一次更新
- **智能更新**: 空闲状态自动安装，繁忙状态延迟
- **命令行控制**: `--autoUpdate=true/false`

### 前端更新 UI
- **组件**: `auto-update.vue` 提供完整更新流程界面
- **状态管理**: `useCheckUpdate.ts` Hook 管理更新状态
- **进度显示**: 下载进度条和状态反馈
- **用户控制**: 立即升级、延迟更新、取消下载

### 更新流程
1. 后台定时检查更新
2. 发现更新后通知前端显示 UI
3. 用户确认后开始下载
4. 下载完成后根据系统状态决定是否自动重启

## WSL 集成特性

### 智能配置优化
- **自动配置**: 根据系统 CPU 和内存自动优化 WSL 配置
- **资源分配**: CPU 占用 70%，内存占用 80%，8GB 交换空间
- **配置备份**: 自动备份用户原有配置，应用退出时恢复

### 节点状态监控
- **空闲检测**: `CheckMirrorNodeIsIdle` 监控节点任务状态
- **任务统计**: 区分 running 和 pending 任务数量
- **更新时机**: 仅在节点空闲时执行自动更新

## 开发环境要求

- Node.js v22.13.0
- 全局安装 rollup: `npm install rollup -g`
- Windows 开发环境（支持 WSL 2）
- 网络代理配置（访问 GitHub 下载依赖）

## 调试和开发工具

### DevTools 快捷键
- **开发环境**: 自动启用
- **生产环境**: `Ctrl+Shift+I` (Windows) / `Alt+Command+I` (macOS)

### 日志系统
- **多级日志**: 支持不同日志级别和加密
- **文件输出**: 日志保存在 `{userData}/log` 目录
- **实时监控**: 应用状态和操作日志

## 用户数据管理

### 凭据存储
- **位置**: `{userData}/user-credentials.json`
- **内容**: 手机号、token、自动登录设置
- **安全**: 30 天自动过期机制

### 用户设置
- **位置**: `{userData}/user-settings.json`
- **配置**: 自动接单等用户偏好设置
- **同步**: 前后端设置实时同步

## 特殊功能

### 单实例控制
- 防止多个应用实例同时运行
- 第二个实例启动时激活主窗口

### 系统集成
- **托盘菜单**: 显示/隐藏窗口、检查更新、退出应用
- **开机自启**: 支持开机自启动配置（代码中已实现但 UI 中未启用）
- **空闲监控**: 30 秒无活动自动隐藏窗口

### 智能任务管理
- **自动接单**: 基于环境检查状态的智能监听器
- **命令行参数**: 支持用户名、密码自动登录
- **状态同步**: 前后端任务状态实时同步