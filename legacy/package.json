{"name": "echowave_client", "version": "0.5.16", "description": "回声涌现客户端", "main": "./out/main/index.js", "scripts": {"start": "electron-vite preview", "dev": "electron-vite dev", "dev:cross-platform": "node dev-start.mjs", "dev:mac": "MOCK_MODE=1 npm run dev", "dev:linux": "MOCK_MODE=1 npm run dev", "build": "electron-vite build", "test": "electron ./test.mjs", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:win:test": "set APP_ENV=test && npm run build && electron-builder --win", "build:win:prod": "set APP_ENV=prod && npm run build && electron-builder --win", "build:win:dev": "set APP_ENV=dev && npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "packageL1Bench": "rollup -c --environment BUILD:bench", "packageL1Prod": "rollup -c --environment BUILD:production,MINIFY:true", "getMSI:WSL": "wget -<PERSON><PERSON> https://github.com/microsoft/WSL/releases/download/2.4.13/wsl.2.4.13.0.x64.msi -O ./wsl.2.4.13.0.x64.msi && cmd /c copy .\\wsl.2.4.13.0.x64.msi .\\node_modules\\electron\\dist\\", "prettier": "prettier --write ./*"}, "author": "成都回声涌现智能科技有限公司", "license": "Commercial", "build": {"appId": "com.echowave.client", "productName": "EchoWave", "directories": {"output": "dist"}, "artifactName": "${productName}-Setup-${version}.${ext}", "files": ["**/*", "!dist", "!EchoWaveUbuntu", "!tmp"], "win": {"target": "nsis", "icon": "resources/icon.png"}, "nsis": {"perMachine": true, "allowElevation": true, "oneClick": false, "allowToChangeInstallationDirectory": true, "include": "installer.nsh"}, "extraFiles": []}, "devDependencies": {"@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-terser": "^0.4.4", "@vitejs/plugin-vue": "^5.2.3", "electron": "^35.2.1", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "prettier": "^3.5.3", "sass-embedded": "^1.87.0", "vite": "^6.2.6", "vue": "^3.5.13"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "axios": "^1.8.4", "electron-squirrel-startup": "^1.0.1", "electron-updater": "^6.3.9", "element-plus": "^2.9.9", "iconv-lite": "^0.6.3", "js-cookie": "^3.0.5", "sudo-prompt": "^9.2.1", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vue-router": "^4.5.1", "vue3-seamless-scroll": "^3.0.2"}}