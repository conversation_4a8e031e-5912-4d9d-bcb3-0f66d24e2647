'use strict';
import {route} from "./scope.mjs";
import fs from 'fs';
import path from 'path';

const f = (c, L) => {
    const logger = L.Neo("SERVER.DeleteFile");
    return async (e, filePath) => {
        logger.Info(`尝试删除文件: ${filePath}`);
        
        try {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                logger.Warn(`文件不存在: ${filePath}`);
                return { success: false, message: '文件不存在' };
            }
            
            // 删除文件
            fs.unlinkSync(filePath);
            logger.Info(`文件删除成功: ${filePath}`);
            return { success: true, message: '文件删除成功' };
        } catch (error) {
            logger.Error(`删除文件失败: ${error.message}`);
            return { success: false, message: error.message };
        }
    };
};

route.set("delete_file", f);

export {f as delete_file};
export default f; 