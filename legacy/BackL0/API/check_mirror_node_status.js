'use strict';
import {route} from "./scope.mjs";
import {CheckMirrorNodeIsIdle} from "../Operations/CheckMirrorNodeStatus.mjs";

const f = (c, L) => {
    const logger = <PERSON>.Neo("SERVER.CheckMirrorNodeIsIdle");
    return (e) => {
        logger.Info("检查节点是否空闲");
        return CheckMirrorNodeIsIdle(undefined, c.distroName, logger);
    };
};

route.set("check_mirror_node_is_idle", f);

export {f as check_mirror_node_is_idle};
export default f;