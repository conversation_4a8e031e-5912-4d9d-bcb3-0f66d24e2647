'use strict';
import {route} from "./scope.mjs";

// 测试回调
const f = (c, L) => {
    const logger = L.Neo("SERVER.PingEx");
    return (e, v, f) => {
        logger.Info("PingEx");
        return new Promise((resolve, reject) => {
            let o = setInterval(() => {
                f(Math.random() + "v");
            }, 1000);
            setTimeout(() => {
                clearInterval(o);
                resolve("Pong:" + v);
            }, 5000);
        });
    };
};

route.set("ping_ex", f);

export {f as ping_ex};
export default f;