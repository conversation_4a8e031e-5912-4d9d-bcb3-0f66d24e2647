'use strict';
import {route} from "./scope.mjs";
import fs from 'fs';
import path from 'path';
import {app} from 'electron';

const f = (c, L) => {
    const logger = L.Neo("SERVER.SaveMirrorVersion");
    return (e, versionData) => {
        logger.Info("保存镜像版本信息");
        
        try {
            // 获取用户数据目录
            const userDataPath = app.getPath('userData');
            const versionFilePath = path.join(userDataPath, 'mirror-version.json');
            
            // 确保目录存在
            if (!fs.existsSync(userDataPath)) {
                fs.mkdirSync(userDataPath, { recursive: true });
            }
            
            // 保存版本数据
            fs.writeFileSync(versionFilePath, JSON.stringify(versionData, null, 2), 'utf8');
            
            logger.Info(`镜像版本信息已保存: ${versionData.current_version}`);
            return true;
        } catch (error) {
            logger.Error(`保存镜像版本信息失败: ${error.message}`);
            return false;
        }
    };
};

route.set("save_mirror_version", f);

export {f};
export default f; 