"use strict";
import { route } from "./scope.mjs";
import {
    MirrorNodeID,
    AbortQueryMirrorNodeId,
} from "../Operations/MirrorNodeID.mjs";

const f = (c, L) => {
    const logger = <PERSON>.Neo("SERVER.MirrorNodeID");
    return (e) => {
        logger.Info("查询镜像节点ID");
        return MirrorNodeID(logger, c.distroName);
        // return new Promise((resolve, reject) => {
        //     resolve("b42aab99-9a55-f86d-9c05-a484f17b801f");
        // });
    };
};
const f2 = (c, L) => {
    const logger = <PERSON>.Neo("SERVER.MirrorNodeID");
    return (e, reason) => {
        logger.Info("终止查询镜像节点ID");
        return AbortQueryMirrorNodeId(reason);
    };
};

route.set("mirror_node_id", f);
route.set("abort_mirror_node_id", f2);

export { f as mirror_node_id };
export default f;
