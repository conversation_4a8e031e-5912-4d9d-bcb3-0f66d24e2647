"use strict";
import { route } from "./scope.mjs";
import { InstallMirror } from "../Operations/InstallMirror.mjs";

const f = (c, L, g) => {
  const logger = <PERSON><PERSON>Neo("SERVER.InstallMirror");
  const statHandler = g.mirrorInstallStat;
  return (e, url, md5) => {
    logger.Info("安装镜像");
    return InstallMirror(
      logger,
      url,
      c.dirname,
      c.userdataDir,
      md5,
      c.distroName,
      statHandler,
    );
  };
};

const fstat = (c, L, g) => {
  const logger = L.Neo("SERVER.InstallMirrorStat");
  const statHandler = g.mirrorInstallStat;
  return (e) => {
    // logger.Info("安装镜像状态查询");
    return statHandler.Progress();
  };
};

const fcancel = (c, L, g) => {
  const logger = <PERSON><PERSON>Neo("SERVER.InstallMirrorCancel");
  const statHandler = g.mirrorInstallStat;
  return (e) => {
    logger.Info("安装镜像取消");
    return statHandler.Cancel();
  };
};

route.set("install_mirror", f);
route.set("install_mirror_stat", fstat);
route.set("install_mirror_cancel", fcancel);

export {
  f as install_mirror,
  fstat as install_mirror_stat,
  fcancel as install_mirror_cancel,
};
export default f;
