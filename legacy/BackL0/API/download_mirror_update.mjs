'use strict';
import {route} from "./scope.mjs";
import fs from 'fs';
import path from 'path';
import {app} from 'electron';
import axios from 'axios';
import http from 'http';
import https from 'https';

let downloadProgress = 0;
let isDownloading = false;

const f = (c, L) => {
    const logger = L.Neo("SERVER.DownloadMirrorUpdate");
    return async (e, updateInfo) => {
        logger.Info("开始下载镜像更新");
        logger.Info(`更新信息: ${JSON.stringify(updateInfo)}`);
        
        if (isDownloading) {
            logger.Info("已有下载任务在进行中");
            return { success: false, message: "下载任务已在进行中" };
        }
        
        try {
            isDownloading = true;
            downloadProgress = 0;
            
            // 使用 Electron 的 app.getPath('userData') 获取用户数据目录
            const targetDir = app.getPath('userData');
            const targetFile = path.join(targetDir, 'mock-mirror-v1.1.tar');
            
            logger.Info(`目标目录: ${targetDir}`);
            logger.Info(`目标文件: ${targetFile}`);
            
            // 确保目标目录存在
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
                logger.Info("创建目标目录");
            }
            
            // 如果目标文件已存在，先删除
            if (fs.existsSync(targetFile)) {
                logger.Info(`删除已存在的目标文件: ${targetFile}`);
                fs.unlinkSync(targetFile);
            }
            
            // 下载文件
            const downloadUrl = updateInfo.downloadUrl || 'http://cdn.echowave.cn/releases/image.tar';
            // const downloadUrl = updateInfo.downloadUrl || 'http://192.168.50.10/core/image.tar';
            logger.Info(`从 ${downloadUrl} 下载镜像更新`);
            
            // 解析URL判断使用HTTP还是HTTPS
            const urlObj = new URL(downloadUrl);
            const isHttps = urlObj.protocol === 'https:';
            logger.Info(`使用 ${isHttps ? 'HTTPS' : 'HTTP'} 协议下载`);
            
            const writer = fs.createWriteStream(targetFile);
            
            // 配置axios
            const axiosConfig = {
                method: 'GET',
                url: downloadUrl,
                responseType: 'stream',
                onDownloadProgress: (progressEvent) => {
                    if (progressEvent.total) {
                        downloadProgress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                        logger.Info(`下载进度: ${downloadProgress}%`);
                    }
                }
            };
            
            // 根据协议添加对应的agent
            if (isHttps) {
                axiosConfig.httpsAgent = new https.Agent({
                    rejectUnauthorized: false // 忽略 SSL 证书验证（开发环境）
                });
            } else {
                axiosConfig.httpAgent = new http.Agent();
            }
            
            logger.Info("开始执行下载请求...");
            const response = await axios(axiosConfig);
            
            // 获取文件大小
            const totalLength = response.headers['content-length'];
            logger.Info(`文件大小: ${totalLength ? (totalLength / 1024 / 1024).toFixed(2) + ' MB' : '未知'}`);
            
            response.data.pipe(writer);
            
            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    isDownloading = false;
                    downloadProgress = 100;
                    logger.Info(`镜像更新下载完成，文件保存在: ${targetFile}`);
                    
                    // 验证文件是否真的存在
                    if (fs.existsSync(targetFile)) {
                        const stats = fs.statSync(targetFile);
                        logger.Info(`下载文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
                    }
                    
                    resolve({ 
                        success: true, 
                        message: "下载完成",
                        filePath: targetFile
                    });
                });
                
                writer.on('error', (error) => {
                    logger.Error(`写入文件时出错: ${error.message}`);
                    isDownloading = false;
                    downloadProgress = 0;
                    if (fs.existsSync(targetFile)) {
                        fs.unlinkSync(targetFile);
                    }
                    reject(error);
                });
                
                response.data.on('error', (error) => {
                    logger.Error(`下载流出错: ${error.message}`);
                    isDownloading = false;
                    downloadProgress = 0;
                    writer.close();
                    if (fs.existsSync(targetFile)) {
                        fs.unlinkSync(targetFile);
                    }
                    reject(error);
                });
            });
            
        } catch (error) {
            logger.Error(`下载镜像更新失败: ${error.message}`);
            logger.Error(`错误堆栈: ${error.stack}`);
            
            // 特别处理SSL错误
            if (error.message.includes('WRONG_VERSION_NUMBER')) {
                logger.Error('SSL版本错误，可能是使用了HTTPS访问HTTP服务，请检查URL协议');
            }
            
            isDownloading = false;
            downloadProgress = 0;
            return { 
                success: false, 
                message: error.message 
            };
        }
    };
};

// 获取下载进度
const getProgressAPI = (c, L) => {
    const logger = L.Neo("SERVER.GetDownloadProgress");
    return (e) => {
        return {
            isDownloading: isDownloading,
            progress: downloadProgress
        };
    };
};

route.set("download_mirror_update", f);
route.set("get_download_progress", getProgressAPI);

export {f, getProgressAPI};
export default f; 