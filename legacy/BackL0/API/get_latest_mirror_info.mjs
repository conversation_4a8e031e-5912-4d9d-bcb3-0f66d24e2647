"use strict";
import { route } from "./scope.mjs";
import { GetLatestMirrorInfo } from "../Operations/GetLatestMirrorInfo.mjs";

const f = (c, L) => {
  const logger = <PERSON>.Neo("SERVER.GetLatestMirrorInfo");
  return async (e) => {
    logger.Info("获取最新镜像信息");

    const value = await GetLatestMirrorInfo(c, logger);
    return {
      url: value.image_url,
      hash: value.image_hash,
      version: value.image_version,
      release_note: value.image_release_note,
    };
  };
};

route.set("get_latest_mirror_info", f);

export { f };
export default f;
