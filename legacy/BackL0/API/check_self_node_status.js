'use strict';
import {route} from "./scope.mjs";
import { CheckSelfNodeStatus } from "../Operations/CheckSelfNodeStatus.mjs";
import { GetUID } from '../Operations/GetUID.mjs'
import { MergeAsyncCalls } from '../../Utils/MergeAsyncCall.mjs'
import { getApiUrl, API_PATHS } from './config.mjs';

const f = (c, L) => {
    const logger = L.Neo("SERVER.CheckSelfNodeStatus");
    const CheckObj = new CheckSelfNodeStatus(logger,
        getApiUrl(c, API_PATHS.nodeGet),
        () => c.credentials?.token,
         () => GetUID(logger)
        );
    CheckObj.subscribe(v => {
        c.mainWindow?.webContents.send("self-node-status-change", v);
    })
    return MergeAsyncCalls(async () => {
        try {
            await CheckObj.immediateCheck()
            return CheckObj.current()
        } catch(e) {
            logger.Error(`检查自身节点状态异常` + e);
            throw e
        }
    });
};

route.set("check_self_node_status", f);

export {f as check_self_node_status};
export default f;