"use strict";
import { route } from "./scope.mjs";
import CheckNet from "../Operations/CheckNet.mjs";
import { getApiUrl, API_PATHS } from "./config.mjs";

const f = (c, L) => {
    const logger = L.Neo("SERVER.CheckNet");
    const CheckNetObj = new CheckNet(logger);
    let lastStat = 0;
    return async (e) => {
        try {
            const stat = CheckNetObj.stat;
            if (lastStat >= 0 && stat < 0) logger.Error(`客户端网络异常`);
            lastStat = stat;
            // logger.Info(`客户端网络延时: ${stat}`, 5);
            return stat;
        } catch (e) {
            logger.Error(`客户端网络异常` + e);
            throw e;
        }
    };
};

route.set("check_net", f);

export { f as check_net };
export default f;
