'use strict';
import {route} from "./scope.mjs";

const f = (c, L, g) => {
    const logger = <PERSON>.<PERSON>("SERVER.GetLog");
    const logRecord = g.logRecord;
    return (e) => {
        // logger.Info("获取任务日志");
        let r = [];
        for(let i = logRecord.length ; i --;) r.unshift(logRecord[i]);
        logRecord.splice(0, logRecord.length);
        return r;
    };
};

route.set("get_log", f);

export {f as get_log};
export default f;