"use strict";
import { route } from "./scope.mjs";
import { RunMirror, AbortRunMirror } from "../Operations/RunMirror.mjs";

const f = (c, L, g) => {
    const logger = <PERSON><PERSON>("SERVER.RunMirror");
    return (e) => {
        logger.Info("启动镜像");
        return RunMirror(logger, c.distroName, g);
    };
};
const f2 = (c, L, g) => {
    const logger = <PERSON><PERSON>("SERVER.RunMirror");
    return (e, reason) => {
        logger.Info("终止启动镜像");
        return AbortRunMirror(g, reason);
    };
};

route.set("run_mirror", f);
route.set("abort_run_mirror", f2);

export { f as run_mirror };
export default f;
