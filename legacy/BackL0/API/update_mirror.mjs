'use strict';
import {route} from "./scope.mjs";
import {exec} from 'child_process';
import {promisify} from 'util';
import fs from 'fs';
import path from 'path';
import {app} from 'electron';
import dayjs from 'dayjs';

const execAsync = promisify(exec);

// 检查特定名称的WSL实例是否存在
async function checkWSLInstance(instanceName, logger) {
    try {
        const { stdout } = await execAsync('wsl --list --verbose');
        logger.Info(`检查WSL实例列表:\n${stdout}`);
        
        // 检查输出中是否包含指定的实例名称
        const instanceExists = stdout.includes(instanceName);
        logger.Info(`WSL实例 ${instanceName} ${instanceExists ? '存在' : '不存在'}`);
        
        return instanceExists;
    } catch (error) {
        logger.Error(`检查WSL实例时出错: ${error.message}`);
        return false;
    }
}

const f = (c, L) => {
    const logger = <PERSON>.Neo("SERVER.UpdateMirror");
    return async (e, updateFilePath) => {
        logger.Info("=== 镜像更新API被调用 ===");
        logger.Info("开始执行镜像更新流程");
        logger.Info(`更新文件路径: ${updateFilePath}`);
        
        try {
            // 验证更新文件是否存在
            if (!fs.existsSync(updateFilePath)) {
                const errorMsg = `更新文件不存在: ${updateFilePath}`;
                logger.Error(errorMsg);
                return { 
                    success: false, 
                    message: errorMsg
                };
            }
            
            const fileStats = fs.statSync(updateFilePath);
            logger.Info(`更新文件大小: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);
            
            // 步骤1: 停止当前镜像
            logger.Info("=== 步骤1: 停止当前镜像 ===");
            const stopResult = await stopCurrentMirror(logger);
            logger.Info(`=== 步骤1完成: ${stopResult.message} ===`);
            
            // 步骤2: 卸载当前镜像
            logger.Info("=== 步骤2: 卸载当前镜像 ===");
            const uninstallResult = await uninstallCurrentMirror(logger);
            logger.Info(`=== 步骤2完成: ${uninstallResult.message} ===`);
            
            // 步骤3: 导入新镜像
            logger.Info("=== 步骤3: 导入新镜像 ===");
            const importResult = await importNewMirror(updateFilePath, logger, c);
            logger.Info(`=== 步骤3完成: ${importResult.message} ===`);
            
            logger.Info("=== 镜像更新流程完成 ===");
            return { 
                success: true, 
                message: "镜像更新成功"
            };
            
        } catch (error) {
            logger.Error(`镜像更新失败: ${error.message}`);
            logger.Error(`错误堆栈: ${error.stack}`);
            return { 
                success: false, 
                message: error.message
            };
        }
    };
};

// 停止当前镜像
function stopCurrentMirror(logger) {
    return new Promise((resolve) => {
        try {
            logger.Info("正在停止WSL实例...");
            
            // 首先检查当前WSL状态
            logger.Info("检查停止前WSL状态...");
            exec('powershell -Command "wsl --list --verbose"', { encoding: 'utf8' }, (checkError, checkStdout) => {
                if (checkStdout) {
                    logger.Info(`停止前WSL实例状态:\n${checkStdout}`);
                } else {
                    logger.Info("无法获取停止前WSL状态，继续执行停止命令");
                }
                
                // 执行停止命令
                const stopCommand = 'wsl --shutdown';
                logger.Info(`执行停止命令: ${stopCommand}`);
                
                exec(stopCommand, (error, stdout, stderr) => {
                    if (stdout) logger.Info(`停止命令输出: ${stdout}`);
                if (stderr && !stderr.includes('Warning')) {
                    logger.Warn(`停止WSL警告: ${stderr}`);
                }
                
                if (error) {
                    logger.Warn(`停止WSL时出现警告: ${error.message}`);
                        logger.Info("停止命令有警告，但继续流程");
                } else {
                        logger.Info("✅ WSL停止命令执行完成");
                }
                
                    // 等待一段时间，然后验证停止效果
                setTimeout(() => {
                        logger.Info("验证停止效果，检查停止后WSL状态...");
                        exec('powershell -Command "wsl --list --verbose"', { encoding: 'utf8' }, (afterError, afterStdout) => {
                            if (afterStdout) {
                                logger.Info(`停止后WSL实例状态:\n${afterStdout}`);
                                
                                // 检查是否有Running状态的实例
                                const hasRunningInstances = afterStdout.includes('Running');
                                if (hasRunningInstances) {
                                    logger.Warn("⚠️ 停止后仍有Running状态的WSL实例");
                                } else {
                                    logger.Info("✅ 确认所有WSL实例已停止");
                                }
                            } else {
                                logger.Info("无法获取停止后WSL状态，但继续流程");
                            }
                            
                            resolve({ success: true, message: "WSL实例停止流程完成" });
                        });
                }, 2000);
                });
            });
            
        } catch (error) {
            logger.Warn(`停止WSL时出现异常: ${error.message}`);
            resolve({ success: false, message: `停止异常但继续: ${error.message}` });
        }
    });
}

// 卸载当前镜像
function uninstallCurrentMirror(logger) {
    return new Promise((resolve, reject) => {
        try {
            logger.Info("正在卸载当前镜像...");
            
            // 首先检查是否存在EchoWaveUbuntu实例
            // 使用PowerShell来确保正确的编码
            const checkCommand = 'powershell -Command "wsl --list --verbose"';
            logger.Info(`执行检查命令: ${checkCommand}`);
            
            exec(checkCommand, { encoding: 'utf8' }, (error, stdout, stderr) => {
                if (error) {
                    logger.Warn(`检查WSL实例列表时出错: ${error.message}`);
                    logger.Info("WSL可能未运行，但继续尝试卸载步骤");
                    // 即使检查失败，也要尝试卸载
                    attemptUninstall(logger, resolve, reject);
                    return;
                }
                
                const listOutput = stdout || '';
                logger.Info(`卸载前WSL实例列表:\n${listOutput}`);
                
                // 处理Unicode编码问题，同时检查原始输出和转换后的输出
                const containsEchoWave = listOutput.includes('EchoWaveUbuntu') || 
                                       listOutput.includes('E\u0000c\u0000h\u0000o\u0000W\u0000a\u0000v\u0000e\u0000U\u0000b\u0000u\u0000n\u0000t\u0000u');
                
                if (containsEchoWave) {
                    logger.Info("✅ 发现EchoWaveUbuntu实例，正在卸载...");
                    attemptUninstall(logger, resolve, reject);
                } else {
                    logger.Info("ℹ️ 未发现EchoWaveUbuntu实例，跳过卸载步骤");
                    resolve({ success: true, message: "无需卸载，实例不存在" });
                }
            });
            
        } catch (error) {
            logger.Error(`卸载镜像异常: ${error.message}`);
            logger.Info("卸载异常，但继续后续流程");
            resolve({ success: false, message: `卸载异常但继续: ${error.message}` });
        }
    });
}

// 执行实际的卸载操作
function attemptUninstall(logger, resolve, reject) {
    logger.Info("开始执行卸载命令...");
    const uninstallCommand = 'wsl --unregister EchoWaveUbuntu';
    logger.Info(`执行卸载命令: ${uninstallCommand}`);
    
    exec(uninstallCommand, (unregError, unregStdout, unregStderr) => {
        if (unregStdout) logger.Info(`卸载输出: ${unregStdout}`);
        if (unregStderr) logger.Warn(`卸载警告: ${unregStderr}`);
        
        if (unregError) {
            logger.Warn(`卸载镜像出现错误: ${unregError.message}`);
            // 即使出错也不失败，因为可能镜像已经不存在了
            logger.Info("卸载出错但继续流程，可能镜像已不存在");
        } else {
            logger.Info("✅ EchoWaveUbuntu镜像卸载命令执行完成");
        }
        
        // 再次列出实例做对比
        logger.Info("验证卸载结果，再次检查WSL实例列表...");
        const afterCheckCommand = 'powershell -Command "wsl --list --verbose"';
        exec(afterCheckCommand, { encoding: 'utf8' }, (afterErr, afterStdout) => {
            const afterListOutput = afterStdout || '(无输出)';
            logger.Info(`卸载后WSL实例列表:\n${afterListOutput}`);
            
            // 检查卸载是否成功
            const stillContainsEchoWave = afterListOutput.includes('EchoWaveUbuntu') || 
                                        afterListOutput.includes('E\u0000c\u0000h\u0000o\u0000W\u0000a\u0000v\u0000e\u0000U\u0000b\u0000u\u0000n\u0000t\u0000u');
            
            if (stillContainsEchoWave) {
                logger.Warn("⚠️ 卸载后仍发现EchoWaveUbuntu实例，可能卸载未完全成功");
            } else {
                logger.Info("✅ 确认EchoWaveUbuntu实例已被卸载");
            }
            
            // 等待一段时间确保完全卸载
            setTimeout(() => {
                resolve({ success: true, message: "EchoWaveUbuntu卸载流程完成" });
            }, 3000);
        });
    });
}

// 导入新镜像 - 使用EchoWaveUbuntu作为镜像名称（替换原有镜像）
function importNewMirror(updateFilePath, logger, config) {
    return new Promise((resolve, reject) => {
        try {
            logger.Info("正在导入新镜像...");
            logger.Info(`镜像文件: ${updateFilePath}`);
            
            // 再次检查更新文件是否存在
            if (!fs.existsSync(updateFilePath)) {
                const errorMsg = `更新文件不存在: ${updateFilePath}`;
                logger.Error(errorMsg);
                reject(new Error(errorMsg));
                return;
            }
            
            // 获取文件大小
            const stats = fs.statSync(updateFilePath);
            logger.Info(`镜像文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
            
            // 确保目标目录存在
            const targetDir = 'C:\\EchoWaveUbuntu';  // 使用原来的目录名
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
                logger.Info(`创建目标目录: ${targetDir}`);
            }
            
            // 使用与原始安装相同的方式，通过cmd窗口执行WSL导入
            // 参考首次安装的方式，这样能确保与首次安装逻辑一致
            const importCommand = `start cmd.exe /k "(wsl --import EchoWaveUbuntu ${targetDir} "${updateFilePath}" --version 2 && wsl --set-version EchoWaveUbuntu 2) || exit 0 & exit 0"`;
            
            logger.Info(`执行导入命令: ${importCommand}`);
            logger.Info("将弹出CMD窗口执行导入，请等待完成");
            
            exec(importCommand, { shell: true }, (error, stdout, stderr) => {
                if (stdout) logger.Info(`导入命令输出: ${stdout}`);
                if (stderr && !stderr.includes('Warning')) {
                    logger.Warn(`导入镜像警告: ${stderr}`);
                }
                
                if (error) {
                    logger.Warn(`导入命令执行警告: ${error.message}`);
                    // 不立即失败，因为cmd窗口方式执行后立即返回，不代表实际失败
                }
                
                logger.Info("新镜像导入命令已执行，CMD窗口已启动");
                logger.Info("导入过程将在CMD窗口中进行，请等待窗口完成并自动关闭");
                
                // 等待CMD窗口启动并开始导入
                setTimeout(() => {
                    logger.Info("导入进程已启动，返回成功状态供前端监控导入进度");
                    resolve({ 
                        success: true, 
                        message: "导入命令已执行，请等待导入完成" 
                    });
                }, 3000);
            });
            
        } catch (error) {
            logger.Error(`导入新镜像失败: ${error.message}`);
            reject(error);
        }
    });
}

route.set("update_mirror", f);

// 添加检查特定WSL实例的API
const checkWSLInstanceAPI = (c, L) => {
    const logger = L.Neo("SERVER.CheckWSLInstance");
    return async (e, instanceName) => {
        logger.Info(`检查WSL实例: ${instanceName}`);
        try {
            const exists = await checkWSLInstance(instanceName, logger);
            return { success: true, exists: exists };
        } catch (error) {
            logger.Error(`检查WSL实例失败: ${error.message}`);
            return { success: false, exists: false, message: error.message };
        }
    };
};

route.set("check_wsl_instance", checkWSLInstanceAPI);

export {f, checkWSLInstanceAPI};
export default f; 