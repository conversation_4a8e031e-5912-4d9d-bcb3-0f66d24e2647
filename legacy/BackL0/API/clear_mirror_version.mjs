'use strict';
import {route} from "./scope.mjs";
import fs from 'fs';
import path from 'path';
import {app} from 'electron';

const f = (c, L) => {
    const logger = L.Neo("SERVER.ClearMirrorVersion");
    return (e) => {
        logger.Info("清除镜像版本信息");
        
        try {
            // 获取用户数据目录
            const userDataPath = app.getPath('userData');
            const versionFilePath = path.join(userDataPath, 'mirror-version.json');
            
            // 检查文件是否存在
            if (fs.existsSync(versionFilePath)) {
                fs.unlinkSync(versionFilePath);
                logger.Info("镜像版本文件已删除");
            } else {
                logger.Info("镜像版本文件不存在，无需删除");
            }
            
            return true;
        } catch (error) {
            logger.Error(`清除镜像版本信息失败: ${error.message}`);
            return false;
        }
    };
};

route.set("clear_mirror_version", f);

export {f};
export default f; 