"use strict";
import { route } from "./scope.mjs";
import { CheckMirror } from "../Operations/CheckMirror.mjs";
import { MergeAsyncCalls } from "../../Utils/MergeAsyncCall.mjs";

const f = (c, L) => {
    const logger = L.Neo("SERVER.CheckMirror");
    return MergeAsyncCalls((e) => {
        logger.Info("检查镜像");
        return CheckMirror(logger, c.distroName);
    });
};

route.set("check_mirror", f);

export { f as check_mirror };
export default f;
