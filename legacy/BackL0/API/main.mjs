"use strict";

import { route } from "./scope.mjs";

import "./ping.mjs";
import "./ping_ex.mjs";
import "./check_virtual.mjs";
import "./check_os.mjs";
import "./check_wsl_enabled.mjs";
import "./install_wsl.mjs";
import "./check_mirror.mjs";
import "./install_mirror.mjs";
import "./uninstall_mirror.mjs";
import "./run_mirror.mjs";
import "./stop_mirror.mjs";
import "./mirror_ip.mjs";
import "./check_mirror_node_status";
import "./get_uid.mjs";
import "./write_log.mjs";
import "./pack_log.mjs";
import "./show_dir.mjs";
import "./get_ver.mjs";
import "./get_log.mjs";
import "./check_powershell.mjs";
import "./install_powershell.mjs";
import "./mirror_node_id.mjs";
import "./show_url.mjs";
import "./check_net.mjs";
import "./write_info.mjs";
import "./auto_start.mjs";
import "./save_mirror_version.mjs";
import "./get_mirror_version.mjs";
import "./clear_mirror_version.mjs";
import "./get_latest_mirror_info.mjs";
import "./download_mirror_update.mjs";
import "./update_mirror.mjs";
import "./delete_file.mjs";
import "./open_windows_features_dialog.mjs";
import "./check_self_node_status.js";

export { route };
export default route;
