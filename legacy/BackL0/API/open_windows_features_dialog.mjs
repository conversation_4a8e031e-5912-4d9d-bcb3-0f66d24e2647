"use strict";
import { route } from "./scope.mjs";
import { OpenOptionalFeatures } from "../Operations/OpenOptionalFeatures.mjs";

const f = (c, L) => {
  const logger = L.Neo("SERVER.WindowsFeatures");
  return async (e, v) => {
    logger.Info("打开 “启用或关闭 Windows 功能” 窗口");
    await OpenOptionalFeatures(logger);
  };
};

route.set("open_windows_features_dialog", f);

export { f as open_windows_features_dialog };
export default f;
