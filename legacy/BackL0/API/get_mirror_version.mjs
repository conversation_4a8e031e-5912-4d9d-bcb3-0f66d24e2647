'use strict';
import {route} from "./scope.mjs";
import fs from 'fs';
import path from 'path';
import {app} from 'electron';

const f = (c, L) => {
    const logger = L.Neo("SERVER.GetMirrorVersion");
    return (e) => {
        logger.Info("获取镜像版本信息");
        
        try {
            // 获取用户数据目录
            const userDataPath = app.getPath('userData');
            const versionFilePath = path.join(userDataPath, 'mirror-version.json');
            
            // 检查文件是否存在
            if (!fs.existsSync(versionFilePath)) {
                logger.Info("镜像版本文件不存在");
                return null;
            }
            
            // 读取版本数据
            const versionData = JSON.parse(fs.readFileSync(versionFilePath, 'utf8'));
            
            logger.Info(`获取镜像版本信息成功: ${versionData.current_version}`);
            return versionData;
        } catch (error) {
            logger.Error(`获取镜像版本信息失败: ${error.message}`);
            return null;
        }
    };
};

route.set("get_mirror_version", f);

export {f};
export default f; 