"use strict";
import { route } from "./scope.mjs";
import {
    EnableAutoStart,
    DisableAutoStart,
    CheckAutoStart,
} from "../Operations/AutoStart.mjs";

const f0 = (c, L) => {
    const logger = <PERSON>.Neo("SERVER.EnableAutoStart");
    return (e) => {
        logger.Info("设置开机启动");
        // 直接使用process.execPath，去掉.exe后缀，因为AutoStart函数会自动添加
        const execPath = c.execPath.replace(/\.exe$/, "");
        return EnableAutoStart(logger, c.appName, execPath);
    };
};

const f1 = (c, L) => {
    const logger = L.Neo("SERVER.DisableAutoStart");
    return (e) => {
        logger.Info("取消开机启动");
        return DisableAutoStart(logger, c.appName);
    };
};

const f2 = (c, L) => {
    const logger = L.Neo("SERVER.CheckAutoStart");
    return (e) => {
        logger.Info("检查是否开机启动");
        return CheckAutoStart(logger, c.appName, c.execPath);
    };
};

route.set("auto_start_enable", f0);
route.set("auto_start_disable", f1);
route.set("auto_start_check", f2);

export {
    f0 as auto_start_enable,
    f1 as auto_start_disable,
    f2 as auto_start_check,
};
export default f0;
