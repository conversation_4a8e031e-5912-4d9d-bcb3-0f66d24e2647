'use strict';
import {route} from "./scope.mjs";

const f0 = (c, L) => {
    const logger = <PERSON><PERSON>("UI");
    return (e, s = "", l = 10, type = "UI", flag = 0) => {
        logger.name = type;
        logger.Info(s, l, flag);
        return true;
    };
};

const f1 = (c, L) => {
    const logger = <PERSON>.Neo("UI");
    return (e, s = "", l = 100, type = "UI", flag = 0) => {
        logger.name = type;
        logger.Error(s, l, flag);
        return true;
    };
};
const f2 = (c, L) => {
    const logger = L.Neo("UI");
    return (e, s = "", l = 60, type = "UI", flag = 0) => {
        logger.name = type;
        logger.Warn(s, l, flag);
        return true;
    };
};
const f3 = (c, L) => {
    const logger = L.Neo("UI");
    return (e, s = "", l = 0, type = "UI", flag = 0) => {
        logger.name = type;
        logger.Debug(s, l, flag);
        return true;
    };
};

route.set("write_log_info", f0);
route.set("write_log_error", f1);
route.set("write_log_warn", f2);
route.set("write_log_debug", f3);

export {f0 as write_log_info, f1 as write_log_error};
export default f0;