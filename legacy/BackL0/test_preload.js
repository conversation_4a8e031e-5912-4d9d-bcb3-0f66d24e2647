"use strict";
const electron = require("electron");
// const preload = require("@electron-toolkit/preload");
const versions = {
  node: () => process.versions.node,
  chrome: () => process.versions.chrome,
  electron: () => process.versions.electron,
  platform: () => process.platform
};
const apis = {
  ping: (v) => electron.ipcRenderer.invoke("ping", v),
  pingEx: (v, f) => electron.ipcRenderer.invoke("ping_ex", v, f),
  checkVirtual: () => electron.ipcRenderer.invoke("check_virtual"),
  checkOS: () => electron.ipcRenderer.invoke("check_os"),
  checkWSLEnabled: () => electron.ipcRenderer.invoke("check_wsl_enabled"),
  installWSL: () => electron.ipcRenderer.invoke("install_wsl"),
  checkMirror: () => electron.ipcRenderer.invoke("check_mirror"),
  installMirror: (url, md5) => electron.ipcRenderer.invoke("install_mirror", url, md5),
  installMirrorStat: () => electron.ipcRenderer.invoke("install_mirror_stat"),
  installMirrorCancel: () => electron.ipcRenderer.invoke("install_mirror_cancel"),
  uninstallMirror: () => electron.ipcRenderer.invoke("uninstall_mirror"),
  runMirror: () => electron.ipcRenderer.invoke("run_mirror"),
  stopMirror: () => electron.ipcRenderer.invoke("stop_mirror"),
  mirrorIP: () => electron.ipcRenderer.invoke("mirror_ip"),
  getUID: () => electron.ipcRenderer.invoke("get_uid"),
  writeLogInfo: (s, l, t, f) => electron.ipcRenderer.invoke("write_log_info", s, l, t, f),
  writeLogError: (s, l, t, f) => electron.ipcRenderer.invoke("write_log_error", s, l, t, f),
  packLog: (name) => electron.ipcRenderer.invoke("pack_log", name),
  showDir: (path) => electron.ipcRenderer.invoke("show_dir", path),
  getVersion: () => electron.ipcRenderer.invoke("get_ver"),
  getLog: () => electron.ipcRenderer.invoke("get_log"),
  checkPowerShell: () => electron.ipcRenderer.invoke("check_powershell"),
  installPowerShell: () => electron.ipcRenderer.invoke("install_powershell"),
  mirrorNodeID: () => electron.ipcRenderer.invoke("mirror_node_id"),
  showUrl: (url) => electron.ipcRenderer.invoke("show_url", url),
  checkNet: () => electron.ipcRenderer.invoke("check_net"),
  writeInfo: (s) => electron.ipcRenderer.invoke("write_info", s),
  enableAutoStart: () => electron.ipcRenderer.invoke("auto_start_enable"),
  disableAutoStart: () => electron.ipcRenderer.invoke("auto_start_disable"),
  checkAutoStart: () => electron.ipcRenderer.invoke("auto_start_check")
};
if (process.contextIsolated) {
  try {
    // electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("versions", versions);
    electron.contextBridge.exposeInMainWorld("API", apis);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.versions = versions;
  window.API = apis;
}
