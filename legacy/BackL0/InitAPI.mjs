"use strict";
import { ipcMain } from "electron";
import { route } from "./API/main.mjs";
import { LoggerFactory } from "../Utils/Logger.mjs";

const InitAPI = (config) => {
  const globe = {
    logRecord: [],
    mirrorInstallStat: { Progress: () => [0.0, 0.0], Cancel: () => {} },
    mirrorRunInterval: null,
    infoLogger: new LoggerFactory(
      10,
      config.dirname + "/log",
      "user",
      undefined,
      undefined,
      undefined,
      1,
    ),
  };
  const loggerFactory = new LoggerFactory(
    config.logLevel,
    config.dirname + "/log",
    undefined,
    config.logEncryptKey,
    (text, level, t, name, textRaw, flag) => {
      globe.logRecord.push({ text: textRaw, level, t, name, flag, type: 0 });
    },
    (text, level, t, name, textRaw, flag) => {
      globe.logRecord.push({ text: textRaw, level, t, name, flag, type: 1 });
    },
  );
  route.forEach((v, k) => {
    ipcMain.handle(k, v(config, loggerFactory, globe));
  });
  return loggerFactory;
  // 异常捕捉 目前没用
  // const LoggerMain = loggerFactory.Neo("Main");
  // process.on("uncaughtException", err => {
  //     LoggerMain.Error("uncaughtException: " + err);
  // });
  // throw new Error("Test Error"); // 测试异常捕捉
};

export { InitAPI };
export default InitAPI;
