'use strict';
import {spawn} from "node:child_process";

const RunMirror = (logger, distroName) => {
    // console.log(process.platform);
    switch(process.platform) {
        case "win32":
            return _f_win32(logger, distroName);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

const _f_win32 = (logger, distroName) => {
    return new Promise((resolve, reject) => {
        // wsl -d EchoWaveUbuntu
        // start cmd /c "wsl -d EchoWaveUbuntu"
        const bat = spawn("cmd", ["start", "cmd", "/c", `"wsl -d ${distroName}"`], {
            detached: true,
            shell: true,
            stdio: "ignore"
        });
        logger.Info("镜像启动完成", 10);
        bat.unref();
        return resolve(true);
    });
};

// RunMirror({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {RunMirror};
export default RunMirror;