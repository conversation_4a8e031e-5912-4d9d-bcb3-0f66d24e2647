"use strict";
// import {exec} from "node:child_process";
import { ExecCodingRetry as exec } from "../../Utils/ExecCoding.mjs";

const regex = /node_id[\s]=[\s]([A-Fa-f0-9-]+)/;

/**
 * @type {AbortController[]}
 */
const queue = [];

const MirrorNodeID = async (logger, distroName) => {
    // console.log(process.platform);
    const aborter = new AbortController();
    queue.push(aborter);
    try {
        switch (process.platform) {
            case "win32":
                return await _f_win32(logger, distroName, aborter.signal);
            default:
                logger.Error("不支持的平台");
                throw new Error("不支持的平台");
        }
    } catch (e) {
        throw e;
    } finally {
        queue.splice(queue.indexOf(aborter), 1);
    }
};

export const AbortQueryMirrorNodeId = (reason) => {
    queue.shift()?.abort(reason);
};
/**
 *
 * @param logger
 * @param {string}distroName
 * @param {?AbortSignal}signal
 * @returns {Promise<unknown>}
 * @private
 */
const _f_win32 = (logger, distroName, signal) => {
    return new Promise((resolve, reject) => {
        if (signal) {
            signal.addEventListener("abort", () => {
                reject(new Error(signal.reason));
            });
        }
        // wsl -d EchoWaveUbuntu -- nomad agent-info
        exec(
            `wsl -d ${distroName} -- nomad agent-info`,
            (error, stdout, stderr) => {
                const trimo = stdout.trim();
                logger.Info(trimo);
                if (error) {
                    logger.Error(`exec error: ${stderr} - ${trimo}`);
                    return reject({ e: error, o: trimo });
                }
                if (stderr) {
                    logger.Error(`stderr: ${stderr} - ${trimo}`);
                    return reject({ e: new Error(stderr), o: trimo });
                }
                const result = regex.exec(trimo);
                if (!result || !result[1]) {
                    logger.Error("无镜像节点ID输出");
                    return reject(new Error("无镜像节点ID输出"));
                }
                logger.Info("镜像节点ID: " + result[1], 10);
                return resolve(result[1]);
            },
            {
                retry: 60,
                interval: 5000,
                // 共 5 分钟
                onRetry: (count, retry, err, stderr) => {
                    // logger.Error(
                    //   `失败获取镜像 ID，开始重试(${count}/${retry}), 错误：${stderr}`,
                    // );
                },
                signal,
            },
        );
    });
};

// let str = `
// client
//     heartbeat tt1 = 19.608232289s
//     known servers = 192.168.2.53:4647
//     node_id = b42aab99-9a55-f86d-9c05-a484f17b801f
//     num_allocations = 0
// `;

// console.log(regex.exec(str));
// console.log(regex.exec(str)[1]);

// MirrorNodeID({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log("out", v);})
//     .catch((e) => {console.error("error", e);});

export { MirrorNodeID };
export default MirrorNodeID;
