'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

const UninstallMirror = (logger, distroName) => {
    // console.log(process.platform);
    switch(process.platform) {
        case "win32":
            return _f_win32(logger, distroName);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

const _f_win32 = (logger, distroName) => {
    return new Promise((resolve, reject) => {
        // wsl --unregister EchoWaveUbuntu
        exec(`wsl --unregister ${distroName}`, (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                logger.Error(`exec error: ${stderr} - ${trimo}`);
                return reject({e: error, o: trimo});
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            logger.Info("镜像卸载完成", 10);
            return resolve(true);
        }, "utf-16le");
    });
};

// UninstallMirror({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {UninstallMirror};
export default UninstallMirror;