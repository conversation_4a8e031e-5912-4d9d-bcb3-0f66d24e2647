import { getApiUrl, API_PATHS } from "../API/config.mjs";
import axios from "axios";

export const GetLatestMirrorInfo = async (c, logger) => {
  try {
    // 调用真实的 API 接口
    const apiUrl = getApiUrl(c, API_PATHS.getSysConfig);
    logger.Info(`获取镜像信息，请求URL：${apiUrl}`);
    // 构建请求配置
    const config = {
      headers: {
        "Content-Type": "application/json",
      },
      httpsAgent: new (await import("https")).Agent({
        rejectUnauthorized: false, // 忽略 SSL 证书验证（开发环境）
      }),
    };
    const token = c.credentials?.token;
    // 如果提供了 token，添加到请求头
    if (token) {
      config.headers["Authorization"] = token;
      logger.Info("使用已存在的 token 进行授权");
    } else {
      logger.Info("未提供 token，以匿名方式请求");
    }
    const response = await axios.post(
      apiUrl,
      {
        type: "CLIENT_VERSION",
      },
      config,
    );

    const responseData = response.data;

    // 输出原始响应数据
    logger.Info(
      `获取镜像信息 API 原始响应: ${JSON.stringify(responseData, null, 2)}`,
    );

    // 修改返回的数据
    // if (
    //   responseData &&
    //   responseData.success &&
    //   responseData.data &&
    //   responseData.data.value
    // ) {
    // 记录原始版本信息
    // logger.Info(
    //   `原始版本信息 - image_version: ${responseData.data.value.image_version}, image_url: ${responseData.data.value.image_url}`
    // );

    // 修改 image_url 和 image_version
    // 注意：这里改为 http 而不是 https，因为 8000 端口通常是 HTTP
    // todo: 未来移除这项
    if (c.mode === "development") {
      responseData.data.value.image_url =
        "http://192.168.1.58:8000/releases/image.tar";
    } else if (c.mode === "testing") {
      responseData.data.value.image_url =
        "http://192.168.2.10/releases/image.tar";
    }
    // responseData.data.value.image_version = '1.1';

    // logger.Info(
    //   `修改后版本信息 - image_version: ${responseData.data.value.image_version}, image_url: ${responseData.data.value.image_url}`
    // );
    // }
    if (
      responseData &&
      responseData.success &&
      responseData.data &&
      responseData.data.value
    ) {
      logger.Info("获取镜像信息成功", responseData.data.value);
      return responseData.data.value;
    } else {
      throw new Error(response?.err_message);
    }
  } catch (e) {
    logger.Error(`获取镜像信息失败: ${e.message}`);
    throw e;
  }
};
