'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

// 这个没有平台之分，仅限windows
const CheckPowerShell = (logger) => {
    return new Promise((resolve, reject) => {
        // where powershell
        exec("where powershell", (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            // if(error) {
            //     logger.Error(`exec error: ${stderr} - ${trimo}`);
            //     return reject({e: error, o: trimo});
            // }
            // if(stderr) {
            //     logger.Error(`stderr: ${stderr} - ${trimo}`);
            //     return reject({e: new Error(stderr), o: trimo});
            // }
            if(error || stderr) {
                logger.Info("PowerShell未安装", 10);
                return resolve(false);
            }
            if(stdout.indexOf("powershell.exe") >= 0) {
                logger.Info("PowerShell已安装", 10);
                return resolve(true);
            }
            else {
                logger.Info("PowerShell未安装", 10);
                return resolve(false);
            }
        });
    });
};

// CheckPowerShell({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {CheckPowerShell};
export default CheckPowerShell;