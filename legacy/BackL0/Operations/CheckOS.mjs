'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

const CheckOS = (logger) => {
    // console.log(process.platform);
    switch(process.platform) {
        case "win32":
            return _f_win32(logger);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

const _f_win32 = (logger) => {
    return new Promise((resolve, reject) => {
        // reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v CurrentBuildNumber >= 10240
        exec("reg query \"HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\" /v CurrentBuildNumber", (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                logger.Error(`exec error: ${stderr} - ${trimo}`);
                return reject({e: error, o: trimo});
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            const result = stdout.split(/\s+/).filter(Number)[0];
            if(result >= 10240) {
                logger.Info("操作系统版本 >= Win10", 10);
                return resolve(true);
            } else {
                logger.Info("操作系统版本 < Win10", 10);
                return resolve(false);
            }
        });
    });
};

// _f_win32({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {CheckOS};
export default CheckOS;