'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

const MirrorIP = (logger, distroName, vport = "8080") => {
    // console.log(process.platform);
    switch(process.platform) {
        case "win32":
            return _f_win32(logger, distroName, vport);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

const _f_win32 = (logger, distroName, vport) => {
    return new Promise((resolve, reject) => {
        // wsl -d EchoWaveUbuntu -- hostname -I
        exec(`wsl -d ${distroName} -- hostname -I`, (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                logger.Error(`exec error: ${stderr} - ${trimo}`);
                return reject({e: error, o: trimo});
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            const ip = trimo.split(" ")[0];
            logger.Info("镜像实例IP: " + ip, 10);
            return resolve([ip, vport]);
        });
    });
};

// MirrorIP({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {MirrorIP};
export default MirrorIP;