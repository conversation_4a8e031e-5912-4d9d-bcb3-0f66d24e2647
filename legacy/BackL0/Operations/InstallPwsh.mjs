'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

const regex = /PowerShell/;

// 这个没有平台之分，仅限windows
const InstallPowerShell = (logger) => {
    return new Promise((resolve, reject) => {
        exec(`winget install --id Microsoft.PowerShell --source winget`, (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                logger.Error(`exec error: ${stderr} - ${trimo}`);
                return reject({e: error, o: trimo});
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            if(regex.test(stdout)) {
                logger.Info("PowerShell安装完成", 10);
                return resolve(true);
            }
            else {
                logger.Info("PowerShell安装失败", 10);
                return resolve(false);
            }
        });
    });
};

// InstallPowerShell({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {InstallPowerShell};
export default InstallPowerShell;