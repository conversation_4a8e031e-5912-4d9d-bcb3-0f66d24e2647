import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";

export const OpenOptionalFeatures = async (logger) => {
  await new Promise((resolve) => {
    exec(
      "OptionalFeatures",
      (error, stdout, stderr) => {
        const trimo = stdout.trim();
        if (error) {
          logger.Error("exec error " + trimo);
        }
        resolve();
      },
      "utf16-le",
    );
  });
};
