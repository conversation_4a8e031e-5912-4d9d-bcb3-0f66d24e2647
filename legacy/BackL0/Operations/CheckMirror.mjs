'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

const CheckMirror = (logger, distroName) => {
    // console.log(process.platform);
    switch(process.platform) {
        case "win32":
            return _f_win32(logger, distroName);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

// const regex = /Windows/;
// const regexMirror = /EchoWaveUbuntu/;
// const regexMirrorRunning = /EchoWaveUbuntu(\s)+Running/;

const _f_win32 = (logger, distroName) => {
    const regex = /Windows/;
    const regexMirror = new RegExp(distroName);
    const regexMirrorRunning = new RegExp(distroName + "(\\s)+Running");
    const regexMirrorInstalling = new RegExp(distroName + "(\\s)+Installing")
    return new Promise((resolve, reject) => {
        // wsl -l
        exec("wsl -l -v", (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                // stdout = "Windows";
                // console.log(typeof stdout, stdout.length, regex.test(stdout));
                if(!stdout || !regex.test(stdout)) {
                    logger.Error(`exec error: ${stderr} - ${trimo}`);
                    return reject({e: error, o: trimo});
                }
                else {
                    logger.Info("镜像未安装", 10);
                    return resolve({installed: false, running: false});
                }
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            if(regexMirror.test(stdout)) {
                let installing = regexMirrorInstalling.test(stdout);
                let running = installing ? false : regexMirrorRunning.test(stdout);
                if (installing) {
                    logger.Info("镜像安装中", 10);
                } else{
                    logger.Info("镜像已安装，" + (running ? "且正在运行" : "但未在运行"), 10);
                }
                return resolve({installed: !installing, running, installing});
            }
            else {
                logger.Info("镜像未安装", 10);
                return resolve({installed: false, running: false, installing: false});
            }
        }, "utf-16le");
    });
};

// CheckMirror({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {CheckMirror};
export default CheckMirror;