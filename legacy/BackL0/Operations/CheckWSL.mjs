"use strict";
// import {exec} from "node:child_process";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";
// import {ExecTry} from "../Utils/ExecTry.mjs";
import { VersionCompare } from "../../Utils/VersionCompare.mjs";
import vtd from "../../externals/virt-detect";

// const regex0 = /Windows/;
// const regex1 = /wslinstall/;
const regex = /WSL.*?(([0-9]+\.)*[0-9]+)/;

/**
 * 这个没有平台之分，仅限windows
 * @param logger
 * @param {string} wslVer
 * @returns {Promise<number>}
 * @constructor
 *
 * ## 状态说明，正数表示可以运行，负数表示未满足安装条件，0 为未安装
 * - `-2` HyperV 未启用
 * - `-1` WSL 未启用
 * - `0` WSL 未安装
 * - `1` WSL 版本过低
 * - `2` WSL 已安装
 */
const CheckWSLEnabled = async (logger, wslVer = "2.4.13.0") => {
  // 非Windows平台直接返回未安装状态
  if (process.platform !== 'win32') {
    logger.Info("非Windows平台，WSL检查跳过");
    return Promise.resolve(0); // 未安装
  }

  try {
    // const hyperv_res = await vtd.isHypervEnabled();
    // if (!hyperv_res.enabled) {
    //   logger.Error("缺少 Windows 功能: HyperV 功能未启用");
    //   if (hyperv_res.details.length > 0) {
    //     logger.Error(`检查 Windows 功能: 细节 : ${hyperv_res.details.join()}`)
    //   }
    //   return Promise.resolve(-2);
    // }
    const wsl_res = await vtd.isWslEnabled();

    if (!wsl_res.enabled) {
      logger.Error("缺少 Windows 功能: WSL 功能未启用");
      if (wsl_res.details.length > 0) {
        logger.Error(`检查 Windows 功能: 细节 : ${wsl_res.details.join()}`)
      }
      return -1;
    }
  } catch (e) {
    logger.Error("WSL 检查失败: " + e);
  }
  return await new Promise((resolve, reject) => {
    // where wsl
    exec(
      "wsl -v",
      (error, stdout, stderr) => {
        const trimo = stdout.trim();
        logger.Info(trimo);
        // if(error) {
        //     logger.Error(`exec error: ${stderr} - ${trimo}`);
        //     return reject({e: error, o: trimo});
        // }
        // if(stderr) {
        //     logger.Error(`stderr: ${stderr} - ${trimo}`);
        //     return reject({e: new Error(stderr), o: trimo});
        // }
        if (error || stderr) {
          logger.Info("WSL未安装", 10);
          return resolve(0);
        }
        const reo = regex.exec(stdout);
        if (reo.length > 2) {
          if (VersionCompare(reo[1], wslVer) >= 0) {
            logger.Info("WSL已安装", 10);
            return resolve(2);
          } else {
            logger.Info("WSL版本过低", 10);
            return resolve(1);
          }
        } else {
          logger.Info("WSL未安装", 10);
          return resolve(0);
        }
      },
      "utf-16le",
    );
  });
};

// CheckWSLEnabled({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { CheckWSLEnabled };
export default CheckWSLEnabled;
