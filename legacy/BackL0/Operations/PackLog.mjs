"use strict";
// import {exec} from "node:child_process";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";
import { ShowDir } from "./ShowDir.mjs";

const PackLog = (logger, srcPath, dstPath, fileName) => {
  // console.log(process.platform);
  switch (process.platform) {
    case "win32":
      return _f_win32(logger, srcPath, dstPath, fileName);
  }
  return new Promise((resolve, reject) => {
    logger.Error("不支持的平台");
    reject(new Error("不支持的平台"));
  });
};

const _f_win32 = (logger, srcPath, dstPath, fileName) => {
  const dst = `${dstPath}/${fileName}.zip`;
  return new Promise((resolve, reject) => {
    // pwsh Compress-Archive -Path "C:\目标路径\*.log" -DestinationPath "C:\输出路径\logs.zip" -Force
    exec(
      `powershell Compress-Archive -Path "${srcPath}/*.log" -DestinationPath "${dst}" -Force`,
      (error, stdout, stderr) => {
        const trimo = stdout.trim();
        logger.Info(trimo);
        if (error) {
          logger.Error(`exec error: ${stderr} - ${trimo}`);
          return reject({ e: error, o: trimo });
        }
        if (stderr) {
          logger.Error(`stderr: ${stderr} - ${trimo}`);
          return reject({ e: new Error(stderr), o: trimo });
        }
        logger.Info("日志已打包: " + dstPath, 10);
        ShowDir(logger, dst, true).catch((reason) => logger.Info('打开日志文件夹出错' + JSON.stringify(reason)));
        resolve(true);
      },
    );
  });
};

// _f_win32({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, "../..")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { PackLog };
export default PackLog;
