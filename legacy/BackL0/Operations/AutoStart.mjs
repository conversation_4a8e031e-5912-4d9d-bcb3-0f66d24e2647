"use strict";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";
import path from "node:path";
import {
    access as FSAccess,
    constants as FSConstants,
    unlink as FSUnlink,
    existsSync,
} from "node:fs";
import fs from "node:fs";
import fsp from "node:fs/promises";
import os from "node:os";

// 如果是当前用户安装
const getCurrentUserStartupFolder = () => {
    return path.join(
        process.env.APPDATA,
        "Microsoft",
        "Windows",
        "Start Menu",
        "Programs",
        "Startup",
    );
};
// 如果是所有用户安装
// const getAllUsersStartupFolder = () => {
//     return path.join(
//         process.env.ProgramData,
//         "Microsoft",
//         "Windows",
//         "Start Menu",
//         "Programs",
//         "Startup",
//     );
// };

const getCurrentUserShortcutPath = (appName) => {
    return path.join(getCurrentUserStartupFolder(), `${appName}.lnk`);
};
// const getAllUsersShortcutPath = (appName) => {
//     return path.join(getAllUsersStartupFolder(), `${appName}.lnk`);
// };

const EnableAutoStart = (logger, appName, appPath) => {
    switch (process.platform) {
        case "win32":
            return WriteMSLinkToStartup(logger, appName, appPath);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

const DisableAutoStart = (logger, appName) => {
    switch (process.platform) {
        case "win32":
            return RemoveMSLinkFromStartup(logger, appName);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

/**
 * @param logger
 * @param {string} appName
 * @param {string} appPath
 * @returns {Promise<boolean>}
 * @constructor
 */
const CheckAutoStart = async (logger, appName, appPath) => {
    const shortcutPaths = [
        getCurrentUserShortcutPath(appName),
        // getAllUsersShortcutPath(appName),
    ];
    for (const shortcutPath of shortcutPaths) {
        const exists = await new Promise((resolve) => {
            FSAccess(shortcutPath, FSConstants.F_OK, (err) => {
                if (!err) {
                    logger.Info("开机启动已存在(快捷方式)", 10);
                    resolve(true);
                } else {
                    logger.Info("开机启动不存在(快捷方式) ", 10);
                    console.log("shortcutPath: ", shortcutPath);
                    console.log(err);
                    resolve(false);
                }
            });
        });
        if (!exists) continue;
        try {
            logger.Info(`解析快捷方式 "${shortcutPath}"`);
            const info = await ReadMSLink(shortcutPath);
            // console.log(info)
            logger.Info(`快捷方式  ["${info.TargetPath}" => "${appPath}"]`);
            const matched =
                path.resolve(info.TargetPath) === path.resolve(appPath);
            logger.Info(
                "解析快捷方式成功，路径" + (matched ? "匹配" : "不匹配"),
            );
            return matched;
        } catch (e) {
            logger.Info(`解析快捷方式失败` + e);
        }
    }
    return false;
};

/**
 * @typedef {Object} MSLink
 * @property {string} TartgetPath
 * @property {string} Arguments
 * @property {string} WorkingDirectory
 * @property {string} IconLocation
 */

/**
 *
 * @param path
 * @returns {Promise<MSLink>}
 * @constructor
 */
const ReadMSLink = async (path) => {
    const ps1 = `
  $lnkPath = '${path}'
  
  $wsh      = New-Object -ComObject WScript.Shell
  $shortcut = $wsh.CreateShortcut($lnkPath)
  
  $jsonObj = [PSCustomObject]@{
      TargetPath       = $shortcut.TargetPath
      Arguments        = $shortcut.Arguments
      WorkingDirectory = $shortcut.WorkingDirectory
      IconLocation     = $shortcut.IconLocation
  }
  
  $jsonObj | ConvertTo-Json -Depth 1`;

    const jsonStr = await runPsScript(ps1);
    return JSON.parse(jsonStr);
};

/**
 * @param {string}script
 * @returns {Promise<string>}
 */
const runPsScript = (script) => {
    return new Promise((resolve, reject) => {
        // -NoProfile 避免加载用户配置，加快启动
        // -NonInteractive 防止任何交互提示
        // -Command 后面跟实际 PS 代码
        const tmp = path.join(os.tmpdir(), `script-${Date.now()}.ps1`);
        fs.writeFileSync(tmp, script, "utf8");
        const full = `powershell -NoProfile -NonInteractive -ExecutionPolicy Bypass -File ${tmp}`;
        exec(full, (err, stdout, stderr) => {
            // console.log('runPsCommand: ', full, err, stdout, stderr);
            fs.unlinkSync(tmp);
            if (err) return reject(err);
            if (stderr) return reject(new Error(stderr));
            resolve(stdout.trim());
        });
    });
};

// 创建快捷方式
const WriteMSLinkToStartup = async (logger, appName, appPath) => {
    const shortcutPath = getCurrentUserShortcutPath(appName);
    if (existsSync(shortcutPath)) {
        try {
            logger.Info("删除已存在的快捷方式");
            await fsp.unlink(shortcutPath);
            logger.Info("删除已存在的快捷方式成功");
        } catch (err) {
            logger.Error("删除已存在的快捷方式失败，原因：" + err);
        }
    }
    return await new Promise((resolve, reject) => {
        // 使用 powershell 创建快捷方式
        const psCmd = `
        $WshShell = New-Object -ComObject WScript.Shell;
        $Shortcut = $WshShell.CreateShortcut('${shortcutPath}');
        $Shortcut.TargetPath = '${appPath}.exe';
        $Shortcut.Save();
        `;
        exec(
            `powershell -Command "${psCmd.replace(/\n/g, "")}"`,
            (error, stdout, stderr) => {
                const trimo = stdout.trim();
                logger.Info(trimo);
                if (error || stderr) {
                    logger.Error(`创建快捷方式失败: ${stderr || error}`);
                    return reject({ e: error || new Error(stderr), o: trimo });
                }
                logger.Info("成功设置开机启动(快捷方式)", 10);
                resolve(true);
            },
        );
    });
};

// 删除快捷方式
const RemoveMSLinkFromStartup = async (logger, appName) => {
    const shortcutPaths = [
        getCurrentUserShortcutPath(appName),
        // getAllUsersShortcutPath(appName),
    ];
    for (const shortcutPath of shortcutPaths) {
        if (!existsSync(shortcutPath)) {
            continue;
        }
        await new Promise((resolve) => {
            FSUnlink(shortcutPath, (err) => {
                if (err && err.code !== "ENOENT") {
                    logger.Error(`删除快捷方式失败: ${err}`);
                    resolve();
                }
                logger.Info("成功取消开机启动(快捷方式)", 10);
                resolve();
            });
        });
    }
};

// EnableAutoStart({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, "echowave_client", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\EchoWave\\EchoWave")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

// DisableAutoStart({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, "echowave_client")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

// CheckAutoStart({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, "echowave_client")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { EnableAutoStart, DisableAutoStart, CheckAutoStart };
export default EnableAutoStart;
