"use strict";
// import {exec} from "node:child_process";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";
import { getMachineId, MachineIdFactor } from "../../externals/virt-detect";
import fs from "fs";
import fsp from "fs/promises";
import { app } from "electron";
import crypto from "node:crypto";
import path from "node:path";

const userDataPath = app.getPath("userData");
const userMachineIdFile = path.join(userDataPath, "machineid");
// 第一次启动时检查是否存在 MACHINE_ID, 如果没有或和生成的不匹配视作新的设备。写 MACHINE_ID 需要等待启动镜像时清除 Nomad 的 NODE_ID 后才会写入，防止第二次启动检查失效（当前版本程序启动并不意味着镜像启动）
export const isNewClientRef = { value: undefined };

const GetUID = async (logger) => {
    // console.log(process.platform);
    const machineId = await (async () => {
        switch (process.platform) {
            case "win32":
                return _f_win32(logger);
            case "darwin":
            case "linux":
                return _f_mock(logger);
        }
        return new Promise((resolve, reject) => {
            logger.Error("不支持的平台");
            reject(new Error("不支持的平台"));
        });
    })();
    if (isNewClientRef.value === undefined) {
        const _machineId = await loadMachineIdFromFile(logger);
        isNewClientRef.value = _machineId !== machineId;
        if (isNewClientRef.value) {
            logger.Info(
                `MachineID 不匹配，标记为新设备，"${_machineId}" => "${machineId}"`,
            );
        }
    }
    return machineId;
};
const loadMachineIdFromFile = async (logger) => {
    logger.Info("开始从本地读取 machineid");
    try {
        if (fs.existsSync(userMachineIdFile)) {
            const value = await fsp.readFile(userMachineIdFile, {
                encoding: "utf-8",
            });
            logger.Info(`成功从本地读取到 machineid='${value}'`);
            return value;
        } else {
            logger.Info("未找到 machineid 文件");
            return undefined;
        }
    } catch (e) {
        logger.Error("读取 machineid 文件失败，原因: " + e);
        return undefined;
    }
};
export const writeMachineIdToFile = async (logger) => {
    logger.Info("开始写入 machineid 到本地");
    try {
        await fsp.writeFile(userMachineIdFile, machineId);
        logger.Info("写入 machineid 完成");
    } catch (e) {
        logger.Info("写入 machineid 失败, 原因：" + e);
    }
};
let machineId = undefined;
const _f_win32 = async (logger) => {
    if (machineId) return machineId;
    const machineIdResult = getMachineId([
        MachineIdFactor.Baseboard,
        MachineIdFactor.Processor,
        MachineIdFactor.DiskDrivers,
    ]);
    if (machineIdResult.machineId) {
        logger.Info(`MachineUID: ${machineIdResult.machineId}`, 10);
        logger.Info(
            `MachineUID Factors: ${machineIdResult.factors.join(", ")}`,
        );
        machineId = machineIdResult.machineId;
        return machineId;
    } else {
        logger.Error(`无法获取机器 ID, 原因: '${machineIdResult.error}'`);
    }
    machineId = await fallback(logger);
    logger.Info(`使用后备方案，机器 ID: ${machineId}`);
    return machineId;
};

// _f_win32({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

const fallback = (logger) => {
    return new Promise((resolve, reject) => {
        // Obsolete: wmic bios get serialnumber && wmic baseboard get serialnumber && wmic csproduct get uuid
        // Get-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Cryptography' -Name 'MachineGuid'
        // reg query "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid
        // exec("powershell Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Cryptography' -Name 'MachineGuid'", (error, stdout, stderr) => {
        exec(
            'powershell -Command "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"',
            (error, stdout, stderr) => {
                const trimo = stdout.trim();
                if (error) {
                    logger.Error(`exec error: ${stderr} - ${trimo}`);
                    return reject({ e: error, o: trimo });
                }
                if (stderr) {
                    logger.Error(`stderr: ${stderr} - ${trimo}`);
                    return reject({ e: new Error(stderr), o: trimo });
                }
                const uid = Buffer.from(trimo);
                const _sha256 = crypto.createHash("sha256");
                _sha256.update(uid);
                const shaUid = _sha256.digest("hex");
                resolve(shaUid);
            },
        );
    });
};

const _f_mock = async (logger) => {
    logger.Info("Mock implementation: 生成机器ID (非Windows平台)");
    // 为非Windows平台生成一个基于系统信息的简单机器ID
    const os = require('os');
    const crypto = require('crypto');

    const systemInfo = {
        platform: process.platform,
        arch: process.arch,
        hostname: os.hostname(),
        userInfo: os.userInfo().username,
        networkInterfaces: Object.keys(os.networkInterfaces()).join(',')
    };

    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(systemInfo));
    const machineId = hash.digest('hex').substring(0, 32);

    logger.Info(`Generated mock machine ID: ${machineId}`);
    return machineId;
};

export { GetUID };
export default GetUID;
