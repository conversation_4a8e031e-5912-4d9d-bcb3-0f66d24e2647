"use strict";
import axios from "axios";
import { DateEx } from "../../Utils/DateEx.mjs";

const MAX_TIMEOUT = 60_000;
let checkCount = 0;

export class CheckSelfNodeStatus {
    _dt = 0;
    _resolvers = undefined;
    _interval = undefined;
    _subscribeFn = undefined;
    _pending = false;
    /**
     *
     * @type {{status: 'idle', createdAt: undefined} | {status: 'running', createAt: string}}
     * @private
     */
    _last = {
        status: "idle",
        createdAt: undefined,
    };

    /**
     *
     * @param logger
     * @param {string} url
     * @param {()=>string} getToken
     * @param {()=>string | Promise<string>} getMachineId
     */
    constructor(logger, url, getToken, getMachineId) {
        this._logger = logger;
        this._url = url;
        this._getToken = getToken;
        this._getMachineId = getMachineId;
    }
    _pause = () => {
        if (this._interval) clearInterval(this._interval);
        this._interval = undefined;
    };
    _resume = () => {
        this._interval = setInterval(this._do, 60_000);
    };
    _do = async () => {
        const logger = this._logger;
        const s0 = Date.now();
        try {
            this._pending = true;
            const response = await axios.post(
                this._url,
                {
                    machine_id: await this._getMachineId(),
                },
                {
                    timeout: MAX_TIMEOUT,
                    headers: {
                        Authorization: this._getToken(),
                    },
                },
            );
            const dt = Date.now() - s0;
            const result = response.data.data;
            // 只有網絡延遲波動大時才輸出
            if (Math.abs(dt - this._dt) > 100) {
                logger.Info(`客户端到服务器网络延时: ${dt}`);
            }
            this._dt = dt;
            const current = {
                status: result?.task_status === "RUNNING" ? "running" : "idle",
                createdAt: result?.task_start_time || undefined,
            };
            const changed =
                current.status !== this._last.status ||
                current.createdAt !== this._last.createdAt;
            this._last = current;
            if (changed) {
                checkCount = 0;
                logger.Info(
                    `当前节点状态发生变化: ${current.status} 创建时间: ${current.createdAt || "--"}`,
                );
            } else {
                checkCount ++;
                if (checkCount > 10) {
                    logger.Info(`在过去${checkCount}次检查中，当前节点状态并无变化`);
                    checkCount = 0;
                }
            }
            this._subscribeFn?.(this._last);
            if (this._resolvers) {
                this._resolvers[0]();
                this._resolvers = undefined;
            }
        } catch (e) {
            logger.Info(`检查当前节点状态失败: ` + e);
            if (
                e instanceof Error &&
                e.message.includes("Please sign in to go on")
            ) {
                const dt = Date.now() - s0;
                // 只有網絡延遲波動大時才輸出
                if (Math.abs(dt - this._dt) > 100) {
                    logger.Info(`客户端到服务器网络延时: ${dt}`);
                }
                this._dt = dt;
            }
            if (this._resolvers) {
                this._resolvers[1](e);
                this._resolvers = undefined;
            }
            throw e;
        } finally {
            this._pending = false;
        }
    };

    /**
     * @param {(current: Object)=>void}fn
     */
    subscribe = (fn) => {
        this._subscribeFn = fn;
    };
    current = () => this._last;
    immediateCheck = async () => {
        this._logger.Info("立即进行节点状态检查")
        if (this._pending) {
            return await new Promise((resolve, reject) => {
                this._resolvers = [resolve, reject];
            });
        }
        this._pause();
        await this._do();
        this._resume();
        this._logger.Info("立即进行节点状态检查完成")
    };
    dispose() {
        if (this._interval) {
            clearInterval(this._interval);
            this._interval = null;
        }
    }
}
