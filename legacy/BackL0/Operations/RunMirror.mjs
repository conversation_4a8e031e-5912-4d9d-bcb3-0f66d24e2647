"use strict";
import { exec } from "node:child_process";
import { ExecCoding } from "../../Utils/ExecCoding.mjs";
import { isNewClientRef, writeMachineIdToFile } from "./GetUID.mjs";

/**
 * @type {{value: AbortController|undefined}}
 */
export const aborterRef = { value: undefined };
/**
 * @type {[(value: unknown)=>void,(reason: unknown)=>void][]}
 */
let resolvers = [];

const RunMirror = async (logger, distroName, globe) => {
    // console.log(process.platform);
    if (aborterRef.value) {
        return new Promise((resolve, reject) => {
            resolvers.push([resolve, reject]);
        });
    }
    aborterRef.value = new AbortController();
    return new Promise(async (resolve, reject) => {
        resolvers.push([resolve, reject]);
        try {
            switch (process.platform) {
                case "win32":
                    const value = await _f_win32(
                        logger,
                        distroName,
                        globe,
                        aborterRef.value.signal,
                    );
                    resolvers.forEach(([resolve]) => resolve(value));
                    break;
                default:
                    logger.Error("不支持的平台");
                    throw new Error("不支持的平台");
            }
        } catch (e) {
            resolvers.forEach(([, reject]) => reject(e));
        } finally {
            aborterRef.value = undefined;
            resolvers = [];
        }
    });
};
export const AbortRunMirror = (globe, reason) => {
    aborterRef.value?.abort(reason);
    if (globe.mirrorRunInterval) {
        clearInterval(globe.mirrorRunInterval);
        globe.mirrorRunInterval = null;
    }
};
const removeNomadData = async (logger, distroName) => {
    logger.Info("开始清理 Nomad");
    await new Promise((resolve, reject) => {
        ExecCoding(
            `wsl -d ${distroName} -u root -- rm -rf /opt/nomad/data`,
            (error, stdout, stderr) => {
                const trimo = stdout.trim();
                if (error) {
                    logger.Error(
                        `clean nomad data exec error: ${stderr} - ${trimo || stderr}`,
                    );
                    return reject({ e: error, o: trimo });
                }
                if (stderr) {
                    logger.Error(`stderr: ${stderr} - ${trimo}`);
                    return reject({ e: new Error(stderr), o: trimo });
                }
                resolve();
            },
            "utf-16le",
        );
    });
    logger.Info("已完成 nomad 清理");
};
const _f_win32 = async (logger, distroName, globe, signal) => {
    if (isNewClientRef.value === true) {
        try {
            logger.Info("这是一台新的设备，尝试移除可能存在的 Nomad 数据");
            await removeNomadData(logger, distroName);
            await writeMachineIdToFile(logger);
            isNewClientRef.value = false;
            logger.Info("移除 Nomad 数据完成");
        } catch (e) {
            logger.Info("移除 Nomad 数据失败，原因：" + e);
        }
    }
    return await new Promise((resolve, reject) => {
        if (globe.mirrorRunInterval) return resolve(true);
        // wsl -d EchoWaveUbuntu -- echo
        ExecCoding(
            `wsl -d ${distroName} -- echo`,
            (error, stdout, stderr) => {
                const trimo = stdout.trim();
                if (signal?.aborted) {
                    return resolve(false);
                }
                if (error) {
                    logger.Error(
                        `exec error: ${stderr} - ${trimo || stderr} - signal: ${signal?.aborted ?? "unknown"}`,
                    );
                    return reject({ e: error, o: trimo });
                }
                if (stderr) {
                    logger.Error(`stderr: ${stderr} - ${trimo}`);
                    return reject({ e: new Error(stderr), o: trimo });
                }
                logger.Info(trimo);
                logger.Info("镜像启动完成", 10);
                if (!globe.mirrorRunInterval) {
                    globe.mirrorRunInterval = setInterval(() => {
                        exec(`wsl -d ${distroName} -- echo`, { signal });
                    }, 1000);
                }
                return resolve(true);
            },
            "utf-16le",
            signal,
        );
    });
};

// RunMirror({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { RunMirror };
export default RunMirror;
