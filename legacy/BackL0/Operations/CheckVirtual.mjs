"use strict";
import vtd from "../../externals/virt-detect";

const CheckVirtual = (logger, path) => {
  // console.log(process.platform);
  switch (process.platform) {
    case "win32":
      return _f_win32(logger, path);
    case "darwin":
    case "linux":
      return _f_mock(logger, path);
  }
  return new Promise((resolve, reject) => {
    logger.Error("不支持的平台");
    reject(new Error("不支持的平台"));
  });
};

const _f_win32 = async (logger, path) => {
  try {
    const result = vtd.getVirtualization();
    logger.Info(`CPU: ${result.cpuSupported}, OS: ${result.osReportedEnabled}`);
    return result.osReportedEnabled;
  } catch (e) {
    logger.Error("检查虚拟化失败" + e);
    return true;
  }
};

const _f_mock = async (logger, path) => {
  logger.Info("Mock implementation: 虚拟化检查 (非Windows平台)");
  // 在非Windows平台上，我们假设虚拟化是可用的，以便开发测试
  return true;
};

export { CheckVirtual };
export default CheckVirtual;
