"use strict";
// import {exec} from "node:child_process";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";
import { existsSync, mkdirSync } from "node:fs";
import { DownloadFile } from "../../Utils/DownloadFile.mjs";
import { rm } from "node:fs/promises";

// https://cloud-images.ubuntu.com/wsl/releases/24.04/20240423/ubuntu-noble-wsl-amd64-24.04lts.rootfs.tar.gz

const _EmptyFn = () => {};

const InstallMirror = (
  logger,
  mirrorUrl,
  dirname,
  userdataDir,
  checkCode,
  distroName,
  handler = { Progress: _EmptyFn, Cancel: _EmptyFn },
) => {
  // console.log(process.platform);
  switch (process.platform) {
    case "win32":
      return _f_win32(
        logger,
        mirrorUrl,
        dirname,
        userdataDir,
        checkCode,
        distroName,
        handler,
      );
  }
  return new Promise((resolve, reject) => {
    logger.Error("不支持的平台");
    reject(new Error("不支持的平台"));
  });
};

const _f_win32 = async (
  logger,
  mirrorUrl,
  dirname,
  userdataDir,
  checkCode,
  distroName,
  handler,
) => {
  const progress = [0.0, 0.0];
  // wsl --update
  // wsl --import <DistroName> <location to Install> <location of wsl.rootfs.tar.gz File>
  // wsl --import EchoWaveUbuntu ./EchoWaveUbuntu ./image.tar
  // wsl --export EchoWaveUbuntu customized_ubuntu.tar
  const tmpDir = dirname + "/tmp/";
  if (!existsSync(tmpDir)) mkdirSync(tmpDir, { recursive: true });
  const aborter = new AbortController();
  const downloadHandler = { Progress: () => 0.0, Cancel: _EmptyFn };
  logger.Info(`开始从 "${mirrorUrl}" 下载镜像`);
  handler.Progress = () => {
    if (progress[0] < 1.0) return [downloadHandler.Progress(), 0.0];
    return [1.0, progress[1]];
  };
  handler.Cancel = () => {
    if (progress[0] < 1.0) {
      logger.Info("镜像取消下载", 10);
      downloadHandler.Cancel();
      aborter.abort("镜像取消下载");
    }
  };
  try {
    await DownloadFile(
      mirrorUrl,
      tmpDir + "image.tar",
      checkCode,
      downloadHandler,
      aborter.signal,
      logger,
    );
    logger.Info("镜像下载完成", 10);
    progress[0] = 1.0;
    progress[1] = 0.0;
  } catch (e) {
    logger.Error("下载镜像失败" + e);
    throw e;
  }
  if (aborter.signal.aborted) {
    return;
  }
  try {
    logger.Info(
      `开始导入镜像: '${`wsl --import ${distroName} ${userdataDir}/${distroName} ${tmpDir}image.tar`}'`,
    );
    if (existsSync(`${userdataDir}/${distroName}`)) {
      logger.Info("镜像数据卷目录已存在，即将删除");
      try {
        await rm(`${userdataDir}/${distroName}`, { recursive: true });
      } catch (e) {
        logger.Error("删除镜像数据卷失败" + e);
      }
      logger.Info("已删除镜像数据卷");
    }
    await new Promise((resolve, reject) => {
      exec(
        `wsl --import ${distroName} ${userdataDir}/${distroName} ${tmpDir}image.tar`,
        (error, stdout, stderr) => {
          const trimo = stdout.trim();
          logger.Info(trimo);
          if (error) {
            logger.Error(`导入镜像失败(A): ${stderr} - ${trimo}`);
            return reject({ e: error, o: trimo });
          }
          if (stderr) {
            logger.Error(`导入镜像失败(B): ${stderr} - ${trimo}`);
            return reject({ e: new Error(stderr), o: trimo });
          }
          return resolve();
        },
        'utf-16le'
      );
    });
    logger.Info("镜像安装完成", 10);
  } catch (e) {
    throw e;
  }
  if (aborter.signal.aborted) {
    return;
  }
  try {
    logger.Info("设置镜像以 WSL2 运行");
    await new Promise((resolve, reject) => {
      exec(`wsl --set-version ${distroName} 2`, (error, stdout, stderr) => {
        const trimo = stdout.trim();
        if (error) {
          if (trimo.includes("Wsl/Service/WSL_E_VM_MODE_INVALID_STATE")) {
            return resolve();
          }
          logger.Error(`设置镜像失败(A): ${stderr} - ${trimo}`);
        } else if (stderr) {
          logger.Error(`设置镜像失败(B): ${stderr} - ${trimo}`);
        }
        return resolve();
      }, 'utf-16le');
    });
    logger.Info("设置镜像完成");
  } catch (e) {
    throw e;
  }
  progress[1] = 1.0;
  return true;
};

// InstallMirror({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, "https://cloud-images.ubuntu.com/wsl/releases/24.04/20240423/ubuntu-noble-wsl-amd64-24.04lts.rootfs.tar.gz", "./")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { InstallMirror };
export default InstallMirror;
