"use strict";
// import {exec} from "node:child_process";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";

// 这个没有平台之分，仅限windows
const ShowDir = (logger, path, select = false) => {
  return new Promise((resolve, reject) => {
    // explorer "C:\目标路径"
    const cmd = `powershell explorer ${select ? "/select," : ""}"${path.replaceAll("/", "\\")}"`;
    //console.log(`打开目录: ${cmd}`)
    exec(cmd, (error, stdout, stderr) => {
      const trimo = stdout.trim();
      // logger.Info(trimo);
      if (error) {
        logger.Error(`exec error: ${stderr} - ${trimo}`);
        return reject({ e: error, o: trimo });
      }
      if (stderr) {
        logger.Error(`stderr: ${stderr} - ${trimo}`);
        return reject({ e: new Error(stderr), o: trimo });
      }
      logger.Info("启动文件夹: " + path, 10);
      return resolve(true);
    });
  });
};

// ShowDir({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, ".")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { ShowDir };
export default ShowDir;
