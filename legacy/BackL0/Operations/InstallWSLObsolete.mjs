'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";
// import iconv from "iconv-lite";
// {encoding: "buffer"}
// iconv.decode(stdout, "gbk");

const tempFile = "_echo_install_wsl_output.txt";
const regex = /Online\s+:\s+True/;
const regexRestart = /RestartNeeded\s+:\s+False/;
const timeout = 5;

// 这个没有平台之分，仅限windows
const InstallWSL = (logger) => {
    return new Promise((resolve, reject) => {
        // Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux
        const command = `Start-Process powershell -Verb RunAs -ArgumentList 'Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux > $env:TEMP/${tempFile}'`;
        // exec("ping 127.0.0.1 -n 3 > nul", {encoding: "buffer"}, (error, stdout, stderr) => {
        // exec(`pwsh -Command "${command}"`, (error, stdout, stderr) => {
        exec(`powershell -Command "${command}" && ping 127.0.0.1 -n ${timeout} > nul && type %TEMP%\\${tempFile} && del %TEMP%\\${tempFile}`, (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                logger.Error(`exec error: ${stderr} - ${trimo}`);
                return reject({e: error, o: trimo});
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            if(regex.test(stdout)) {
                let noNeedRestart = regexRestart.test(stdout);
                logger.Info("WSL安装完成，" + (noNeedRestart ? "不需要重启" : "需要重启"), 10);
                return resolve({succeed: true, needRestart: !noNeedRestart});
            }
            else {
                logger.Info("WSL安装失败", 10);
                return resolve({succeed: false, needRestart: false});
            }
        });
    });
};

// InstallWSL({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {InstallWSL};
export default InstallWSL;