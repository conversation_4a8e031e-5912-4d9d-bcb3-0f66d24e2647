"use strict";
import axios from "axios";
import { DateEx } from "../../Utils/DateEx.mjs";
import dns from "node:dns";

const MAX_TIMEOUT = 60_000;
const MAX_RECORD = 5;

const checkDns = async (
    hostname = "www.echowave.cn",
    timeout = MAX_TIMEOUT,
) => {
    let timer = null;
    return await Promise.race([
        new Promise((resolve) => {
            dns.resolve(hostname, (err) => {
                if (timer) clearTimeout(timer);
                timer = null;
                resolve(!err);
            });
        }),
        new Promise((_, reject) => {
            timer = setTimeout(() => {
                timer = null;
                reject(new Error("Timeout"));
            }, timeout);
        }),
    ]);
};

class CheckNet {
    _dt = 0;
    constructor(logger) {
        this._logger = logger;
        this._stats = [];
        this._interval = setInterval(this._do.bind(this), MAX_TIMEOUT);
        this._do();
    }
    _record(dt, s0) {
        if (
            this._stats.length > 0 &&
            this._stats[this._stats.length - 1].t > s0
        )
            return this;
        this._stats.push({ v: dt, t: s0 });
        if (this._stats.length > MAX_RECORD) this._stats.shift();
        return this;
    }
    _do() {
        const logger = this._logger;
        const s0 = DateEx.now();
        checkDns()
            .then(() => {
                const dt = DateEx.now() - s0;
                // 只有網絡延遲波動大時才輸出
                if (Math.abs(dt - this._dt) > 100 || this._dt === 0) {
                    logger.Info(`客户端网络延时: ${dt}`);
                }
                this._dt = dt;
                this._record(dt, s0);
            })
            .catch((e) => {
                logger.Info(`客户端网络异常` + e);
                this._record(MAX_TIMEOUT * 2, s0);
            });
        return this;
    }
    Dispose() {
        if (this._interval) {
            clearInterval(this._interval);
            this._interval = null;
        }
        return this;
    }
    get stat() {
        if (this._stats.length === 0) return 0;
        let avg = 0;
        this._stats.forEach((v) => (avg += v.v));
        avg /= this._stats.length;
        avg = Math.round(avg);
        if (avg >= MAX_TIMEOUT) return -1;
        return avg;
    }
}

export { CheckNet };
export default CheckNet;
