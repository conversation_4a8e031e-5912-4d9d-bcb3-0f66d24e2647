'use strict';
// import {exec} from "node:child_process";
import {ExecCoding as exec} from "../../Utils/ExecCoding.mjs";

const ShowUrl = (logger, path) => {
    // console.log(process.platform);
    switch(process.platform) {
        case "win32":
            return _f_win32(logger, path);
    }
    return new Promise((resolve, reject) => {
        logger.Error("不支持的平台");
        reject(new Error("不支持的平台"));
    });
};

const _f_win32 = (logger, path) => {
    return new Promise((resolve, reject) => {
        // start www.baidu.com
        exec(`start ${path}`, (error, stdout, stderr) => {
            const trimo = stdout.trim();
            logger.Info(trimo);
            if(error) {
                logger.Error(`exec error: ${stderr} - ${trimo}`);
                return reject({e: error, o: trimo});
            }
            if(stderr) {
                logger.Error(`stderr: ${stderr} - ${trimo}`);
                return reject({e: new Error(stderr), o: trimo});
            }
            logger.Info("启动浏览器: " + path, 10);
            return resolve(true);
        });
    });
};

// ShowUrl({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}}, "www.baidu.com")
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export {ShowUrl};
export default ShowUrl;