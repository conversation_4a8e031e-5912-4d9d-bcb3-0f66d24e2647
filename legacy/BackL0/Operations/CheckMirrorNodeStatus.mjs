import { exec } from 'node:child_process'
import { MirrorNodeID } from './MirrorNodeID.mjs'

/**
 * 检查是否处于空闲状态
 * 
 * 如果处于空闲状态说明没有任务再跑
 * @param {string|undefined} nodeId
 * @param {string} distroName
 * @param {*} logger
 * @returns {Promise<boolean>}
 */
export async function CheckMirrorNodeIsIdle(nodeId, distroName, logger){
    try {
        logger.Info('获取节点状态中')
        if (!nodeId) {
            nodeId = await MirrorNodeID(logger, distroName)
        }
        /**
         * @type {string[]}
         */
        const lines = await new Promise((resolve, reject) => {
            exec(`wsl -d ${distroName} -- nomad status ${nodeId}`, (err, stdout, stderr) => {
                if (err) {
                    reject(err)
                    return
                }
                const lines = stdout.split('\n');
                for (let i=0; i< lines.length; i++){
                    if (lines[i].trim() !== 'Allocations') continue
                    resolve(lines.slice(i + 1).map(it => it.trim()).filter(it => it.length > 0))
                    return
                }
            })
        })
        // No allocations placed
        if (lines[0] === 'No allocations placed') {
            logger.Info('当前节点处于空闲')
            return true
        } else {
            const allocations = lines.slice(1).map(it => {
                const values = it.split(/\s+/);
                return {
                    id: values[0],
                    node_id: values[1],
                    task_group: values[2],
                    version: values[3],
                    desired: values[4],
                    status: values[5],
                    created: values[6],
                    modified: values[7]
                }
            })
            // console.log('Allocations', allocations);
            const runningNum = allocations.filter(it => it.status === 'running').length;
            const pendingNum = allocations.filter(it => it.status === 'pending').length;
            logger.Info(`当前节点存在 ${(lines.length - 1)} 任务, running: ${runningNum} pending: ${pendingNum}`)
            return runningNum === 0 && pendingNum === 0
        }
    } catch(e) {
        throw e
    }
}

// console.log(await CheckMirrorNodeIsIdle('45d1c297', 'EchoWaveUbuntu', {
//     Info: console.log,
//     Error: console.error
// }))