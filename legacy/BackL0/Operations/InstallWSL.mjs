"use strict";
import DownloadFile from "../../Utils/DownloadFile.mjs";
// import {exec} from "node:child_process";
import { ExecCoding as exec } from "../../Utils/ExecCoding.mjs";
import { existsSync } from "fs";

// 这个没有平台之分，仅限windows
const InstallWSL = async (
  logger,
  dirname = ".",
  wslVer = "2.4.13.0",
  source,
  wslSourceHash,
) => {
  const dirnamew = dirname.replace(/\//g, "\\");
  const wslInstallerPath = `${dirnamew}\\wsl.${wslVer}.x64.msi`;
  let exists = true;
  if (!existsSync(wslInstallerPath)) {
    exists = false;
    logger.Info(`开始从 "${source}" 下载 WSL 安装包`);
  }
  const downloadHandler = { Progress: () => 0.0, Cancel: () => undefined };
  const timer = setInterval(() => {
    logger.Info(
      `WSL 安装包已下载: ${Math.ceil(downloadHandler.Progress() * 100)}%`,
    );
  }, 5_000);
  // 校验码是检查本地文件而非下载文件
  try {
    await DownloadFile(
      source,
      wslInstallerPath,
      wslSourceHash,
      downloadHandler,
      undefined,
      logger,
    );
    if (!exists) logger.Info("WSL 安装包下载完成", 10);
  } catch (e) {
    logger.Info("WSL 安装包下载失败" + e);
    throw e;
  } finally {
    clearInterval(timer);
  }
  return await new Promise((resolve, reject) => {
    // wsl --install --no-distribution && wsl --set-default-version 2
    exec(
      `msiexec /i "${wslInstallerPath}" /passive /norestart && wsl --set-default-version 2`,
      (error, stdout, stderr) => {
        const trimo = stdout.trim();
        logger.Info(trimo);
        if (error) {
          logger.Error(`exec error: ${stderr} - ${trimo}`);
          return reject({ e: error, o: trimo });
        }
        if (stderr) {
          logger.Error(`stderr: ${stderr} - ${trimo}`);
          return reject({ e: new Error(stderr), o: trimo });
        }
        let noNeedRestart = false;
        logger.Info(
          "WSL安装完成，" + (noNeedRestart ? "不需要重启" : "需要重启"),
          10,
        );
        return resolve({ succeed: true, needRestart: !noNeedRestart });
      },
      "utf-16le",
    );
  });
};

// InstallWSL({Info: (v) => {console.log(v);}, Error: (v) => {console.error(v);}})
//     .then((v) => {console.log(v);})
//     .catch((e) => {console.error(e);});

export { InstallWSL };
export default InstallWSL;
