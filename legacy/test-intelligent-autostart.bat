@echo off
chcp 437 > nul
echo EchoWave Intelligent Auto-Start Test Script
echo ==========================================

REM Test parameters
set "username=13011129301"
set "password=123456"
set "app_path=C:\Users\<USER>\AppData\Local\Programs\EchoWave\EchoWave.exe"

echo Testing intelligent auto-start functionality...
echo Username: %username%
echo Password: [HIDDEN]
echo App path: %app_path%
echo.

REM Check if app exists
if not exist "%app_path%" (
  echo Error: EchoWave application not found at %app_path%
  echo Please check the installation path.
  pause
  exit /b 1
)

echo Starting EchoWave with intelligent auto-start...
echo.
echo What will happen:
echo 1. App will start and attempt auto-login
echo 2. Frontend will monitor 6 environment checks automatically
echo 3. Once all checks pass, task acceptance will start automatically
echo 4. No fixed waiting time - intelligent monitoring
echo.

start "" "%app_path%" --username="%username%" --password="%password%" --autoStart=true

echo Application started with intelligent auto-start parameters.
echo.
echo Monitor the application:
echo - Check system tray for EchoWave icon
echo - Right-click tray icon and select "Show Window" to see progress
echo - Environment checks will be monitored automatically
echo - Task acceptance will start when all 6 checks pass
echo.
echo Press any key to exit this script (app will continue running)...
pause > nul 