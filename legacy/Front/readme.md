# 功能对应说明
```
前端目录为/src/renderer/src/，不要在此目录下做开发
所有API&调用说明在./api.mjs
UI和API的对应关系在./UI.docx
```
## 项目结构说明
```
/BackL0/                            Electron主进程接口实现相关
/BackL0/API/                        主进程&渲染进程交互接口(下面简称接口)对接层
/BackL0/API/main.mjs                加载哪些接口
/BackL0/Operations/                 接口功能实现层
/BackL0/InitAPI.mjs                 接口功能初始化
/BackL0/test_preload.mjs            Electron Preload脚本(test专用)

/BackL1/                            镜像内Agent服务(下面简称服务)实现相关
/BackL1/API/Get/                    服务接口(HttpGet)
/BackL1/API/Post/                   服务接口(HttpPost)
/BackL1/API/main.mjs                加载哪些服务接口
/BackL1/HttpServer/                 Http服务具体框架实现
/BackL1/Main.mjs                    服务启动入口函数

/BuildL1/                           镜像内Agent服务运行相关
/BuildL1/config.mjs                 服务配置
/BuildL1/EchoBackL1.mjs             打包好的服务压缩版
/BuildL1/EchoBackL1.unmin.mjs       打包好的服务未压缩版
/BuildL1/run.mjs                    服务运行入口

/C/                                 cpu指令方式虚拟化检测源码

/Front/                             测试&说明
/Front/api.mjs                      主进程&渲染进程交互接口说明

/resources/                         图标等资源

/src/                               Electron业务逻辑实现
/src/main/index.mjs                 主进程入口
/src/preload/index.js               Preload脚本
/src/renderer/                      渲染进程相关(Vue3)

/Utils/                             工具函数

/electron.vite.config.mjs           vite配置
/rollup.config.js                   镜像内Agent服务打包配置

/test.html                          Electron渲染进程入口页面(测试)
/test.mjs                           Electron主进程入口(测试)

/vt.exe                             cpu指令方式虚拟化检测程序

/install.nsh                        安装/卸载时附带命令
```
## 配置说明
### 打包配置
```
/package.json
{
    name: "echowave_client",        appName，用于开机自启设置注册表的appName
    version: "0.5.0",               客户端版本号，作用于打包和右下角版本显示
    main: "./out/main/index.js",    主进程入口
    build: {...},                   见Electron-Builder配置说明
    ...
}
```
### 客户端配置
```
/src/main/index.mjs
const config = {
    dirname: __execname,                            当前程序目录。用来存放日志、临时文件(镜像&wsl安装包)，以及执行虚拟化检测程序。
    logLevel: 10,                                   日志打印等级。等级定义见./日志相关说明.doc。
    logEncryptKey: "echo_wave",                     日志加密密钥(AES)。如果是undefined就不加密。
    vport: 11121,                                   Agent服务端口。目前镜像内未启用Agent服务，所以暂时没用。
    ver: packageJson.version,                       客户端版本。和package.json中的version保持一致。
    wslVer: "2.4.13.0",                             wsl安装版本。安装包的版本，安装时会从本地目录取相应msi。
    wslCheckVer: "2.0.4",                           wsl检查最低版本。如果低于这个版本，则wsl检测不通过。
    appName: packageJson.name,                      appName。和package.json中的name保持一致。
    distroName: "EchoWaveUbuntu",                   镜像实例名。
    productName: packageJson.build.productName      安装后的可执行文件名。用于开机自启设置注册表的app路径。
};      
```
### Agent服务配置
```
/BuildL1/config.mjs
const config = {
    host: "0.0.0.0",            监听地址
    port: 11121,                监听端口
    logLevel: 10,               日志等级
    dirname: __dirname          当前目录。用于存放日志
};
```