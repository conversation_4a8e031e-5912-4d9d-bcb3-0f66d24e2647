'use strict';
/**
 * test.html引用js & api说明
 * Front为electron渲染进程相关代码
 * BackL0为electron主进程相关代码，以下简称L0
 * BackL1为镜像内http服务相关代码，以下简称L1
 * Front通过全局变量operations调用L0的API
 */

//wsl --install --no-distribution

import {uploadUID} from "./upload_uid.mjs";

const button = document.getElementById("testBtn");
button.addEventListener("click", async () => {
    ver();
    ping();
    // pingEx();
    // checkVirtual();
    // checkOS();
    // checkWSLEnabled();
    // installWSL();
    // await checkMirror();
    // await installMirror();
    // await checkMirror();
    // await uninstallMirror();
    // await checkMirror();
    // await runMirror();
    // await checkMirror();
    // setTimeout(async () => {
    //     await stopMirror();
    //     await checkMirror();
    // }, 10000);
    await mirrorIP();
    // await getUID();
    // console.log("uploadUID", await uploadUID(await API.mirrorIP(), "halo", await API.getUID()));
    // writeLogInfo();
    // writeLogError();
    // packLog();
    // showDir();
    // getVer();
    // getLog();
    // await checkPowerShell();
    // await installPowerShell();
    // await checkPowerShell();
    // mirrorNodeID();
    // showUrl();
    // checkNet();
    // writeInfo();
    // enableAutoStart();
    // disableAutoStart();
    // checkAutoStart();
});

// 打印版本号&操作系统类型
async function ver() {
    console.log("versions", versions.node(), versions.chrome(), versions.electron(), versions.platform());
}

// 测试API，L0返回pong和传入的参数(字符串)
async function ping() {
    console.log("ping", await API.ping("my message"));
}

// 测试API，测试是否支持回调，结果是不行
async function pingEx() {
    console.log("pingEx", await API.pingEx("my message", (o) => console.log("wow", o)));
}

// 检查是否开启了虚拟化(返回Boolean，以下返回布尔的都略)
async function checkVirtual() {
    console.log("checkVirtual", await API.checkVirtual());
}

// 检查操作系统是否符合要求
async function checkOS() {
    console.log("checkOS", await API.checkOS());
}

// 检查WSL是否安装(仅限 platform === "win32") 0 未安装 1 版本过低 2 已安装
async function checkWSLEnabled() {
    console.log("checkWSLEnabled", await API.checkWSLEnabled());
}

// 安装WSL(仅限 platform === "win32")，返回{succeed: Boolean, needRestart: Boolean}，是否成功以及是否需要重启电脑
async function installWSL() {
    console.log("installWSL", await API.installWSL());
}

// 检查镜像状态，返回{installed: Boolean, running: Boolean}，是否安装以及是否正在运行
async function checkMirror() {
    console.log("checkMirror", await API.checkMirror());
}

// 安装镜像
async function installMirror() {
    const url = "https://cloud-images.ubuntu.com/wsl/releases/24.04/20240423/ubuntu-noble-wsl-amd64-24.04lts.rootfs.tar.gz";
    const md5 = "97617cd686663d0578712d26c4b5dac1";
    let c = 100;
    // url为镜像地址，md5为镜像文件的md5值。即如果用户本地已经下载了镜像文件，可以用md5来验证文件是否完整。md5可以为空(为空意味着无论如何用户都要重新下载镜像)。
    const p = API.installMirror(url, md5).then((v) => {
        c = 0;
        console.log("installMirror", v);
    });
    const iv = setInterval(() => {
        if(-- c < 0) {clearInterval(iv); return;}
        // 查询进度 [a, b]，a为下载进度，b为安装进度。
        // 目前安装进度无法监控，所以只返回0.0或1.0
        API.installMirrorStat().then((v) => {
            console.log("installMirrorStat", v);
        });
    }, 1000);
    await p;
}

// 卸载镜像，如果卸载失败直接抛出Error
async function uninstallMirror() {
    console.log("uninstallMirror", await API.uninstallMirror());
}

// 启动镜像(只返回命令有没有执行，镜像是否启动需要重新checkMirror)
async function runMirror() {
    console.log("runMirror", await API.runMirror());
}

// 停止镜像(只返回命令有没有执行，镜像是否启动需要重新checkMirror)
async function stopMirror() {
    console.log("stopMirror", await API.stopMirror());
}

// 查询镜像实例IP，返回[IP, port]，IP为镜像实例IP，port为端口号
// 每次镜像实例重启，ip可能会变，所以需要重新查询
async function mirrorIP() {
    console.log("mirrorIP", await API.mirrorIP());
}

// 获取UID，返回字符串
async function getUID() {
    console.log("getUID", await API.getUID());
}

// 把字符串写到日志文件
// 参数依次是 日志内容、日志等级、日志标注、日志flag。后三个可以省略。
async function writeLogInfo() {
    console.log("writeLogInfo", await API.writeLogInfo("my message", 10, "Meow", 1));
}

// 同上，但是是Error
async function writeLogError() {
    console.log("writeLogError", await API.writeLogError("my error", 15, "Meow.11"));
}

// 打包日志，打成zip文件放在桌面下，文件名为参数0
async function packLog() {
    console.log("packLog", await API.packLog("rizhi_日志"));
}

// 打开当前目录，参数为路径，如果不传默认是当前目录
async function showDir() {
    console.log("showDir", await API.showDir());
}

// 获取客户端版本号，返回字符串
async function getVer() {
    console.log("getVer", await API.getVersion());
}

// 获取任务日志，返回{text: string, level: number, t: number, name: string, flag: number, type: number}[]
// text为日志内容，level为日志等级，t为时间戳，name为日志标注，flag为日志标志(例如要不要在UI显示)，type为日志类型(Info/Error)
async function getLog() {
    setInterval(async () => {
        console.log("getLog", await API.getLog());
    }, 5000);
}

// 检查PowerShell是否安装，返回布尔值
async function checkPowerShell() {
    console.log("checkPowerShell", await API.checkPowerShell());
}

// 安装PowerShell，返回布尔值
async function installPowerShell() {
    console.log("installPowerShell", await API.installPowerShell());
}

// 获取镜像实例ID，返回字符串
async function mirrorNodeID() {
    console.log("mirrorNodeID", await API.mirrorNodeID());
}

// 打开浏览器，参数为url
async function showUrl() {
    const url = "https://www.baidu.com";
    console.log("showUrl", await API.showUrl(url));
}

// 检查网络是否正常，返回延迟
async function checkNet() {
    setInterval(async () => {
        console.log("checkNet", await API.checkNet());
    }, 1000);
}

// 写入用户信息
async function writeInfo() {
    let i = 0;
    setInterval(async () => {
        if(i % 5 === 0) {
            console.log("writeInfo", await API.writeInfo(""));
            i += 1;
            return;
        }
        console.log("writeInfo", await API.writeInfo("my info" + (++ i)));
    }, 1000);
}

// 设置开机自动启动
async function enableAutoStart() {
    console.log("enableAutoStart", await API.enableAutoStart());
}

// 取消开机自动启动
async function disableAutoStart() {
    console.log("disableAutoStart", await API.disableAutoStart());
}

// 检查开机自动启动是否设置
async function checkAutoStart() {
    console.log("checkAutoStart", await API.checkAutoStart());
}

// 乱码情况：chcp 65001