{"version": "0.2.0", "compounds": [{"name": "Main + renderer", "configurations": ["Main", "<PERSON><PERSON><PERSON>"], "stopAll": true}], "configurations": [{"name": "<PERSON><PERSON><PERSON>", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}"}, {"name": "Main", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "args": [".", "--remote-debugging-port=9222"], "outputCapture": "std", "console": "integratedTerminal"}]}