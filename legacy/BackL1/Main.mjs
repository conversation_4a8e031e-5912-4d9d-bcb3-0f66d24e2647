'use strict';
import {HttpAPIStart} from "./HttpServer/HttpAPI.mjs";
import {LoggerFactory as _LoggerFactory} from "../Utils/Logger";
import "./API/main.mjs";

const Main = (config) => {
    const LoggerFactory = new _LoggerFactory(config.logLevel, config.dirname);
    HttpAPIStart({host: config.host, port: config.port}, LoggerFactory.Neo("HttpAPI"));
    // TODO 异常捕捉
    // const LoggerMain = LoggerFactory.Neo("Main");
    // process.on("uncaughtException", err => {
    //     LoggerMain.Error("uncaughtException: " + err);
    // });
    // throw new Error("Test Error"); // 测试异常捕捉
};

export {Main};
export default Main;