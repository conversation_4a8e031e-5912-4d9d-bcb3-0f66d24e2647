'use strict';
import {createServer as HTTP_Create} from "node:http";
import {Defined, IsString, IsNumUI} from "../../Utils/Defined.mjs";
import {_PrintStack} from "../../Utils/PrintStack.mjs";
import {Stream2Buffer} from "../../Utils/Stream2Buffer.mjs";
import {favicon} from "./Utils/favicon.mjs";
import {scopeGet as routerGet, scopePost as routerPost} from "./APIRouter/scope.mjs";
import {H404} from "./Utils/H404.mjs";
import {H500} from "./Utils/H500.mjs";

const _500 = (request, response, e) => {
    response.statusCode = 500;
    response.setHeader("Content-Type", "text/html; charset=utf-8");
    response.write(H500[0] + e + H500[1]);
    response.end();
};

const _404 = (request, response) => {
    response.statusCode = 404;
    response.setHeader("Content-Type", "text/html; charset=utf-8");
    response.write(H404[0] + request.url + H404[1]);
    response.end();
};

const HttpAPIStart = (param, logger) => {
    if(!Defined(param) || !IsString(param.host) || !IsNumUI(param.port)) _PrintStack();
    const server = HTTP_Create();
    server.on("request", (request, response) => {
        const url = request.url ?? "";
        const routeUrl = url.split('?')[0];
        logger.Info(`Request URL: ${url}`);
        if("GET" === request.method) {
            if("/favicon.ico" === url) {
                response.statusCode = 200;
                response.setHeader("Content-Type", "x-icon");
                response.write(favicon);
                response.end();
            }
            else if(routerGet.has(routeUrl)) {
                routerGet.get(routeUrl).Call(request, response, logger).then(code => {
                    response.statusCode = code;
                    response.end();
                    logger.Info(`Response URL: ${url} : GET-${code}`);
                }).catch(e => {
                    _500(request, response, e);
                    logger.Error(`Response URL: ${url} : GET-ROUTE-500 : ${e}`);
                });
            }
            else {
                _404(request, response);
                logger.Info(`Response URL: ${url} : GET-404`, 1);
            }
        }
        else if("POST" === request.method) {
            if(routerPost.has(routeUrl)) {
                Stream2Buffer(request).then(buffer => {
                    routerPost.get(routeUrl).Call(request, response, buffer, logger).then(code => {
                        response.statusCode = code;
                        response.end();
                        logger.Info(`Response URL: ${url} : POST-${code}`);
                    }).catch(e => {
                        _500(request, response, e);
                        logger.Error(`Response URL: ${url} : POST-ROUTE-500 : ${e}`);
                    });
                }).catch(e => {
                    _500(request, response, e);
                    logger.Error(`Response URL: ${url} : POST-S2B-500 : ${e}`);
                });
            }
            else {
                _404(request, response);
                logger.Info(`Response URL: ${url} : POST-404`, 1);
            }
        }
        return;
    });
    server.listen(param.port, param.host, () => {
        logger.Info(`服务启动于: http://${param.host}:${param.port}/`, 10);
    });
    return server;
};

export {HttpAPIStart};
export default HttpAPIStart;