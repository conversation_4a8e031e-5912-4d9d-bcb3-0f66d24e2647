'use strict';
import {IsFunc} from "../../../Utils/Defined.mjs";
import {_PrintStack} from "../../../Utils/PrintStack.mjs";
// import {IncomingMessage, ServerResponse} from "node:http";

const APIRouterType = {
    GET: 1,
    POST: 2
};

class APIRouterGet {
    // protected _call: (request: IncomingMessage, response: ServerResponse, logger: ILogger) => Promise<GLint>;
    constructor(call) {
        if(!IsFunc(call)) _PrintStack();
        this._type = APIRouterType.GET;
        this._call = call;
    }
    get type() {return APIRouterGet._type;}
    get Call() {return this._call;}
}

class APIRouterPost {
    // protected _call: (request: IncomingMessage, response: ServerResponse, requestData: Buffer, logger: ILogger) => Promise<GLint>;
    constructor(call) {
        if(!IsFunc(call)) _PrintStack();
        this._call = call;
        this._type = APIRouterType.POST;
    }
    get type() {return APIRouterPost._type;}
    get Call() {return this._call;}
}

export {APIRouterGet, APIRouterPost, APIRouterType};
export default APIRouterGet;