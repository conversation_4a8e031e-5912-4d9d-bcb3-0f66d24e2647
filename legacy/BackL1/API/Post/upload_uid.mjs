'use strict';
import {scopePost} from "../../HttpServer/APIRouter/scope.mjs";
import {APIRouterPost} from "../../HttpServer/APIRouter/APIRouter.mjs";

// 列出所有数据源
const f = async (request, response, requestData, logger) => {
    response.setHeader("Content-Type", "application/json");
    const responseJson = {
        success: true,
        code: 200,
        msg: null,
        result: null
    };
    const requestJson = JSON.parse(requestData.toString("utf-8"));
    logger.Info("客户端上传UID: " + requestJson);
    // TODO
    console.log("uid/muid", requestJson.uid, requestJson.muid);
    responseJson.result = true;
    response.write(JSON.stringify(responseJson));
    return responseJson.code;
};

const executor = new APIRouterPost(f);
scopePost.set("/userinfo/upload", executor);

export {executor};
export default executor;