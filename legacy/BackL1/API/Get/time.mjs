'use strict';
import {DateEx} from "../../../Utils/DateEx.mjs";
import {scopeGet} from "../../HttpServer/APIRouter/scope.mjs";
import {APIRouterGet} from "../../HttpServer/APIRouter/APIRouter.mjs";

// 测试服务(返回时间)
const f = (request, response, logger) => {
    return new Promise((resolve, reject) => {
        const c = new DateEx();
        response.setHeader("Content-Type", "text/html; charset=utf-8");
        response.write(c.Format("yyyy-MM-dd hh:mm:ss"));
        resolve(200);
    });
};

const executor = new APIRouterGet(f);
scopeGet.set("/time", executor);

export {executor};
export default executor;