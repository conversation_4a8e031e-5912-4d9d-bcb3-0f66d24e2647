// rollup -c 见package.json
const _Terser = require("@rollup/plugin-terser").default;
const {BUILD, MINIFY, TARGET} = process.env;
const minified = MINIFY === "true";
const production = BUILD === "production";

module.exports = {
    input: "./BackL1/Main.mjs",
    output: {
        name: "EchoBackL1",
        // dir: './',
        file: minified ? "./BuildL1/EchoBackL1.mjs" : "./BuildL1/EchoBackL1.unmin.mjs",
        // cjs(commonjs) or iife/umd(browser)
        format: "es"
    },
    external: ["node:fs", "node:http", "node:http2", "node:stream", "node:url", "node:path", "node:crypto"],
    plugins: [
        minified ? _Terser({
            compress: {
                pure_getters: true,
                passes: 3
            },
            mangle: {
                reserved: ["h"]
            },
            keep_classnames: false
        }) : false
    ]
};