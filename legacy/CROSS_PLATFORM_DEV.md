# EchoWave Windows Client - 跨平台开发指南

这个项目是专门为 Windows 平台设计的 Electron 应用，但为了方便开发和测试，我们添加了跨平台支持。

## 问题说明

原始错误：
```
Error: dlopen(...virt-detect.win32-x64-msvc-B3lHHLAE.node, 0x0001): tried: '...' (slice is not valid mach-o file)
```

这个错误是因为项目使用了 Windows 特定的原生模块 `virt-detect`，该模块只编译了 Windows 版本，无法在 macOS 或 Linux 上运行。

## 解决方案

我们实现了以下解决方案：

### 1. 原生模块包装器
- 修改了 `externals/virt-detect/index.mjs`，添加了平台检测和 mock 实现
- 在非 Windows 平台上提供模拟功能

### 2. 操作模块跨平台支持
- 修改了各个操作模块（CheckVirtual, CheckWSL, GetUID 等）
- 添加了 mock 实现用于非 Windows 平台

### 3. 开发配置系统
- 创建了 `dev-config.mjs` 配置文件
- 创建了 `dev-start.mjs` 启动脚本

## 使用方法

### 在 macOS 上开发

```bash
# 方法1：使用跨平台启动脚本
npm run dev:cross-platform

# 方法2：直接使用 macOS 脚本
npm run dev:mac

# 方法3：手动设置环境变量
MOCK_MODE=1 npm run dev
```

### 在 Linux 上开发

```bash
# 方法1：使用跨平台启动脚本
npm run dev:cross-platform

# 方法2：直接使用 Linux 脚本
npm run dev:linux

# 方法3：手动设置环境变量
MOCK_MODE=1 npm run dev
```

### 在 Windows 上开发（正常模式）

```bash
# 正常开发模式
npm run dev

# 或者使用跨平台脚本（会自动检测为 Windows）
npm run dev:cross-platform
```

## Mock 模式说明

在非 Windows 平台上，应用会运行在 Mock 模式下：

- **虚拟化检查**: 总是返回支持
- **WSL 检查**: 返回未安装状态
- **镜像检查**: 返回未安装状态
- **操作系统检查**: 返回不符合要求（因为不是 Windows）
- **PowerShell 检查**: 返回未安装状态
- **机器 ID**: 基于系统信息生成模拟 ID

## 注意事项

1. **仅用于开发**: 跨平台支持仅用于开发和测试，生产环境仍需要 Windows
2. **功能限制**: 在非 Windows 平台上，WSL 相关功能无法正常工作
3. **UI 测试**: 可以用于测试 UI 界面和基本逻辑
4. **环境检查**: 环境检查项会显示为未满足状态，这是正常的

## 开发建议

1. 在 macOS/Linux 上主要用于 UI 开发和调试
2. Windows 特定功能需要在 Windows 环境中测试
3. 使用 Mock 模式时注意日志输出，了解哪些功能被模拟了

## 故障排除

如果仍然遇到问题：

1. 确保使用了正确的启动脚本
2. 检查环境变量是否正确设置
3. 查看控制台日志中的 `[DEV-*]` 消息
4. 确认 `MOCK_MODE` 环境变量已设置

## 文件修改说明

以下文件已被修改以支持跨平台：

- `externals/virt-detect/index.mjs` - 添加平台检测和 mock
- `BackL0/Operations/CheckVirtual.mjs` - 添加 mock 实现
- `BackL0/Operations/CheckWSL.mjs` - 添加平台检测
- `BackL0/Operations/GetUID.mjs` - 添加 mock 机器 ID 生成
- `Utils/ExecCoding.mjs` - 添加平台检测
- `package.json` - 添加新的开发脚本

新增文件：
- `dev-config.mjs` - 开发配置
- `dev-start.mjs` - 跨平台启动脚本
- `CROSS_PLATFORM_DEV.md` - 本文档
