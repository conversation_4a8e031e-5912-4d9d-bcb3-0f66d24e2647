# EchoWave Client 记住登录状态功能说明

## 功能概述

EchoWave Client 现在支持记住登录状态功能，用户登录一次后可以选择保存登录信息，下次启动应用时将自动登录，无需重新输入账号密码。

## 功能特性

### 1. **安全存储**
- 登录凭据保存在Electron的userData目录中
- 只保存手机号和token，不保存明文密码
- 数据使用JSON格式存储，带有时间戳和过期检查
- 默认30天过期，过期后自动清除

### 2. **自动登录**
- 启动应用时自动检查是否有保存的有效凭据
- 有效凭据存在且启用自动登录时，直接跳转到主页面
- 无效或过期的凭据会被自动清除

### 3. **用户控制**
- 登录时可选择是否记住登录状态
- 可随时禁用/启用自动登录功能
- 可手动清除所有保存的登录数据
- 退出登录时自动清除保存的数据

## 使用方法

### 首次使用

1. **启动应用**，进入登录页面
2. **输入账号密码**
3. **勾选"记住登录状态"**复选框
4. **点击登录**

登录成功后，应用会保存您的登录信息。

### 自动登录

下次启动应用时：
- 如果有有效的保存凭据且启用了自动登录，应用会自动跳转到主页面
- 如果凭据已过期或无效，会自动清除并显示登录页面
- 如果禁用了自动登录，会显示登录页面但自动填充手机号

### 管理登录设置

可以通过以下方式管理登录设置：

1. **查看当前设置**
   ```javascript
   const credentials = await window.API.loadUserCredentials()
   ```

2. **启用/禁用自动登录**
   ```javascript
   await window.API.updateAutoLoginSetting(true/false)
   ```

3. **清除所有保存数据**
   ```javascript
   await window.API.clearUserCredentials()
   ```

## 存储位置

用户凭据保存在以下位置：

- **Windows**: `%APPDATA%/EchoWave/user-credentials.json`
- **macOS**: `~/Library/Application Support/EchoWave/user-credentials.json`
- **Linux**: `~/.config/EchoWave/user-credentials.json`

## 数据格式

保存的数据格式如下：

```json
{
  "phone": "13800138000",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "savedAt": 1703123456789,
  "autoLogin": true
}
```

## 安全考虑

### 1. **数据保护**
- 不保存明文密码，只保存服务端返回的token
- token有过期时间限制，定期自动清理
- 数据存储在用户本地目录，其他用户无法访问

### 2. **自动清理**
- 30天后自动过期清除
- 退出登录时主动清除
- 检测到无效token时自动清除

### 3. **用户控制**
- 用户可随时禁用自动登录
- 用户可随时清除所有保存数据
- 登录时可选择不保存数据

## API 接口

### 主进程API

```javascript
// 保存用户凭据
saveUserCredentials(data: {phone: string, token: string, autoLogin: boolean}): Promise<boolean>

// 读取用户凭据
loadUserCredentials(): Promise<{phone: string, token: string, savedAt: number, autoLogin: boolean} | null>

// 清除用户凭据
clearUserCredentials(): Promise<boolean>

// 更新自动登录设置
updateAutoLoginSetting(autoLogin: boolean): Promise<boolean>
```

### 渲染进程调用

```javascript
// 保存登录信息
await window.API.saveUserCredentials({
  phone: '13800138000',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  autoLogin: true
})

// 加载保存的信息
const credentials = await window.API.loadUserCredentials()

// 清除保存的信息
await window.API.clearUserCredentials()

// 更新自动登录设置
await window.API.updateAutoLoginSetting(false)
```

## 错误处理

系统会自动处理以下异常情况：

1. **文件读写错误**：记录错误日志但不影响正常功能
2. **数据格式错误**：自动删除损坏的数据文件
3. **权限问题**：提示用户检查文件权限
4. **过期数据**：自动清除过期的凭据

## 注意事项

1. **隐私保护**：请在个人设备上使用此功能，避免在公共设备上保存登录信息
2. **定期清理**：建议定期清理保存的登录数据，特别是在不再使用设备时
3. **网络安全**：token的有效性依赖于服务端验证，请确保网络连接安全
4. **备份恢复**：重装系统或清理用户数据会导致保存的登录信息丢失

## 故障排除

### 自动登录失败
1. 检查网络连接是否正常
2. 确认token是否已过期
3. 检查服务端登录状态
4. 尝试清除数据重新登录

### 无法保存登录信息
1. 检查磁盘空间是否充足
2. 确认应用有写入权限
3. 检查userData目录是否存在
4. 查看控制台错误信息

### 设置无法生效
1. 重启应用
2. 检查保存的数据文件
3. 清除数据重新设置
4. 查看应用日志

## 更新日志

- **v1.0.0**: 首次发布记住登录状态功能
- 支持token安全存储
- 支持自动登录和手动控制
- 支持数据过期自动清理 