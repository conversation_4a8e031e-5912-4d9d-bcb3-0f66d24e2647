// import {app, BrowserWindow} from "electron";
import { fileURLToPath } from "node:url";
import path, { join } from "node:path";
import InitAPI from "../../BackL0/InitAPI.mjs";
import packageJson from "../../package.json" with { type: "json" };
import {
    CheckAutoStart,
    DisableAutoStart,
    EnableAutoStart,
} from "../../BackL0/Operations/AutoStart.mjs";
import { CheckMirrorNodeIsIdle } from "../../BackL0/Operations/CheckMirrorNodeStatus.mjs";
import { MirrorNodeID } from "../../BackL0/Operations/MirrorNodeID.mjs";
import { CheckMirror } from "../../BackL0/Operations/CheckMirror.mjs";
import fs from "fs";
import { autoUpdater, CancellationToken } from "electron-updater";
import {
    app,
    BrowserWindow,
    globalShortcut,
    ipcMain,
    Menu,
    nativeImage,
    shell,
    Tray,
    powerMonitor,
} from "electron";
import { electronApp, is, optimizer } from "@electron-toolkit/utils";
import {
    setLimitedWSLConfig,
    unsetLimitedWSLConfig,
} from "../../Utils/limitedWSLConfig.mjs";
import icon from "../../resources/icon.png?asset";
import axios from "axios";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const __execname = path.dirname(process.execPath);
const __appdatadir = app.getPath("userData");
/**
 * @type {'development' | 'production' | 'testing' | 'staging'}
 */
const mode = (() => {
    const mode = import.meta.env.APP_ENV.trim();
    console.log("ENVS", {
        APP_ENV: import.meta.env.APP_ENV,
        VITE_MODE: import.meta.env.VITE_MODE,
        MODE: import.meta.env.MODE,
    });
    const aliasMap = {
        prod: "production",
        release: "production",
        production: "production",
        test: "testing",
        testing: "testing",
        dev: "development",
        debug: "development",
        development: "development",
        // preview: "preview",
    };
    return aliasMap[mode] ? aliasMap[mode] : "development";
})();

const devConfig = {
    wslSource: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi",
    updateServer: "http://************:8000/releases",
    apiUrl: "http://api.echowave.cn:8090/api/",
    frontendApiUrl: "http://api.echowave.cn:8090/api/",
    portalUrl: "http://localhost:3000/supplierRevenue",
    clientName: "EchoWave 客户端(开发版)",
};
const prodConfig = {
    wslSource: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi",
    updateServer: "http://cdn.echowave.cn/releases",
    apiUrl: "http://api.echowave.cn:8090/api/",
    frontendApiUrl: "http://api.echowave.cn:8090/api/",
    portalUrl: "http://api.echowave.cn:8090/supplierRevenue",
    clientName: "EchoWave 客户端",
};
const testConfig = {
    wslSource: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi",
    updateServer: "http://************/releases",
    apiUrl: "http://api.echowave.cn:8090/api/",
    frontendApiUrl: "http://api.echowave.cn:8090/api/",
    portalUrl: "http://api.echowave.cn:8090/supplierRevenue",
    clientName: "EchoWave 客户端(测试版)",
};
const envConfigMap = {
    production: prodConfig,
    testing: testConfig,
    development: devConfig,
};

const config = {
    // TODO 临时路径修改
    // dirname: __execname + "/../../../",
    dirname: __appdatadir,
    userdataDir: __appdatadir,
    logLevel: 10,
    // TODO
    logEncryptKey: undefined,
    // logEncryptKey: "echo_wave",
    vport: 11121,
    execPath: process.execPath,
    ver: packageJson.version,
    wslVer: "2.4.13.0",
    wslCheckVer: "2.0.4",
    wslSource: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi",
    wslSourceHash: "E3E7062C39D05EF4D7D63D7AF5C61126",
    appName: "EchoWave", // 使用与exe文件名一致的名称
    distroName: "EchoWaveUbuntu",
    productName: packageJson.build.productName,
    updateServer: "",
    apiUrl: "",
    frontendApiUrl: "",
    portalUrl: "",
    clientName: "",
    mode: mode,
    /**
     * @type {Credentials | undefined}
     */
    credentials: undefined,
    /**
     * @type {BrowserWindow | undefined}
     */
    mainWindow: undefined,
    ...envConfigMap[mode],
};

// console.log("exe:", config.dirname + "/" + config.productName);

// const createWindow = () => {
//     const win = new BrowserWindow({
//         width: 800,
//         height: 600,
//         webPreferences: {
//             preload: path.join(__dirname + "/BackL0", "preload.js")
//         }
//     });
//     win.loadFile("index.html");
//     // 开启控制台
//     // win.webContents.openDevTools();
// };

// app.whenReady().then(() => {
//     InitAPI(config);
//     createWindow();
//     app.on("activate", () => {
//         if(BrowserWindow.getAllWindows().length === 0) createWindow();
//     });
// });

/**
 * 解析命令行参数
 * @param {Partial<CmdArgs>} defaultArgs
 * @returns
 */
function parseCommandLineArgs(defaultArgs = {}) {
    const args = defaultArgs;
    const argv = process.argv.slice(1);

    for (let i = 0; i < argv.length; i++) {
        const arg = argv[i];
        if (arg.startsWith("--")) {
            const parts = arg.substring(2).split("=");
            if (parts.length === 2) {
                args[parts[0]] = parts[1];
            } else {
                args[parts[0]] = true;
            }
        }
    }

    console.log("Command line arguments parsed:", args);
    if (args.username || args.password) {
        logger.Warn("用户名和密码参数已废弃，请勿使用");
    }
    return args;
}

/**
 * @typedef {Object} CmdArgs
 * @property {?string} username
 * @property {?string} password
 * @property {?string} autoUpdate
 * @property {?string} updateServer
 */

// 获取命令行参数
/**
 * @type {CmdArgs}
 */
const cmdArgs = parseCommandLineArgs({
    autoUpdate: "true",
});
/**
 * @type {UserSettings}
 */
const userSettings = {
    autoOrder: true,
};

// 记录应用启动时间
const appStartTime = Date.now();
console.log(
    "Application start time recorded:",
    new Date(appStartTime).toISOString(),
);

let windowWidth = 360;
let windowHeight = 585;
/**
 *
 * @type {BrowserWindow | null}
 */
let mainWindow = null;
let tray = null;
// Flag to track if task acceptance has been initiated
let taskAcceptanceInitiated = false;
// Flag to track if mirror start is in progress
let mirrorStartInProgress = false;

// 自动接单函数
async function autoStartTask(logger) {
    try {
        // Check if task acceptance has already been initiated to prevent multiple triggers
        if (taskAcceptanceInitiated) {
            logger.Info(
                "Task acceptance already initiated, skipping duplicate call",
            );
            return;
        }

        // Set the flag to indicate we're starting task acceptance
        taskAcceptanceInitiated = true;

        // 等待应用完全加载 - 增加等待时间
        logger.Info("Waiting for application to fully load...");
        await new Promise((resolve) => setTimeout(resolve, 15000)); // 增加到15秒

        logger.Info("Starting automatic task acceptance...");
        console.log("Command line arguments:", cmdArgs);

        // 检查当前页面是否是主页面
        logger.Info("Checking current page...");
        let currentURL = "";
        try {
            currentURL = await mainWindow.webContents.executeJavaScript(
                "window.location.href",
            );
        } catch (error) {
            logger.Error(`Failed to get current page URL: ${error}`);
            // 等待一会再重试
            await new Promise((resolve) => setTimeout(resolve, 5000));
            try {
                currentURL = await mainWindow.webContents.executeJavaScript(
                    "window.location.href",
                );
            } catch (retryError) {
                logger.Error(
                    `Failed to get current page URL on retry: ${retryError}`,
                );
                taskAcceptanceInitiated = false; // Reset flag on failure
                return;
            }
        }

        logger.Info(`Current page URL: ${currentURL}`);

        // 如果不在主页，尝试登录 - 保留登录逻辑
        let loginAttempted = false;
        if (!currentURL.includes("/main-page")) {
            loginAttempted = true;
            logger.Info("Not on main page, attempting to login...");

            // 检查命令行参数中是否有用户名和密码
            if (cmdArgs.username && cmdArgs.password) {
                // 尝试登录
                logger.Info(
                    `Attempting to login with username ${cmdArgs.username}...`,
                );
                handleLogin(mainWindow, cmdArgs.username, cmdArgs.password);

                // 等待登录跳转到主页
                logger.Info("Waiting for login redirect to main page...");
                let retries = 0;
                let loginSuccess = false;

                while (retries < 20) {
                    await new Promise((resolve) => setTimeout(resolve, 3000));

                    try {
                        const newURL =
                            await mainWindow.webContents.executeJavaScript(
                                "window.location.href",
                            );
                        logger.Info(`Checking URL (${retries}/20): ${newURL}`);

                        if (newURL.includes("/main-page")) {
                            logger.Info(
                                "Login successful, redirected to main page",
                            );
                            loginSuccess = true;
                            break;
                        }

                        // 如果仍在登录页，尝试再次登录
                        if (retries === 10) {
                            logger.Info(
                                "Login not yet successful, trying again",
                            );
                            handleLogin(
                                mainWindow,
                                cmdArgs.username,
                                cmdArgs.password,
                            );
                        }
                    } catch (error) {
                        logger.Error(`Error checking URL: ${error}`);
                    }

                    retries++;
                    logger.Info(
                        `Waiting for login redirect, attempt ${retries}/20...`,
                    );
                }

                if (!loginSuccess) {
                    logger.Error(
                        "Login redirect timeout, automatic task acceptance failed",
                    );
                    try {
                        const errorMsg = await mainWindow.webContents
                            .executeJavaScript(`
              const errorEl = document.querySelector('.el-message--error');
              errorEl ? errorEl.textContent : 'No error message found';
            `);
                        logger.Error(`Login error message: ${errorMsg}`);
                    } catch (e) {
                        logger.Error(`Unable to get login error message: ${e}`);
                    }
                    taskAcceptanceInitiated = false; // Reset flag on failure
                    return;
                }
            } else {
                logger.Error("No login credentials provided, cannot login");
                taskAcceptanceInitiated = false; // Reset flag on failure
                return;
            }
        }

        // 登录成功后，等待页面完全加载和准备
        logger.Info(
            "Login successful, waiting for page to fully load and prepare...",
        );
        await new Promise((resolve) => setTimeout(resolve, 10000)); // 在登录成功后再等待10秒

        // 使用智能环境检查监听器，不再固定等待2分钟
        logger.Info(
            "Starting intelligent task acceptance after successful login...",
        );

        // 通知渲染进程自动开始接单（使用智能监听器）
        if (mainWindow) {
            logger.Info(
                "Sending intelligent auto-start task message to renderer process",
            );
            mainWindow.webContents.send("auto-start-task");

            // 等待一段时间，确保渲染进程处理了消息
            logger.Info(
                "Waiting for renderer process to process the message...",
            );
            await new Promise((resolve) => setTimeout(resolve, 5000)); // 等待5秒

            logger.Info(
                "Auto-start task message sent successfully. Frontend intelligent watcher will handle the rest.",
            );

            // 移除主进程的按钮点击逻辑，由前端智能监听器处理
            // 前端的智能监听器会监控环境检查状态，一旦6项检查都通过就自动接单
        } else {
            logger.Error(
                "Main window does not exist, cannot send task acceptance message",
            );
        }
    } catch (error) {
        logger.Error(`Automatic task acceptance failed: ${error}`);
        console.error("Automatic task acceptance failed:", error);
        taskAcceptanceInitiated = false; // Reset flag on error
    }
}

// 检查并触发自动接单的函数
function checkAndTriggerAutoOrder(mainWindow, logger) {
    if (!mainWindow) {
        logger.Error("Main window does not exist，无法继续自动接单流程");
        return;
    }

    logger.Info("=== 向前端发送自动接单事件 ===");
    // 检查用户是否已经登录
    mainWindow.webContents.send("auto-start-task");
}

function createWindow(logger) {
    // Create the browser window.
    mainWindow = new BrowserWindow({
        width: windowWidth,
        height: windowHeight,
        title: "客户端",
        show: false,
        movable: true, // 禁止窗口拖动
        frame: true, // 无边框
        resizable: false, // 禁止调整窗口大小
        fullscreenable: false, // 禁用进入全屏
        maximizable: false, // 禁用最大化按钮
        autoHideMenuBar: true,
        skipTaskbar: true, // 不在任务栏显示
        icon: icon,
        useContentSize: true, // 确保width/height指的是内容区域大小
        ...(process.platform === "linux" ? { icon } : {}),
        webPreferences: {
            preload: join(__dirname, "../preload/index.js"),
            sandbox: false,
        },
    });
    config.mainWindow = mainWindow;

    // 只在开发环境打开DevTools
    //if (is.dev) {
    const accelerator =
        process.platform === "darwin" ? "Alt+Command+I" : "Ctrl+Shift+I";
    const registered = globalShortcut.register(accelerator, () => {
        if (mainWindow) {
            mainWindow.webContents.toggleDevTools({ mode: "detach" });
        }
    });
    mainWindow.setFullScreenable(false);

    if (!registered) {
        logger.Error("DevTools shortcut registration failed");
    } else {
        logger.Info("DevTools shortcut registered: " + accelerator);
        app.on("will-quit", () => {
            globalShortcut.unregister(accelerator);
        });
    }
    //}

    mainWindow.on("ready-to-show", () => {
        mainWindow.show();

        // 如果有用户名和密码参数，则尝试自动登录
        if (cmdArgs.username && cmdArgs.password) {
            console.log("Login parameters detected, preparing auto-login...");
            handleLogin(mainWindow, cmdArgs.username, cmdArgs.password);
        }
        // 注意：自动接单的触发现在通过 frontend-listeners-ready 事件处理
        console.log(
            "Window ready, waiting for frontend listeners ready notification...",
        );
    });

    mainWindow.webContents.setWindowOpenHandler((details) => {
        shell.openExternal(details.url);
        return { action: "deny" };
    });

    // 处理窗口关闭事件 - 隐藏窗口而不是退出应用
    mainWindow.on("close", (event) => {
        if (is.dev) {
            return true;
        }
        if (!app.isQuitting) {
            event.preventDefault();
            mainWindow.hide();
            return false;
        }
        return true;
    });

    // HMR for renderer base on electron-vite cli.
    // Load the remote URL for development or the local html file for production.
    if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
        mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
    } else {
        mainWindow.loadFile(join(__dirname, "../renderer/index.html"));
    }
}

// 创建系统托盘
async function createTray(logger) {
    // 获取正确的图标路径，处理开发环境和生产环境
    let trayIcon;
    if (is.dev) {
        // 开发环境使用导入的图标
        trayIcon = nativeImage.createFromPath(path.resolve(icon));
        trayIcon = trayIcon.resize({ width: 16, height: 16 });
    } else {
        // 生产环境使用绝对路径
        const iconPath = join(process.resourcesPath, "icon.png");
        if (fs.existsSync(iconPath)) {
            trayIcon = nativeImage.createFromPath(iconPath);
        } else {
            // 备用路径，在应用目录中查找
            const appIconPath = join(__dirname, "../../resources/icon.png");
            if (fs.existsSync(appIconPath)) {
                trayIcon = nativeImage.createFromPath(appIconPath);
            } else {
                logger.Error("找不到托盘图标文件");
                // 创建一个空白图标防止崩溃
                trayIcon = nativeImage.createEmpty();
            }
        }
    }
    // 使用应用图标作为托盘图标
    tray = new Tray(trayIcon);
    tray.setToolTip("EchoWave Client");

    // 初始化并设置托盘菜单
    await updateTrayMenu(logger);

    // 点击托盘图标切换窗口显示状态
    tray.on("click", () => {
        if (mainWindow) {
            if (mainWindow.isVisible()) {
                mainWindow.hide();
            } else {
                mainWindow.show();
            }
        }
    });
}

// 更新托盘菜单（支持动态状态）
async function updateTrayMenu(logger) {
    if (!tray) return;

    // 获取当前开机自启动状态
    // let autoStartEnabled = false;
    // try {
    //   autoStartEnabled = await CheckAutoStart(logger, config.appName);
    // } catch (error) {
    //   logger.Error("检查开机自启动状态失败: " + error);
    // }

    // 创建动态菜单
    const contextMenu = Menu.buildFromTemplate([
        {
            label: "显示窗口",
            click: () => {
                if (mainWindow) {
                    mainWindow.show();
                }
            },
        },
        {
            label: "隐藏窗口",
            click: () => {
                if (mainWindow) {
                    mainWindow.hide();
                }
            },
        },
        {
            label: "检查更新",
            click: async () => {
                logger.Info("从托盘处触发检查更新");
                // const result = await autoUpdater.checkForUpdates();
                // console.log('结果', result)
                mainWindow?.webContents.send("immediate-check");
            },
        },
        // { type: "separator" },
        // {
        //   label: "自动接单",
        //   type: "checkbox",
        //   checked: userSettings.autoOrder,
        //   click: async (menuItem) => {
        //     saveUserSettings(
        //       {
        //         autoOrder: menuItem.checked,
        //       },
        //       logger,
        //     );
        //   },
        // },
        // {
        //   label: "开机自启动",
        //   type: "checkbox",
        //   checked: autoStartEnabled,
        //   click: async (menuItem) => {
        //     await toggleAutoStart(menuItem.checked, logger);
        //   },
        // },
        { type: "separator" },
        {
            label: "退出",
            click: () => {
                logger.Info("从托盘处退出应用程序");
                app.isQuitting = true;
                app.quit();
            },
        },
    ]);

    // 设置托盘菜单
    tray.setContextMenu(contextMenu);
}

// 切换开机自启动状态
async function toggleAutoStart(enable, logger) {
    try {
        if (enable) {
            // 启用开机自启动
            const execPath = process.execPath.replace(/\.exe$/, "");
            await EnableAutoStart(logger, config.appName, execPath);
            logger.Info("开机自启动已启用");
        } else {
            // 禁用开机自启动
            await DisableAutoStart(logger, config.appName);
            logger.Info("开机自启动已禁用");
        }

        // 更新菜单状态
        // await updateTrayMenu(logger);
    } catch (error) {
        logger.Error("切换开机自启动失败:", error);

        // 发生错误时，恢复菜单状态
        // setTimeout(async () => {
        //   await updateTrayMenu(logger);
        // }, 500);
    }
}

const gotTheLock = app.requestSingleInstanceLock();

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
    if (!gotTheLock) {
        console.log("已存在相同实例");
        app.quit();
        return;
    }
    // 初始化API，获取现有的logger
    const loggerFactory = InitAPI(config);
    loggerFactory
        .Neo("Main.App")
        .Info(
            `${config.appName} 启动中, 客户端版本: ${config.ver}, 内置 WSL 版本: ${config.wslVer}，模式：${config.mode}`,
        );
    const logger = loggerFactory.Neo("Main.AutoTask");
    registerAutoUpdate(loggerFactory.Neo("Main.AutoUpdate"));
    loadUserSettings(loggerFactory.Neo("Main.UserSettings"));
    registerAxiosInspector(loggerFactory.Neo("Main.AxiosInspector"));
    registerIdleWatcher(loggerFactory.Neo("Main.IdleWatch"));
    const waitLimitedWSLConfig = setLimitedWSLConfig(
        loggerFactory.Neo("Main.WSLConfig"),
    );

    // Set app user model id for windows
    electronApp.setAppUserModelId("com.echowave");

    // Default open or close DevTools by F12 in development
    // and ignore CommandOrControl + R in production.
    // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
    app.on("browser-window-created", (_, window) => {
        optimizer.watchWindowShortcuts(window);
    });

    // IPC test
    ipcMain.on("ping", () => console.log("pong"));

    // 添加IPC处理器，让渲染进程可以触发自动接单
    ipcMain.handle("auto-start-task", async () => {
        if (!taskAcceptanceInitiated) {
            logger.Info(
                "Received auto-start-task IPC request, initiating task acceptance",
            );
            await autoStartTask(logger);
            return true;
        } else {
            logger.Info(
                "Task acceptance already initiated, ignoring IPC request",
            );
            return false;
        }
    });

    // 添加IPC处理器，重置自动接单状态
    ipcMain.handle("reset-task-acceptance", () => {
        logger.Info("Resetting task acceptance initiated flag");
        taskAcceptanceInitiated = false;
        mirrorStartInProgress = false;
        return true;
    });

    // 用户凭据相关的IPC处理器
    ipcMain.handle("save-user-credentials", async (event, data) => {
        return saveUserCredentials(data);
    });

    ipcMain.handle("load-user-credentials", async () => {
        return loadUserCredentials();
    });

    ipcMain.handle("clear-user-credentials", async () => {
        return clearUserCredentials();
    });

    ipcMain.handle("update-auto-login-setting", async (event, autoLogin) => {
        return updateAutoLoginSetting(autoLogin);
    });
    ipcMain.handle("load-user-settings", () => {
        return userSettings;
    });

    // 添加保存用户设置的处理器
    ipcMain.handle("save-user-settings", async (event, settings) => {
        const logger = loggerFactory.Neo("Main.UserSettings");
        return saveUserSettings(settings, logger);
    });

    // 添加切换开机自启动的处理器
    ipcMain.handle("toggle-auto-start", async (event, enable) => {
        const logger = loggerFactory.Neo("Main.AutoStart");
        await toggleAutoStart(enable, logger);
        return await CheckAutoStart(logger, config.appName, config.execPath);
    });

    // 添加获取开机自启动状态的处理器
    ipcMain.handle("get-auto-start-status", async () => {
        const logger = loggerFactory.Neo("Main.AutoStart");
        try {
            return await CheckAutoStart(
                logger,
                config.appName,
                config.execPath,
            );
        } catch (error) {
            logger.Error("检查开机自启动状态失败: " + error);
            return false;
        }
    });

    // 监听登录成功事件
    ipcMain.on("login-success", () => {
        logger.Info("收到登录成功事件，准备开始自动接单");
        if (!taskAcceptanceInitiated) {
            logger.Info(
                "Auto-start parameter is true and task not yet initiated, starting task acceptance",
            );
            setTimeout(() => {
                autoStartTask(logger);
            }, 2000);
        } else if (taskAcceptanceInitiated) {
            logger.Info(
                "Task acceptance already initiated, skipping login-success trigger",
            );
        }
    });

    // 监听前端监听器就绪事件
    // 前端 MainPage 页面挂载，等待 1s 发送该事件，同时注册相关的事件，
    // 自动登录链：前端 MainPage 挂载 -> 等待 1s -> 发送该事件 -> 触发 auto-start-task 事件 -> 切换接单状态
    ipcMain.on("frontend-listeners-ready", () => {
        logger.Info("已接收到渲染进程监听器就绪事件，触发自动接单");

        logger.Info("Triggering intelligent auto-start");
        triggerIntelligentAutoOrder(logger);
    });

    // 检查是否是开机自启动
    function isAutoStartOnBoot() {
        // 检查是否没有命令行参数但应用是开机自启动
        // 这里可以通过检查启动方式或其他标识来判断
        const hasCommandLineArgs = process.argv.length > 2;
        const isFromAutoStart =
            !hasCommandLineArgs && process.platform === "win32";

        logger.Info("Checking if auto-start on boot:", {
            hasCommandLineArgs,
            isFromAutoStart,
            argv: process.argv,
        });

        return isFromAutoStart;
    }

    // 智能自动接单触发器
    function triggerIntelligentAutoOrder(logger) {
        logger.Info("=== 开始自动接单流程 ===");
        checkAndTriggerAutoOrder(mainWindow, logger);
    }

    await waitLimitedWSLConfig;
    createWindow(loggerFactory.Neo("Main.Window"));
    await createTray(loggerFactory.Neo("Main.Tray"));

    app.on("activate", function () {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
    app.on("will-quit", function () {
        unsetLimitedWSLConfig(loggerFactory.Neo("Main.WSLConfig"));
    });
});

// 修改退出行为，确保正确处理托盘退出
app.on("window-all-closed", () => {
    if (process.platform !== "darwin") {
        app.quit();
    }
});
// 有第二个实例尝试启动（例如用户双击 .exe）让主窗口激活
app.on("second-instance", () => {
    if (mainWindow) {
        mainWindow.show();
        mainWindow.focus();
    }
});
// 注册一个“同步”通道
ipcMain.on("get-config-sync", (event) => {
    // 直接把整个对象挂到 returnValue 上（会被序列化传回渲染）
    event.returnValue = {
        apiUrl: config.frontendApiUrl,
        portalUrl: config.portalUrl,
        clientName: config.clientName,
    };
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.

// 添加登录处理函数
function handleLogin(mainWindow, username, password) {
    if (!mainWindow || !username || !password) {
        console.error("Login parameters incomplete:", {
            windowExists: !!mainWindow,
            username,
            password: password ? "provided" : "not provided",
        });
        return;
    }

    console.log("Preparing auto-login, parameters:", {
        username,
        passwordProvided: !!password,
    });

    // 等待确保页面完全加载
    setTimeout(() => {
        console.log("Checking if user is already logged in...");

        // 先检查用户是否已经登录
        mainWindow.webContents
            .executeJavaScript(
                `
      (function() {
        console.log('Checking login status...');
        
        // 检查当前URL是否已经在主页面
        const currentUrl = window.location.href;
        console.log('Current URL:', currentUrl);
        
        // 检查localStorage中是否有有效的token
        const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
        const uId = localStorage.getItem('uId');
        
        console.log('Stored credentials:', {
          hasAuthToken: !!authToken,
          hasUId: !!uId,
          isOnMainPage: currentUrl.includes('/main-page')
        });
        
        if (currentUrl.includes('/main-page') && (authToken || uId)) {
          console.log('User is already logged in');
          return 'already_logged_in';
        } else if (currentUrl.includes('/login') || currentUrl.includes('/') && !currentUrl.includes('/main-page')) {
          console.log('User is on login page, need to login');
          return 'need_login';
        } else {
          console.log('Unknown state');
          return 'unknown';
        }
      })()
    `,
            )
            .then((loginStatus) => {
                console.log("Login status check result:", loginStatus);

                if (loginStatus === "already_logged_in") {
                    console.log(
                        "User is already logged in, auto-start will be handled by frontend-ready event",
                    );
                    // 用户已经登录，自动接单将由前端就绪事件处理
                    // 不在这里直接触发，避免时机问题
                } else if (loginStatus === "need_login") {
                    console.log(
                        "User needs to login, executing login script...",
                    );
                    // 用户需要登录，执行登录脚本
                    executeLoginScript(mainWindow, username, password);
                } else {
                    // 未知状态，等待一段时间后重试
                    console.log(
                        "Unknown login state, retrying in 2 seconds...",
                    );
                    setTimeout(() => {
                        handleLogin(mainWindow, username, password);
                    }, 2000);
                }
            })
            .catch((err) => {
                console.error("Login status check error:", err);
                // 出错时执行登录脚本
                executeLoginScript(mainWindow, username, password);
            });
    }, 3000);
}

// 执行登录脚本的函数
function executeLoginScript(mainWindow, username, password) {
    console.log("Starting login script...");

    // 执行简单直接的登录脚本
    mainWindow.webContents
        .executeJavaScript(
            `
    (function() {
      console.log('Running simple login script');
      
      // 查找用户名输入框
      const usernameInput = document.querySelector('input[placeholder*="手机号码"]') || 
                            document.querySelector('input[type="text"]');
      
      // 查找密码输入框
      const passwordInput = document.querySelector('input[type="password"]');
      
      // 查找登录按钮
      const loginButton = document.querySelector('.el-button') || 
                         document.querySelector('button');
      
      console.log('Form elements found:', {
        usernameInput: usernameInput ? true : false,
        passwordInput: passwordInput ? true : false,
        loginButton: loginButton ? true : false
      });
      
      if (usernameInput && passwordInput && loginButton) {
        // 直接填充用户名
        console.log('Filling username');
        usernameInput.value = '${username}';
        usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        // 直接填充密码
        console.log('Filling password');
        passwordInput.value = '${password}';
        passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        // 稍等片刻确保表单填充生效
        setTimeout(() => {
          // 点击登录按钮
          console.log('Clicking login button');
          loginButton.click();
          
          // 登录成功后通知主进程
          setTimeout(() => {
            if (window.location.href.includes('/main-page')) {
              console.log('Login successful, redirected to main page');
              window.electron.ipcRenderer.send('login-success');
              return 'Login successful';
            } else {
              console.log('Still on login page, login may have failed');
              return 'Login may have failed';
            }
          }, 3000);
        }, 500);
        
        return 'Login form filled and submitted';
      } else {
        console.error('Could not find all required form elements');
        return 'Could not find all required form elements';
      }
    })();
  `
                .replace(/\$\{username\}/g, username)
                .replace(/\$\{password\}/g, password),
        )
        .then((result) => {
            console.log("Login script execution result:", result);
        })
        .catch((err) => {
            console.error("Login script execution error:", err);
        });
}

function registerAutoUpdate(logger) {
    // const logger = loggerFactory.Neo('Main.AutoUpdate')
    // console.log(logger, typeof logger.Info);
    logger.Info("注册自动更新相关事件");
    /**
     * @type {CancellationToken | undefined}
     */
    let aborter = undefined;
    let updateReady = false;
    let updateAvailable = false;
    let timer = undefined;
    const updateServer = cmdArgs.updateServer || config.updateServer;
    autoUpdater.setFeedURL({
        provider: "generic",
        // url:  cmdArgs.updateServer || 'http://*************/releases',
        url: updateServer,
    });
    autoUpdater.autoDownload = true;
    autoUpdater.autoRunAppAfterInstall = true;
    autoUpdater.autoInstallOnAppQuit = cmdArgs.autoUpdate === "true";
    // 自动更新相关事件
    ipcMain.handle("check-for-updates", async () => {
        logger.Info("开始检查更新, 更新服务器: " + updateServer);
        try {
            const result = await autoUpdater.checkForUpdates();
            logger.Info("检查更新结果:\n" + JSON.stringify(result));
            return !!result
                ? {
                      isUpdateAvailable: result.isUpdateAvailable,
                      updateInfo: result.updateInfo,
                  }
                : result;
        } catch (e) {
            logger.Info("检查更新失败" + e);
        }
    });
    // UI 确认更新
    ipcMain.handle("confirm-update", async () => {
        if (!updateAvailable) {
            logger.Error("更新不可用，无法开始下载更新内容");
            return;
        }
        aborter = new CancellationToken();
        logger.Info("用户确认更新，开始下载更新内容");
        return await autoUpdater.downloadUpdate();
    });
    ipcMain.handle("cancel-update", async () => {
        logger.Info("用户取消更新");
        if (aborter) {
            aborter.cancel();
        }
        aborter = undefined;
    });
    ipcMain.handle("restart-now", (e, arg) => {
        if (!updateReady) {
            logger.Error("更新未就绪，无法执行退出并安装");
            return;
        }
        if (taskAcceptanceInitiated) {
            logger.Error("更新以就绪，但当前处于接单状态，无法执行退出并安装");
            return;
        }
        logger.Info("用户点击立即重启，执行退出并安装");
        app.isQuitting = true;
        autoUpdater.quitAndInstall(true, true);
    });
    // 自动更新错误
    autoUpdater.on("error", (error) => {
        logger.Error(`自动更新失败，原因：${error}`);
        aborter = undefined;
        mainWindow?.webContents.send("update-error", error);
    });
    // 检查更新中
    autoUpdater.on("checking-for-update", () => {
        //logger.Info("检查更新中");
    });
    // 当自动更新可用
    autoUpdater.on("update-available", function (info) {
        updateAvailable = true;
        logger.Info(
            `更新可用，版本：${info.version}, 发布日期：${info.releaseDate}`,
        );
        // 4. 告诉渲染进程有更新，info 包含新版本信息
        mainWindow?.webContents.send("update-available", info);
    });
    autoUpdater.on("update-cancelled", (info) => {
        logger.Info(`更新已取消，版本：${info.version}`);
        mainWindow?.webContents.send("update-cancelled", info);
    });
    // 更新不可用
    autoUpdater.on("update-not-available", function (info) {
        //logger.Info("更新不可用");
    });
    // 下载进度
    autoUpdater.on("download-progress", function (progressObj) {
        // console.log('downloadProgress', progressObj.);
        mainWindow?.webContents.send("download-progress", progressObj);
    });
    autoUpdater.on("update-downloaded", async function (evt) {
        logger.Info(
            `更新内容已下载，版本：${evt.version}, 发布日期：${evt.releaseDate}`,
        );
        aborter = undefined;
        updateReady = true;
        const isIdle = await checkIsIdle(logger);
        if (cmdArgs.autoUpdate === "true") {
            if (!isIdle) {
                logger.Info(
                    "已开启自动更新但是当前处于繁忙状态，无法退出更新并自动重启",
                );
                return;
            }
            logger.Info(
                "已开启自动更新并且当前处于空闲状态，即将退出更新并自动重启",
            );
            app.isQuitting = true;
            autoUpdater.quitAndInstall(true, true);
        } else {
            mainWindow?.webContents.send("update-downloaded");
        }
    });
    timer = setInterval(
        () => {
            autoUpdater.checkForUpdates();
        },
        2 * 60 * 1000,
    );
}

// 用户数据存储功能
const userDataPath = app.getPath("userData");
const userCredentialsFile = path.join(userDataPath, "user-credentials.json");

/**
 * @typedef {Object} Credentials
 * @property {string} phone
 * @property {string} token
 * @property {number} savedAt
 * @property {boolean} autoLogin
 */

// 保存用户凭据
function saveUserCredentials(data) {
    try {
        // 确保目录存在
        if (!fs.existsSync(userDataPath)) {
            fs.mkdirSync(userDataPath, { recursive: true });
        }

        /**
         * 保存数据（只保存非敏感信息和token）
         * @type {Credentials}
         */
        const credentialsData = {
            phone: data.phone,
            token: data.token,
            savedAt: Date.now(),
            autoLogin: data.autoLogin !== false, // 默认开启自动登录
        };

        fs.writeFileSync(
            userCredentialsFile,
            JSON.stringify(credentialsData, null, 2),
        );
        config.credentials = credentialsData;
        console.log("用户凭据已保存到:", userCredentialsFile);
        return true;
    } catch (error) {
        console.error("保存用户凭据失败:", error);
        return false;
    }
}

/**
 * 读取用户凭据
 * @returns {Credentials | null}
 */
function loadUserCredentials() {
    try {
        if (config.credentials) return config.credentials;
        if (!fs.existsSync(userCredentialsFile)) {
            return null;
        }

        const data = fs.readFileSync(userCredentialsFile, "utf8");
        const credentials = JSON.parse(data);

        // 检查token是否过期（这里设置30天过期）
        const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
        if (Date.now() - credentials.savedAt > thirtyDaysInMs) {
            console.log("用户凭据已过期，自动删除");
            clearUserCredentials();
            return null;
        }
        config.credentials = credentials;
        return credentials;
    } catch (error) {
        console.error("读取用户凭据失败:", error);
        return null;
    }
}

// 清除用户凭据
function clearUserCredentials() {
    try {
        config.credentials = undefined;
        if (fs.existsSync(userCredentialsFile)) {
            fs.unlinkSync(userCredentialsFile);
            console.log("用户凭据已清除");
        }
        return true;
    } catch (error) {
        console.error("清除用户凭据失败:", error);
        return false;
    }
}

// 更新自动登录设置
function updateAutoLoginSetting(autoLogin) {
    try {
        const credentials = loadUserCredentials();
        if (credentials) {
            credentials.autoLogin = autoLogin;
            fs.writeFileSync(
                userCredentialsFile,
                JSON.stringify(credentials, null, 2),
            );
            return true;
        }
        return false;
    } catch (error) {
        console.error("更新自动登录设置失败:", error);
        return false;
    }
}

const userSettingsFile = path.join(userDataPath, "user-settings.json");

/**
 * @typedef {Object} UserSettings
 * @property {boolean} autoOrder
 */

/**
 * 保存用户设置
 * @param {UserSettings} data
 * @param logger
 * @returns {boolean}
 */
function saveUserSettings(data, logger) {
    try {
        // 确保目录存在
        if (!fs.existsSync(userDataPath)) {
            fs.mkdirSync(userDataPath, { recursive: true });
        }
        const newUserSettings = {
            ...userSettings,
            ...data,
        };
        fs.writeFileSync(
            userSettingsFile,
            JSON.stringify(newUserSettings, null, 2),
        );
        const needUpdateTrayAndNotify =
            userSettings.autoOrder !== newUserSettings.autoOrder;
        Object.assign(userSettings, newUserSettings);
        logger.Info("用户设置已保存到: " + userSettingsFile);
        if (needUpdateTrayAndNotify) {
            // updateTrayMenu(logger).catch((err) =>
            //   logger.Info("设置用户 autoOrder 后更新 Tray 失败: " + err),
            // );
            mainWindow?.webContents.send("user-settings-change", [
                "autoOrder",
                newUserSettings.autoOrder,
            ]);
        }
        return true;
    } catch (error) {
        logger.Error("保存用户设置失败: " + error);
        return false;
    }
}
/**
 * 加载用户设置
 * @param logger
 * @returns {UserSettings|null}
 */
function loadUserSettings(logger) {
    try {
        if (!fs.existsSync(userSettingsFile)) {
            return null;
        }
        const data = fs.readFileSync(userSettingsFile, "utf8");
        const _userSettings = JSON.parse(data);
        Object.assign(userSettings, _userSettings);
        return userSettings;
    } catch (error) {
        logger.Error("读取用户设置失败: " + error);
        return null;
    }
}

async function checkIsIdle(logger, distroName = config.distroName) {
    try {
        logger.Info("开始检查当前是否处于空闲");
        const mirrorStatus = await CheckMirror(logger, distroName);
        if (mirrorStatus.installling) return false;
        if (!mirrorStatus.running) return true;
        const nodeId = await MirrorNodeID(logger, distroName);
        const isIdle = await CheckMirrorNodeIsIdle(nodeId, distroName, logger);
        logger.Info(`当前处于${isIdle ? "空闲" : "繁忙"}状态`);
        return isIdle;
    } catch (e) {
        logger.Error("无法获取当前是否处于空闲，原因: " + e);
        return true;
    }
}

function registerAxiosInspector(logger) {
    let expired = false;
    axios.interceptors.response.use(
        // 处理成功的响应
        (response) => {
            // console.log('响应拦截器 - 成功:', response)
            if (response.data.err_code === 901) {
                if (!expired && mainWindow)
                    logger.Info(
                        "用户凭据已失效, access: " + response.config.url,
                    );
                expired = true;
                mainWindow?.webContents.send("login-expired");
                throw new Error(response.data.err_message);
            } else {
                expired = false;
            }
            return response;
        },
        // 处理错误的响应
        async (err) => {
            if (err.isUnAuthorized) {
                expired = true;
                logger.Info(`未授权访问: ${response.config.url}` + err);
                mainWindow?.webContents.send("login-expired");
            }

            return Promise.reject(err);
        },
    );
}
function registerIdleWatcher(logger) {
    let extended = false;
    const check = () => {
        const idleState = powerMonitor.getSystemIdleState(30);
        const idleTime = powerMonitor.getSystemIdleTime();
        if (idleState === "idle" || idleTime >= 30) {
            logger.Info(
                "过去 30 秒都没有任何活动，隐藏界面" +
                    `idleState=${idleState} idleTime=${idleTime} extended=${extended}`,
            );
            mainWindow?.hide();
        } else {
            if (idleTime > 15 && !extended) {
                extended = true;
                setTimeout(check, (30 - idleTime + 1) * 1000);
                return;
            }
            logger.Info(
                "过去 30 秒用户存在活动，放弃隐藏界面" +
                    `idleState=${idleState} idleTime=${idleTime} extended=${extended}`,
            );
        }
    };
    setTimeout(check, 30_000);
}
