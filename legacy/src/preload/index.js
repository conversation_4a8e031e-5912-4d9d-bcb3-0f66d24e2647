"use strict";
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import { electronAPI } from "@electron-toolkit/preload";

const versions = {
  node: () => process.versions.node,
  chrome: () => process.versions.chrome,
  electron: () => process.versions.electron,
  platform: () => process.platform,
};

const apis = {
  ping: (v) => ipcRenderer.invoke("ping", v),
  pingEx: (v, f) => ipcRenderer.invoke("ping_ex", v, f),
  checkVirtual: () => ipcRenderer.invoke("check_virtual"),
  checkOS: () => ipcRenderer.invoke("check_os"),
  checkWSLEnabled: () => ipcRenderer.invoke("check_wsl_enabled"),
  installWSL: () => ipcRenderer.invoke("install_wsl"),
  checkMirror: () => ipcRenderer.invoke("check_mirror"),
  checkWSLInstance: (instanceName) =>
    ipcRenderer.invoke("check_wsl_instance", instanceName),
  installMirror: (url, md5) => ipcRenderer.invoke("install_mirror", url, md5),
  installMirrorStat: () => ipcRenderer.invoke("install_mirror_stat"),
  installMirrorCancel: () => ipcRenderer.invoke("install_mirror_cancel"),
  uninstallMirror: () => ipcRenderer.invoke("uninstall_mirror"),
  runMirror: () => ipcRenderer.invoke("run_mirror"),
  abortRunMirror: (reason) => ipcRenderer.invoke("abort_run_mirror", reason),
  stopMirror: () => ipcRenderer.invoke("stop_mirror"),
  mirrorIP: () => ipcRenderer.invoke("mirror_ip"),
  checkMirrorNodeIsIdle: () => ipcRenderer.invoke("check_mirror_node_is_idle"),
  getUID: () => ipcRenderer.invoke("get_uid"),
  writeLogInfo: (s, l, t, f) =>
    ipcRenderer.invoke("write_log_info", s, l, t, f),
  writeLogError: (s, l, t, f) =>
    ipcRenderer.invoke("write_log_error", s, l, t, f),
  writeLogWarn: (s, l, t, f) =>
    ipcRenderer.invoke("write_log_warn", s, l, t, f),
  writeLogDebug: (s, l, t, f) =>
    ipcRenderer.invoke("write_log_debug", s, l, t, f),
  packLog: (name) => ipcRenderer.invoke("pack_log", name),
  showDir: (path) => ipcRenderer.invoke("show_dir", path),
  getVersion: () => ipcRenderer.invoke("get_ver"),
  getLog: () => ipcRenderer.invoke("get_log"),
  checkPowerShell: () => ipcRenderer.invoke("check_powershell"),
  installPowerShell: () => ipcRenderer.invoke("install_powershell"),
  mirrorNodeID: () => ipcRenderer.invoke("mirror_node_id"),
  abortMirrorNodeID: (reason) => ipcRenderer.invoke("abort_mirror_node_id", reason),
  showUrl: (url) => ipcRenderer.invoke("show_url", url),
  checkNet: () => ipcRenderer.invoke("check_net"),
  writeInfo: (s) => ipcRenderer.invoke("write_info", s),
  autoStartTask: () => ipcRenderer.invoke("auto-start-task"),
  resetTaskAcceptance: () => ipcRenderer.invoke("reset-task-acceptance"),
  enableAutoStart: () => ipcRenderer.invoke("auto_start_enable"),
  disableAutoStart: () => ipcRenderer.invoke("auto_start_disable"),
  checkAutoStart: () => ipcRenderer.invoke("auto_start_check"),
  saveUserCredentials: (data) =>
    ipcRenderer.invoke("save-user-credentials", data),
  loadUserCredentials: () => ipcRenderer.invoke("load-user-credentials"),
  clearUserCredentials: () => ipcRenderer.invoke("clear-user-credentials"),
  updateAutoLoginSetting: (autoLogin) =>
    ipcRenderer.invoke("update-auto-login-setting", autoLogin),
  saveMirrorVersion: (versionData) =>
    ipcRenderer.invoke("save_mirror_version", versionData),
  getMirrorVersion: () => ipcRenderer.invoke("get_mirror_version"),
  getLatestMirrorInfo: () => ipcRenderer.invoke("get_latest_mirror_info"),
  clearMirrorVersion: () => ipcRenderer.invoke("clear_mirror_version"),
  downloadMirrorUpdate: (updateInfo) =>
    ipcRenderer.invoke("download_mirror_update", updateInfo),
  getDownloadProgress: () => ipcRenderer.invoke("get_download_progress"),
  updateMirror: (filePath) => ipcRenderer.invoke("update_mirror", filePath),
  deleteFile: (filePath) => ipcRenderer.invoke("delete_file", filePath),
  openWindowsFeaturesDialog: () => ipcRenderer.invoke("open_windows_features_dialog"),
  checkSelfNodeStatus: () => ipcRenderer.invoke("check_self_node_status"),
  // === 自动更新相关
  /**
   * @returns {Promise<import("electron-updater").UpdateCheckResult>}
   */
  checkForUpdates: () => ipcRenderer.invoke("check-for-updates"),
  /**
   * @returns {Promise<string[]>}
   */
  confirmUpdate: () => ipcRenderer.invoke("confirm-update"),
  /**
   * @returns {Promise<void>}
   */
  cancelUpdate: () => ipcRenderer.invoke("cancel-update"),
  /**
   * @returns {Promise<void>}
   */
  restartNow: () => ipcRenderer.invoke("restart-now"),

  loadUserSettings: () => ipcRenderer.invoke("load-user-settings"),
  saveUserSettings: (settings) =>
    ipcRenderer.invoke("save-user-settings", settings),
  getAutoStartStatus: () => ipcRenderer.invoke("get-auto-start-status"),
  toggleAutoStart: (enable) => ipcRenderer.invoke("toggle-auto-start", enable),
};

// 添加IPC事件监听器
const listeners = {
  onAutoStartTask: (callback) => {
    const listener = (event) => {
      callback();
    };
    ipcRenderer.on("auto-start-task", listener);
    return () => {
      ipcRenderer.off('auto-start-task', listener)
    }
  },
  onDownloadProgress: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("download-progress", listener);
    return () => {
      ipcRenderer.off("download-progress", listener);
    };
  },
  onUpdateDownloaded: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("update-downloaded", listener);
    return () => {
      ipcRenderer.off("update-downloaded", listener);
    };
  },
  onUpdateError: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("update-error", listener);
    return () => {
      ipcRenderer.off("update-error", listener);
    };
  },
  onUpdateAvailable: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("update-available", listener);
    return () => {
      ipcRenderer.off("update-available", listener);
    };
  },
  onUpdateCanceled: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("update-cancelled", listener);
    return () => {
      ipcRenderer.off("update-cancelled", listener);
    };
  },
  onImmediateCheck: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("immediate-check", listener);
    return () => {
      ipcRenderer.off("immediate-check", listener);
    };
  },
  onLoginExpired: (cb) => {
    const listener = (_event, args) => {
      cb(args);
    };
    ipcRenderer.on("login-expired", listener);
    return () => {
      ipcRenderer.off("login-expired", listener);
    };
  },
  onUserSettingsChange: (key, cb) => {
    const listener = (_event, args) => {
      if (args[0] === key) {
        cb(args[1]);
      }
    };
    ipcRenderer.on("user-settings-change", listener);
    return () => {
      ipcRenderer.off("user-settings-change", listener);
    };
  },
  onSelfNodeStatusChange: (cb) => {
      const listener = (_event, args) => {
          cb(args);
      };
      ipcRenderer.on("self-node-status-change", listener);
      return () => {
          ipcRenderer.off("self-node-status-change", listener);
      };
  }
};

const AppConfig = {
  getSync: () => ipcRenderer.sendSync("get-config-sync"),
};

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld("electron", electronAPI);
    contextBridge.exposeInMainWorld("versions", versions);
    contextBridge.exposeInMainWorld("API", apis);
    contextBridge.exposeInMainWorld("IPCListeners", listeners);
    contextBridge.exposeInMainWorld("AppConfig", AppConfig);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = electronAPI;
  window.versions = versions;
  window.API = apis;
  window.IPCListeners = listeners;
  window.AppConfig = AppConfig;
}
