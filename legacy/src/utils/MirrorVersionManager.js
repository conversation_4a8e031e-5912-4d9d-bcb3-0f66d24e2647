/**
 * 镜像版本管理器
 * 负责镜像版本的存储、读取和管理
 */

class MirrorVersionManager {
  constructor() {
    this.versionFile = "mirror-version.json";
  }

  /**
   * 保存镜像版本信息
   * @param {string} version - 镜像版本号
   * @param {string} imageUrl - 镜像下载URL
   * @param {string} releaseNotes - 版本说明
   */
  async saveMirrorVersion(version, imageUrl = "", releaseNotes = "") {
    try {
      const versionData = {
        current_version: version,
        last_check_time: new Date().toISOString(),
        install_time: new Date().toISOString(),
        image_url: imageUrl,
        release_notes: releaseNotes,
        auto_check_enabled: true,
      };

      const result = await window.API.saveMirrorVersion(versionData);
      if (result) {
        console.log("镜像版本信息保存成功:", version);
        return true;
      } else {
        console.error("镜像版本信息保存失败");
        return false;
      }
    } catch (error) {
      console.error("保存镜像版本信息时出错:", error);
      return false;
    }
  }

  /**
   * 获取本地镜像版本信息
   * @returns {Object|null} 版本信息对象或null
   */
  async getMirrorVersion() {
    try {
      const versionData = await window.API.getMirrorVersion();
      if (versionData) {
        console.log("获取到本地镜像版本:", versionData.current_version);
        return versionData;
      } else {
        console.log("本地没有镜像版本记录");
        return null;
      }
    } catch (error) {
      console.error("获取镜像版本信息时出错:", error);
      return null;
    }
  }

  /**
   * 检查是否存在版本记录
   * @returns {boolean} 是否存在版本记录
   */
  async hasVersionRecord() {
    const versionData = await this.getMirrorVersion();
    return versionData !== null && versionData.current_version;
  }

  /**
   * 更新最后检查时间
   */
  async updateLastCheckTime() {
    try {
      const versionData = await this.getMirrorVersion();
      if (versionData) {
        versionData.last_check_time = new Date().toISOString();
        await window.API.saveMirrorVersion(versionData);
        console.log("更新最后检查时间成功");
      }
    } catch (error) {
      console.error("更新最后检查时间时出错:", error);
    }
  }

  /**
   * 清除版本记录
   */
  async clearVersionRecord() {
    try {
      const result = await window.API.clearMirrorVersion();
      if (result) {
        console.log("镜像版本记录清除成功");
        return true;
      } else {
        console.error("镜像版本记录清除失败");
        return false;
      }
    } catch (error) {
      console.error("清除镜像版本记录时出错:", error);
      return false;
    }
  }

  /**
   * 获取当前版本号
   * @returns {string|null} 当前版本号
   */
  async getCurrentVersion() {
    const versionData = await this.getMirrorVersion();
    return versionData ? versionData.current_version : null;
  }

  /**
   * 检查服务器版本更新
   * @returns {Object|null} 服务器版本信息或null
   */
  async checkServerVersion() {
    try {
      return await window.API.getLatestMirrorInfo();
    } catch (error) {
      console.error("检查服务器版本时出错:", error);
      return null;
    }
  }

  /**
   * 比较版本号
   * @param {string} currentVersion - 当前版本
   * @param {string} serverVersion - 服务器版本
   * @returns {number} -1: 当前版本更新, 0: 版本相同, 1: 服务器版本更新
   */
  compareVersions(currentVersion, serverVersion) {
    if (!currentVersion || !serverVersion) return 0;

    const current = currentVersion.split(".").map(Number);
    const server = serverVersion.split(".").map(Number);

    for (let i = 0; i < Math.max(current.length, server.length); i++) {
      const currentPart = current[i] || 0;
      const serverPart = server[i] || 0;

      if (currentPart < serverPart) return 1; // 服务器版本更新
      if (currentPart > serverPart) return -1; // 当前版本更新
    }

    return 0; // 版本相同
  }

  /**
   * 检查是否有可用更新
   * @returns {Object|null} 更新信息或null
   */
  async checkForUpdates() {
    try {
      console.log("=== 🔎 开始检查镜像版本更新 ===");

      // 获取本地版本
      const localVersion = await this.getCurrentVersion();
      console.log("🏠 本地镜像版本:", localVersion);

      // 获取服务器版本
      const serverInfo = await this.checkServerVersion();
      if (!serverInfo) {
        console.log("❌ 无法获取服务器版本信息");
        return null;
      }

      const serverVersion = serverInfo.version;
      console.log("🌐 服务器镜像版本:", serverVersion);
      console.log("📋 服务器版本详情:", {
        url: serverInfo.url,
        release_notes: serverInfo.release_notes,
      });

      // 比较版本
      const comparison = this.compareVersions(localVersion, serverVersion);
      console.log(
        "⚖️ 版本比较结果:",
        comparison,
        "(-1: 本地更新, 0: 相同, 1: 服务器更新)"
      );

      if (comparison > 0) {
        console.log("🆕 发现新版本可用!");
        const updateInfo = {
          hasUpdate: true,
          currentVersion: localVersion,
          newVersion: serverVersion,
          downloadUrl: serverInfo.url,
          releaseNotes: serverInfo.release_notes,
        };
        console.log("📦 更新信息:", updateInfo);
        return updateInfo;
      } else if (comparison === 0) {
        console.log("✅ 当前版本已是最新");
        return {
          hasUpdate: false,
          currentVersion: localVersion,
          newVersion: serverVersion,
        };
      } else {
        console.log("🔼 本地版本比服务器版本更新");
        return {
          hasUpdate: false,
          currentVersion: localVersion,
          newVersion: serverVersion,
        };
      }
    } catch (error) {
      console.error("💥 检查更新时出错:", error);
      return null;
    }
  }

  /**
   * 下载镜像更新
   * @param {Object} updateInfo - 更新信息
   * @returns {boolean} 是否下载成功
   */
  async downloadUpdate(updateInfo) {
    try {
      console.log("=== 📥 MirrorVersionManager: 开始下载镜像更新 ===");
      console.log("📦 更新信息详情:", updateInfo);

      const result = await window.API.downloadMirrorUpdate(updateInfo);
      console.log("🔄 下载API调用结果:", result);

      if (result && result.success) {
        console.log("✅ 镜像更新下载成功:", result.filePath);
        console.log("💾 下载文件保存路径:", result.filePath);
        return true;
      } else {
        console.error("❌ 镜像更新下载失败:", result?.message);
        return false;
      }
    } catch (error) {
      console.error("💥 下载镜像更新时出错:", error);
      console.error("错误堆栈:", error.stack);
      return false;
    }
  }

  /**
   * 获取下载进度
   * @returns {Object} 下载进度信息
   */
  async getDownloadProgress() {
    try {
      const progress = await window.API.getDownloadProgress();
      return progress;
    } catch (error) {
      console.error("获取下载进度时出错:", error);
      return { isDownloading: false, progress: 0 };
    }
  }

  /**
   * 检查更新并自动下载
   * @returns {Object|null} 更新状态信息
   */
  async checkAndDownloadUpdates() {
    try {
      console.log("=== 🔍 MirrorVersionManager: 开始检查更新并自动下载 ===");

      const updateInfo = await this.checkForUpdates();
      console.log("📊 版本检查结果:", updateInfo);

      if (!updateInfo) {
        console.log("⚠️ 版本检查失败，返回 check_failed 状态");
        return { status: "check_failed" };
      }

      if (updateInfo.hasUpdate) {
        console.log("🎯 发现新版本需要更新!");
        console.log("当前版本:", updateInfo.currentVersion);
        console.log("目标版本:", updateInfo.newVersion);
        console.log("开始自动下载...");

        const downloadSuccess = await this.downloadUpdate(updateInfo);
        console.log("📥 下载结果:", downloadSuccess);

        if (downloadSuccess) {
          console.log("✅ 下载启动成功，返回 download_started 状态");
          return {
            status: "download_started",
            updateInfo: updateInfo,
          };
        } else {
          console.log("❌ 下载启动失败，返回 download_failed 状态");
          return {
            status: "download_failed",
            updateInfo: updateInfo,
          };
        }
      } else {
        console.log("✅ 当前版本已是最新，无需下载");
        console.log("当前版本:", updateInfo.currentVersion);
        console.log("服务器版本:", updateInfo.newVersion);
        return {
          status: "up_to_date",
          updateInfo: updateInfo,
        };
      }
    } catch (error) {
      console.error("💥 MirrorVersionManager: 检查更新并下载时出错:", error);
      return { status: "error", error: error.message };
    }
  }
}

// 创建单例实例
const mirrorVersionManager = new MirrorVersionManager();

export default mirrorVersionManager;
