import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
// import store from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import elementPlusLocaleZhCn from 'element-plus/dist/locale/zh-cn.mjs'
import '@assets/scss/elementUi.scss'
// import '@assets/scss/color.scss'
import '@assets/scss/common.scss'
import dayjs from 'dayjs'
// vue virtual scroller
// import 'default-passive-events' // 阻止默认事件警告
import { ElMessage } from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const app = createApp(App)
// app.use(store)
app.use(router)
app.use(ElementPlus, { locale: elementPlusLocaleZhCn })
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
//

app.config.globalProperties.$message = ElMessage
// app.config.globalProperties.$dist = (type: string) => {
//   let data = []
//   let dist = []
//   const distString = window.sessionStorage.getItem('dist')
//   if (distString) {
//     dist = JSON.parse(distString)
//   }
//   if (dist) {
//     data = dist[type]
//   }
//   return data
// }
// app.config.globalProperties.getNameByCodeAndType = (
//   code: string,
//   type: string,
// ) => {
//   if (window.sessionStorage.getItem('dist')) {
//     const dictionariesArray = JSON.parse(
//       window.sessionStorage.getItem('dist') || '',
//     )[type]
//     let label = ''
//     if (dictionariesArray && dictionariesArray.length) {
//       dictionariesArray.forEach((item: { code: string; value: string }) => {
//         if (item.code + '' === code + '') {
//           label = item.value
//         }
//       })
//       return label
//     }
//   }
// }
// 设置 浏览器头名称
const titleEl = document.head.querySelector('title')
titleEl.innerText = window.AppConfig.getSync().clientName
app.mount('#app')
export default app
