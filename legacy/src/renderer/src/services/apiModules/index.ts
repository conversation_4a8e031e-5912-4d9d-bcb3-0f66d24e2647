import {baseUrl, getLatestVersion} from './common'

export interface commonApi {
  baseUrl: string
  addDict?: string | undefined
  addDictType?: string | undefined
  batchRemoveDict?: string | undefined
  batchRemoveDictType?: string | undefined
  downloadFile?: string | undefined
  editDict?: string | undefined
  editDictType?: string | undefined
  getDictList?: string | undefined
  getDictType?: string | undefined
  getDictTypeList?: string | undefined
  getDist?: string | undefined
  login?: string | undefined
  loginCode?: string | undefined
  priviewFile?: string | undefined
  removeDict?: string | undefined
  removeDictType?: string | undefined
  uploadFile?: string | undefined
  getLatestVersion?: string | undefined
  [key: string]: string | undefined
}
const apiContext: any = import.meta.glob('../apiModules/**/*.ts', {
  eager: true,
})
let apiObj: commonApi = { baseUrl }
if (Object.keys(apiContext).length) {
  for (const obj in apiContext) {
    const objValue = JSON.parse(JSON.stringify(apiContext[obj]))
    apiObj = { ...apiObj, ...objValue }
  }
}

export default apiObj
