// 公用接口模块
import axios from "axios";
type UnknownProperties = Record<string, unknown>;
export default {
    fetchPost(
        params: UnknownProperties,
        path: string,
        url: string,
        contentType: boolean,
        signal,
    ) {
        return axios.post(`${path}`, params, {
            baseURL: url,
            headers: {
                "Content-Type": contentType ? "text/plain" : "application/json",
            },
            signal,
        });
    },
    fetchPostParams(
        urlParams: UnknownProperties,
        params: UnknownProperties,
        path: string,
        url: string,
    ) {
        console.log(`${path}${urlParams}`, params, "paramsparamsparamsparams");
        return axios.post(`${path}${urlParams}`, params, {
            baseURL: url,
            headers: {
                "Content-Type": "application/json",
            },
        });
    },
    fetchGetParams(
        urlParams: UnknownProperties,
        params: UnknownProperties,
        path: string,
        url: string,
    ) {
        console.log(urlParams, params, `${path}`, "paramsparamsparamsparams");
        // return
        return axios.get(`${path}${urlParams}`, {
            params,
            baseURL: url,
            headers: {
                "Content-Type": "application/json",
            },
        });
    },
    fetchGet(params: UnknownProperties, path: string, url: string) {
        if (params) {
            return axios.get(`${path}/${params}`, {
                baseURL: url,
            });
        } else {
            return axios.get(`${path}`, {
                baseURL: url,
            });
        }
    },
    fetchGets(params: UnknownProperties, path: string, url: string) {
        return axios.get(`${path}?${params}`, {
            baseURL: url,
        });
    },

    fetchExportPost(params: UnknownProperties, path: string, url: string) {
        return axios.post(`${path}`, params, {
            baseURL: url,
            headers: {
                "Content-Type": "application/json",
            },
            responseType: "arraybuffer",
        });
    },
    fetchGetDownload(path: string, url: string) {
        return axios.get(`${path}`, {
            baseURL: url,
            responseType: "arraybuffer",
        });
    },
    fetchGetFtpDownload(params: object, path: string, url: string) {
        return axios.get(`${path}`, {
            params,
            baseURL: url,
            responseType: "arraybuffer",
        });
    },
};
