"use strict";
import axios from "axios";
import myRouter from "../router";
import { ElMessage } from "element-plus";
import { handleError } from "./handleError.ts";
import { removeCookie } from "../global/cookie";
import { type AxiosResponse, type AxiosError } from "axios";

const loadingInstance: any = null;
const website = "//" + location.host + location.pathname + "#/";
window.__axiosCancelTokenArr = [];
/**
 * 请求拦截器
 * 处理发送请求之前 header 插入 token 鉴权
 */

axios.interceptors.request.use(
    (config: any) => {
        // 从 localStorage 获取 token
        let token = localStorage.getItem("authToken") || "";
        let clientVersion = localStorage.getItem("clientVersion") || "";
        const headers = config.headers || {};
        // 确保不发送 cookie
        config.withCredentials = false;
        // 这个地方可以自定义请求头
        config.headers = {
            ...headers,
            "Content-Type": "application/json",
            Authorization: token || "",
            System: "Client",
            version: clientVersion,
            accept: "*/*",
            language: "en", // 这个是自定义的请求头，还可以加 token 等
        };
        return config;
    },
    (err: any) => {
        return Promise.reject(err);
    },
);

/**
 * 响应拦截器
 * 响应回调错误处理、数据处理等逻辑
 */
axios.interceptors.response.use(
    (response: AxiosResponse) => {
        let result = response;
        console.log("client response", response);
        if (result.status == 200) {
            return Promise.resolve(result.data);
        } else if (result.status === 401) {
            ElMessage.error(
                "由于您长时间没有操作系统，为了您的数据安全，请重新登录",
            );
            window.location.href = website;
            clearClientInfo();
        } else if (result.status === 420) {
            ElMessage.error(result.message || result.msg);
            return Promise.resolve(result.data);
        } else if (result.status === 421) {
            ElMessage.error(result.message || result.msg);
            window.location.href = website;
            if (myRouter.currentRoute.value.path !== "/login") {
                clearClientInfo();
                myRouter.replace({ path: "/login" });
            }
        } else if (result.status === 500) {
            ElMessage.error(result.message || result.msg);
            window.location.href = website;
            if (myRouter.currentRoute.value.path !== "/login") {
                clearClientInfo();
                myRouter.replace({ path: "/login" });
            }
        } else if (result.message === "尚未登录，请登录！") {
            if (myRouter.currentRoute.value.path !== "/login") {
                clearClientInfo();
                myRouter.replace({ path: "/login" });
            }
        } else if (response.data.err_code === 901) {
            if (myRouter.currentRoute.value.path !== "/login") {
                clearClientInfo();
                myRouter.replace({ path: "/login" });
            }
        }
        return response; // 必须返回response或修改后的response
    },
    async (err: AxiosRequestError) => {
        err = handleError(err); // 调用我们自定义的 错误处理方法
        if (err.isUnAuthorized) {
            // 未授权的情况的处理
            clearClientInfo();
        }
        // 还可以自定义其他的情况的处理

        return Promise.reject(err);
    },
);
window.IPCListeners.onLoginExpired(async () => {
    if (
        myRouter.currentRoute.value.path !== "/login" ||
        myRouter.currentRoute.value.path !== "/register"
    ) {
        await clearClientInfo();
        myRouter.replace({ path: "/login" });
    }
});
async function clearClientInfo() {
    removeCookie("user");
    await window.API.clearUserCredentials();
}
// 切换配置
const defaults = {
    // baseURL: baseUrl,
    headers: {
        post: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
    },
};
Object.assign(axios.defaults, defaults);
const files = import.meta.glob("./modules/**/*.ts", { eager: true });

const services: { common: Record<string, unknown> } = { common: {} };
for (const file in files) {
    const fileObj: any = files[file];
    const _file = fileObj.default || fileObj;
    services.common = _file;
}
export default {
    services,
    axios,
};
