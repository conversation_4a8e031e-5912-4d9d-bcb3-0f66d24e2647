// @use "./color.scss" as *;

// 统一样式
html,
body,
#app {
  // width: 100%;
  // height: 100%;
  width: 360px;
  height: 585px;
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  background: $background-color;
  color: $white;
  user-select: none; /* 标准语法 */
  -webkit-user-select: none; /* Safari/Chrome */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
}

body {
  padding: 0 !important;
  font-family: "Microsoft YaHei";
}

textarea {
  resize: none!important;
}

ul,
li {
  list-style: none;
  padding: 0;
}

* {
  box-sizing: border-box;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
}

// ------------------------------------------------------------------------------
// flex 常用布局
// 常用横向水平垂直居中 common-flex-center-row
.cn-flex-c-r {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

// 常用横向水平垂直居左 common-flex-left-row
.cn-flex-l-r {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
}

// 常用横向水平居右 垂直居中 common-flex-right-row
.cn-flex-r-r {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
}

// 常用横向水平垂直两侧 common-flex-between-row
.cn-flex-b-r {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
}

// 常用横向水平居左 垂直居上 common-flex-left-top-row
.cn-flex-l-t-r {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: hidden;
}

// 常用横向水平 between 垂直居上 common-flex-between-top-row
.cn-flex-b-t-r {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  overflow: hidden;
}

// 常用纵向水平垂直居中 common-flex-center-column
.cn-flex-c-c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

// 常用纵向水平居中 垂直居左 两侧 common-flex-center-(flex-start)-column
.cn-flex-c-s-c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;
}

// 常用纵向水平居两端 垂直居左 两侧 common-flex-between-(flex-start)-column
.cn-flex-b-s-c {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  overflow: hidden;
}

// 常用的纵向 flex 并且overfloe:hidden的 common-flex-column-hidden
.cn-flex-c-h {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// ------------------------------------------------------------------------------
// 单行超出显示省略号
.cn-one-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行超出显示省略号
.cn-more-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

// ------------------------------------------------------------------------------
// 常用自适应高度宽度
.cn-flex-hidden {
  flex: 1;
  overflow: hidden;
}

// ------------------------------------------------------------------------------
// 常用的盒子样式
.cn-include-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// ------------------------------------------------------------------------------

// 页面布局——通用 common-layout-wrap
.cn-layout-wrap {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  // border-radius: 8px;
}

// 左侧树的宽度 common-layout-left
.cn-layout-left {
  flex: 0 0 238px;
  height: 100%;
  border-right: 1px solid rgba(67, 76, 92, 0.1);
  padding: 9px 12px;
  overflow: hidden;
  background-color: $white;
  display: flex;
}

// 右侧盒子的宽度 common-layout-right
.cn-layout-right {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0px 15px;
}

// 表格上面的查询条件
.cn-search-wrap {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  flex-shrink: 0;
  margin-bottom: 10px;
  width: 100%;

  .cn-search-left {
    display: flex;

    .el-input {
      width: 148px;
      margin-right: 10px;

      .el-input__wrapper {
        width: 148px !important;
        height: 32px !important;
        line-height: 32px !important;
      }
    }

    .el-select {
      width: 148px;
      margin-right: 10px;
    }

    .cn-s-left-btn {
      .iconfont {
        font-size: 18px;
      }
    }
  }

  .cn-search-right {}
}

// 表格的样式
.cn-table-wrap {
  flex: 1;
  overflow: hidden;
}

// 表格下面的分页样式
.cn-pagination-wrap {
  height: 45px;
}

// 弹窗中form的样式
.cn-dialog-el-form {
  padding: 0 20px;
}

// form 表单的盒子样式
.cn-form-items {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 10px;

  .el-form-item {
    flex: 1;

    &:not(:nth-child(1)) {
      margin-left: 20px;
    }

    .el-input {
      width: 100%;
    }

    .el-cascader {
      width: 100%;
    }

    .el-select {
      width: 100%;
    }

    .el-date-picker {
      width: 100%;
    }

    .el-input-number {
      width: 100%;
    }

    .el-cascader {
      width: 100% !important;
    }
  }
}

// 带单位样式
.el-form-item.form-num-suffix {
  position: relative;

  .suffix {
    position: absolute;
    right: 50px;
    height: 100%;
    top: 0;
    text-align: center;
    color: #C0C4CC;
    transition: all .3s;
    pointer-events: none;
  }
}

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  border: 0px solid #CCC !important;
  height: 27px !important;
  line-height: 27px !important;
  border-radius: 0 4px 4px 0;
  background: (255, 255, 255, 0);
}

// 没有数据的样式
.cn-no-data {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  >img {
    width: 30%;
    height: auto;
  }
}

.cn-el-tooltip-wrap {
  max-width: 50%;
}

// 表单中按钮形式的单选多选样式
.cn-form-button-choose-box {
  flex-wrap: wrap;

  >.el-button {
    margin: 0 10px 10px 0 !important;

    &:nth-last-child(1) {
      margin-right: 0px;
    }
  }
}