// @use "./color.scss" as *;

// 无特殊情况下 后台业务系统按钮拥有以下属性
.el-button--primary {
  background: $main-color !important;
  color: $white;
}

.el-button {
  margin: 0;
}

// 全局el-form-item向start对齐
.el-form-item__content {
  align-items: flex-start;
}

.el-table {
  th.el-table__cell {
    background-color: $border-color !important;
    text-align: center !important;
  }

  td.el-table__cell {
    text-align: center !important;
  }

  .el-table__header-wrapper {
    border-top: solid 1px $border-color !important;
    border-left: solid 1px $border-color !important;
    border-right: solid 1px $border-color !important;
    box-sizing: border-box !important;
  }
}

.el-scrollbar__view {
  height: 100%;
}
.el-tag {
  border: transparent;
}

