<svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="path-1-inside-1_420_951" fill="white">
<path d="M-4.37114e-07 5C-6.78525e-07 2.23858 2.23858 6.78525e-07 5 4.37114e-07C7.76142 1.95703e-07 10 2.23858 10 5C10 7.76142 7.76142 10 5 10C2.23858 10 -1.95703e-07 7.76142 -4.37114e-07 5Z"/>
</mask>
<g clip-path="url(#paint0_angular_420_951_clip_path)" data-figma-skip-parse="true" mask="url(#path-1-inside-1_420_951)"><g transform="matrix(-4.37114e-10 -0.005 0.005 -4.37114e-10 5 5)"><foreignObject x="-1400" y="-1400" width="2800" height="2800"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(255, 218, 218, 1) 0deg,rgba(167, 4, 4, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M2 5C2 3.34315 3.34315 2 5 2L5 -2C1.13401 -2 -2 1.13401 -2 5L2 5ZM5 2C6.65685 2 8 3.34315 8 5L12 5C12 1.13401 8.86599 -2 5 -2L5 2ZM8 5C8 6.65685 6.65685 8 5 8L5 12C8.86599 12 12 8.86599 12 5L8 5ZM5 8C3.34315 8 2 6.65685 2 5L-2 5C-2 8.86599 1.13401 12 5 12L5 8Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.85490196943283081,&#34;b&#34;:0.85490196943283081,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.65490198135375977,&#34;g&#34;:0.015686275437474251,&#34;b&#34;:0.015686275437474251,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.85490196943283081,&#34;b&#34;:0.85490196943283081,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.65490198135375977,&#34;g&#34;:0.015686275437474251,&#34;b&#34;:0.015686275437474251,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-8.7422779415646801e-07,&#34;m01&#34;:10.0,&#34;m02&#34;:0.0,&#34;m10&#34;:-10.0,&#34;m11&#34;:-8.7422779415646801e-07,&#34;m12&#34;:10.000000953674316},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-1-inside-1_420_951)"/>
<defs>
<clipPath id="paint0_angular_420_951_clip_path"><path d="M2 5C2 3.34315 3.34315 2 5 2L5 -2C1.13401 -2 -2 1.13401 -2 5L2 5ZM5 2C6.65685 2 8 3.34315 8 5L12 5C12 1.13401 8.86599 -2 5 -2L5 2ZM8 5C8 6.65685 6.65685 8 5 8L5 12C8.86599 12 12 8.86599 12 5L8 5ZM5 8C3.34315 8 2 6.65685 2 5L-2 5C-2 8.86599 1.13401 12 5 12L5 8Z" mask="url(#path-1-inside-1_420_951)"/></clipPath></defs>
</svg>
