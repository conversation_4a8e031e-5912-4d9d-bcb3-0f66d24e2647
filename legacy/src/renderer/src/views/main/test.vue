<template>
  <div class="scroll-container" ref="scrollContainer" @scroll.passive="handleScroll">
    <div class="scroll-content">
      <div
        v-for="item in logList"
        :key="item.id"
        class="scroll-item"
        @mouseup="handleRowSelection($event, item)">
        <label class="scroll-item-time" v-if="item.t">{{ dayjs(item.t).format('HH:mm:ss') }}</label>
        <label class="scroll-item-type">{{ item.name.split('.')[0] }}</label>
        <label class="scroll-item-text" :title="item.text" style="margin-left: 10px;">{{ item.text }}</label>
      </div>
    </div>

    <!-- 复制提示 -->
    <transition name="fade">
      <div
        v-if="showCopyTip"
        class="copy-tip"
        :style="{
          top: `${tipPosition.top - 10}px`,
          left: `${tipPosition.left + tipPosition.width/2}px`,
          transform: 'translateX(-50%)'
        }">
        已复制
      </div>
    </transition>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import dayjs from 'dayjs';

  let logList = ref([
    {
      type: '1',
      t: '215478545',
      name: '测试一下',
      text: 'nev'
    },
    {
      type: '2',
      t: '22222222',
      name: '加一下校验',
      text: 'test'
    },
    {
      type: '1',
      t: '22222222',
      name: '加一下校验',
      text: 'asdkfjsaflkjsalkfjsadlkfjsaldkfjnslkdfjslakdfjlsadjflsadjflksajf'
    }
  ])

  const showCopyTip = ref(false)
  const tipPosition = ref({
    top: 0,
    left: 0,
    width: 0
  })
  let copyTimeout = ref(null)

  // 处理行选择事件
  const handleRowSelection = (event, item) => {
    // 清除之前的定时器
    if (copyTimeout.value) {
      clearTimeout(copyTimeout.value)
    }

    const selection = window.getSelection()
    const selectedText = selection.toString().trim()

    if (selectedText) {
      // 获取选中文本的位置信息
      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()

      tipPosition.value = {
        top: rect.top,
        left: rect.left,
        width: rect.width
      }

      // 3秒后自动复制
      copyTimeout.value = setTimeout(() => {
        // 构建整行内容
        const fullLineContent = buildFullLineContent(item)
        copyToClipboard(fullLineContent)
      }, 400)
    }
  }

  // 构建整行内容
  const buildFullLineContent = (item) => {
    let line = ''
    if (item.t) {
      line += `${dayjs(item.t).format('HH:mm:ss')} `
    }
    line += `${item.name.split('.')[0]} `
    line += item.text
    return line
  }

  // 复制到剪贴板
  const copyToClipboard = (text) => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(() => {
          showCopyFeedback()
        })
        .catch(err => {
          console.error('复制失败:', err)
          fallbackCopyText(text)
        })
    } else {
      fallbackCopyText(text)
    }
  }

  // 显示复制反馈
  const showCopyFeedback = () => {
    showCopyTip.value = true

    // 1.5秒后自动隐藏提示
    setTimeout(() => {
      showCopyTip.value = false
    }, 3000)
  }

  // 兼容性降级方案
  const fallbackCopyText = (text) => {
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'
    document.body.appendChild(textarea)
    textarea.select()

    try {
      const successful = document.execCommand('copy')
      if (successful) {
        showCopyFeedback()
      }
    } catch (err) {
      console.error('复制失败:', err)
    }

    document.body.removeChild(textarea)
  }
</script>

<style scoped>
  .scroll-container {
    height: 280px;
    overflow-y: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: scroll-position;
  }

  .scroll-content {
    display: flex;
    flex-direction: column;
    transform: translateZ(0);
    overflow: hidden;
  }

  .scroll-item {
    transform: translateZ(0);
    display: flex;
    line-height: 21px;
  }
  .scroll-item-text{
    width: 80px;
    word-break: break-all;
    line-height: 21px;
  }
  .scroll-item-type {
    width: 100px;
    text-align: left;
  }
  .scroll-item-time{
    width: 100px;
  }
  .error-item{
    color: red;
  }
  .info-item{
    color: green;
  }

  /* 优化滚动条 */
  .scroll-container::-webkit-scrollbar {
    width: 6px;
  }
  .scroll-container::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
  }

  /* 更新复制提示样式 */
  .copy-tip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
    white-space: nowrap;
  }

  /* 优化淡入淡出动画 */
  .fade-enter-active, .fade-leave-active {
    transition: all 0.2s;
  }
  .fade-enter-from, .fade-leave-to {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }

  /* 选中效果 */
  .scroll-item {
    cursor: text;
    user-select: text;
  }

  /* 选中文本样式 */
  ::selection {
    background: rgba(0, 120, 215, 0.3);
  }
</style>