<template>
    <div class="task-list-wrap">
        <div class="task-status-export">
            <span @click="exportLogs()">导出日志</span>
        </div>
        <div class="task-list-wrap-list" v-if="ready">
            <div class="task-list-wrap-item">
                <template v-if="!props.isOrdering">
                    <div class="task-status">
                        <span class="task-status-label">
                            <label class="task-status-idle">
                                <span class="task-status-stop-icon"> </span>
                                空闲</label
                            >
                        </span>
                    </div>
                </template>
                <template v-else-if="selfNodeStatusRef.status === 'idle'">
                    <div class="task-status">
                        <span class="task-status-label">
                            <label class="task-status-PENDING">
                                <Loading2Icon />
                                等待任务中</label
                            >
                        </span>
                    </div>
                </template>
                <template v-else>
                    <div class="task-status">
                        <span class="task-status-label">
                            <label class="task-status-RUNNING">
                                <Loading2Icon />
                                运行中</label
                            >
                        </span>
                    </div>
                    <div class="task-time">
                        {{
                            formatDate(
                                "Y-m-d H:i:s",
                                selfNodeStatusRef.createdAt,
                            )
                        }}
                    </div>
                </template>
            </div>
        </div>
        <div class="task-list-wrap-logs">
            <div class="task-log-title">
                日志信息<img
                    @click="copyTestContent"
                    src="@assets/images/copy.png"
                />
            </div>
            <div class="task-log-list">
                <log-list ref="logComponent"></log-list>
            </div>
        </div>
        <!-- 复制提示 -->
        <transition name="fade">
            <div v-if="showCopyTip" class="copy-tip">已复制</div>
        </transition>
    </div>
</template>

<script setup lang="ts">
import { formatDate } from "../../global/date";
import logList from "./logList.vue";
import { _api, _http } from "./../../mixins/service";
import Loading2Icon from "./loadingIcon.vue";
import { LogSource, LogLevel } from "@/types/enums";
import { ElMessage } from "element-plus";
import Log from "@/types/Log";
import Logger from "@/mixins/logger";
import dayjs from "dayjs";

const logComponent = ref<InstanceType<typeof logList> | null>(null);
const showCopyTip = ref(false);

let taskLogInter = null;
let reactiveObj = reactive({
    personInfo: {
        phone: "", // 手机号
        create_time: "", // 加入时间
        invitation_code: "", // 邀请码
    },
});
const props = defineProps({
    ready: {
        type: Boolean,
    },
    isOrdering: Boolean,
});

type SelfNodeStatus =
    | {
          status: "idle";
      }
    | { status: "running"; createdAt: string };
const selfNodeStatusRef = ref<SelfNodeStatus>({
    status: "idle",
    createdAt: undefined,
});

const copyTestContent = async () => {
    await nextTick(); // 等待DOM更新

    try {
        const content = logComponent.value?.$el.textContent?.trim() || "";
        if (!content) throw new Error("无内容可复制");

        try {
            // 优先使用现代API
            await navigator.clipboard.writeText(content);
        } catch {
            // 回退方案
            const textarea = document.createElement("textarea");
            textarea.value = content;
            textarea.style.position = "fixed";
            textarea.style.opacity = "0";
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand("copy");
            document.body.removeChild(textarea);
        }

        showCopyTip.value = true;
        setTimeout(() => (showCopyTip.value = false), 2000);
    } catch (err) {
        console.error("复制失败:", err);
        alert("复制失败，请手动选择文本复制");
    }
};

function loadPersonInfo() {
    _http.common
        .fetchGet(null, _api.userDetail, _api.baseUrl)
        .then(async (res) => {
            console.log(res, "用户基本信息");
            if (res && res.success) {
                reactiveObj.personInfo = res.data;
                // try {
                //   await window.API.writeLogInfo('用户基本信息获取成功', LogLevel.INFO, LogSource.SERVER)
                // } catch (e) {
                //   console.log('用户基本信息获取成功日志写入错误')
                // }
                Logger(
                    new Log(
                        dayjs().toISOString(),
                        LogSource.SERVER,
                        LogLevel.INFO,
                        "用户基本信息获取成功",
                        true,
                    ),
                );
            } else {
                console.log("用户基本信息获取失败");
                // try {
                //   await window.API.writeLogError('用户基本信息获取失败', LogLevel.ERROR, LogSource.SERVER)
                // } catch (e) {
                //   console.log('用户基本信息获取失败日志写入错误')
                // }
                Logger(
                    new Log(
                        dayjs().toISOString(),
                        LogSource.SERVER,
                        LogLevel.ERROR,
                        "用户基本信息获取失败",
                        true,
                    ),
                );
            }
        })
        .catch(async (error) => {
            // try {
            //   await window.API.writeLogError('用户基本信息获取失败', LogLevel.ERROR, LogSource.SERVER)
            // } catch (e) {
            //   console.log('用户基本信息获取失败日志写入错误')
            // }
            Logger(
                new Log(
                    dayjs().toISOString(),
                    LogSource.SERVER,
                    LogLevel.ERROR,
                    "用户基本信息获取失败",
                    true,
                ),
            );
            console.error("个人基本信息加载失败:", error);
        });
}
async function exportLogs() {
    // try {
    //   await window.API.writeLogInfo('导出日志', LogLevel.INFO, LogSource.UI)
    // } catch (e) {
    //   console.log('导出日志写入错误')
    // }
    Logger(
        new Log(
            dayjs().toISOString(),
            LogSource.UI,
            LogLevel.INFO,
            "导出日志",
            true,
        ),
    );
    let deviceId = null;
    try {
        deviceId = await window.API.getUID();
        deviceId = deviceId.substr(deviceId.length - 8, 8);
    } catch (e) {
        console.log("window.API.getUID() 获取设备Id报错");
    }

    try {
        let params = {
            设备号: deviceId,
            用户ID: reactiveObj.personInfo["id"],
        };
        await window.API.writeInfo(JSON.stringify(params));
        ElMessage.success("导出成功");
        console.log("日志基本信息文件写入完成");
    } catch (error) {
        console.error("日志基本信息文件写入失败:", error);
    }

    let name =
        deviceId +
        "_" +
        reactiveObj.personInfo.phone +
        "_" +
        dayjs().format("YYYYMMDDHHmmss");
    console.log(name, "日志名称");
    try {
        await window.API.packLog(name);
        console.log("导出任务日志完成");
        try {
            await window.API.writeInfo("");
            console.log("日志基本信息清空完成");
        } catch (error) {
            console.error("日志基本信息清空失败:", error);
        }
    } catch (e) {
        console.log("window.API.packLog() 导出任务日志失败");
    }
}

watch(
    () => props.isOrdering,
    () => {
        window.API.checkSelfNodeStatus();
    },
);

const unregisters: Array<() => void> = [];

onMounted(() => {
    loadPersonInfo();
    unregisters.push(
        window.IPCListeners.onSelfNodeStatusChange((cur) => {
            selfNodeStatusRef.value = cur;
        }),
    );
    const listener = () => {
        window.API.checkSelfNodeStatus();
    };
    window.addEventListener("focus", listener);
    unregisters.push(() => {
        window.removeEventListener("focus", listener);
    });
});
onBeforeUnmount(() => {
    clearInterval(taskLogInter);
    unregisters.forEach((unregister) => unregister());
});
</script>
<style lang="scss">
.task-list-wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    .task-status-export {
        margin-right: 10px;
        display: flex;
        justify-content: right;
        text-align: right;
        position: absolute;
        right: 0;
        top: 2px;
        z-index: 5;
        span {
            color: $main-color;
            cursor: pointer;
        }
        span:hover {
            color: $white;
        }
    }
    .task-list-wrap-list {
        width: 100%;
        min-height: 50px;
        max-height: 200px;
        overflow-y: auto;
        .task-list-wrap-item {
            margin: 0 0px 15px;
            .task-status {
                display: flex;
                margin-bottom: 5px;
                .task-status-label {
                    label {
                        font-size: 14px;
                        padding: 0px 10px;
                        border-radius: 16px;
                        display: inline-flex;
                        align-items: center;
                    }
                }
                .task-status-RUNNING {
                    background: #daffe6;
                    color: #1eb251;
                }
                .task-status-Completed {
                    background: #e4e4e4;
                    color: #000;
                }
                .task-status-FAILED {
                    background: #ffd0d0;
                    color: #ca0909;
                }
                .task-status-PENDING,
                .task-status-UNPLACED {
                    background: #bff5ff;
                    color: #15a3bc;
                }
                .task-status-idle {
                    background-color: #3e77a3;
                    line-height: 1.15;
                }
            }
            .task-status-stop-icon {
                width: 12px;
                height: 12px;
                background-color: #fff;
                margin: 4px;
            }
            .task-time {
                color: $input-border-color;
                font-size: 12px;
            }
        }
    }
    .task-list-wrap-logs {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
        .task-log-title {
            font-size: 14px;
            height: 35px;
            line-height: 35px;
            position: relative;
            img {
                width: 16px;
                height: 16px;
                cursor: pointer;
                top: 10px;
                left: 62px;
                position: absolute;
            }
        }
        .task-log-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            // height: calc(100% - 60px);
        }
    }
    /* 复制提示样式 */
    .copy-tip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        z-index: 9999;
        font-size: 14px;
        animation: fadeInOut 2s ease-in-out;
    }

    /* 淡入淡出动画 */
    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.5s;
    }
    .fade-enter,
    .fade-leave-to {
        opacity: 0;
    }

    /* 自定义动画（可选） */
    @keyframes fadeInOut {
        0% {
            opacity: 0;
            transform: translate(-50%, -40%);
        }
        20% {
            opacity: 1;
            transform: translate(-50%, -50%);
        }
        80% {
            opacity: 1;
            transform: translate(-50%, -50%);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -60%);
        }
    }
    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .animate-spin {
        animation: spin 2s linear infinite;
        transform-origin: center; /* 添加这行，确保从中心点旋转 */
        display: inline-block; /* 添加这行，确保图标正确渲染 */
    }
}
</style>
