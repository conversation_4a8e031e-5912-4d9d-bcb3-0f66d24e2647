<template>
  <div>
    <div class="task-log-title" @click="copyTestContent">复制</div>
    <test ref="testComponent"></test>

    <!-- 复制提示 -->
    <transition name="fade">
      <div v-if="showCopyTip" class="copy-tip">
        已复制
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import test from './test.vue'

  const testComponent = ref<InstanceType<typeof test> | null>(null)
  const showCopyTip = ref(false)

  const copyTestContent = async () => {
    try {
      // 获取test组件的内容 - 更安全的获取方式
      const content = testComponent.value?.$el?.textContent?.trim() || ''

      if (!content) {
        throw new Error('没有可复制的内容')
      }

      // 使用现代Clipboard API
      await navigator.clipboard.writeText(content)

      // 显示提示
      showCopyTip.value = true
      setTimeout(() => showCopyTip.value = false, 3000)

    } catch (err) {
      console.error('复制失败:', err)

      // 回退方案
      try {
        const textarea = document.createElement('textarea')
        textarea.value = testComponent.value?.$el?.textContent?.trim() || ''
        textarea.style.position = 'fixed'
        textarea.style.opacity = '0'
        document.body.appendChild(textarea)
        textarea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textarea)

        if (successful) {
          showCopyTip.value = true
          setTimeout(() => showCopyTip.value = false, 3000)
        } else {
          throw new Error('复制命令执行失败')
        }
      } catch (fallbackErr) {
        alert('复制失败，请手动选择文本后复制')
      }
    }
  }
</script>

<style scoped>
  .task-log-title {
    padding: 8px 16px;
    background-color: #f0f0f0;
    border-radius: 4px;
    display: inline-block;
    cursor: pointer;
    margin-bottom: 10px;
  }

  .task-log-title:hover {
    background-color: #e0e0e0;
  }

  .copy-tip {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 1000;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>