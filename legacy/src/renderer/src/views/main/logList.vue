<template>
  <div class="log-list-wrap">
    <div
      class="scroll-container"
      ref="scrollContainer"
      @scroll.passive="handleScroll"
    >
      <div class="scroll-content">
        <div
          v-for="item in logList"
          :key="item.id"
          @mouseup="handleRowSelection($event, item)"
          class="scroll-item"
          :class="[item.type === 1 ? 'error-item' : 'info-item']"
        >
          <label class="scroll-item-time" v-if="item.t">{{
            dayjs(item.t).format("HH:mm:ss") + " "
          }}</label>
          <label class="scroll-item-type">{{
            item.name.split(".")[0] + " "
          }}</label>
          <label
            class="scroll-item-text"
            :title="item.text"
            style="margin-left: 10px"
            >{{ item.text + "\n" }}</label
          >
        </div>
      </div>
    </div>
    <!-- 复制提示 -->
    <transition name="fade">
      <div
        v-if="showCopyTip"
        class="copy-tip"
        :style="{
          top: `${tipPosition.top - 10}px`,
          left: `${tipPosition.left + tipPosition.width / 2}px`,
          transform: 'translateX(-50%)',
        }"
      >
        已复制
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import dayjs from "dayjs";
import { formatDate } from "../../global/date";

const scrollContainer = ref<HTMLElement | null>(null);
const autoScroll = ref(true);
const logList = ref<any[]>([]); // 根据实际数据类型调整 any
let scrollAnimationFrame: number | null = null;
let dataTimer: number | null = null;

// 平滑滚动到底部（使用requestAnimationFrame）
const smoothScrollToBottom = () => {
  if (!scrollContainer.value || !autoScroll.value) return;

  cancelAnimationFrame(scrollAnimationFrame as number);

  const start = scrollContainer.value.scrollTop;
  const end =
    scrollContainer.value.scrollHeight - scrollContainer.value.clientHeight;
  const duration = 300; // 动画时长（毫秒）
  const startTime = performance.now();

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easeProgress = easeOutQuad(progress);

    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = start + (end - start) * easeProgress;
    }

    if (progress < 1) {
      scrollAnimationFrame = requestAnimationFrame(animate);
    }
  };

  scrollAnimationFrame = requestAnimationFrame(animate);
};

// 缓动函数（平滑曲线）
const easeOutQuad = (t: number) => {
  return t * (2 - t);
};

// 优化后的滚动处理（节流+智能判断）
const handleScroll = () => {
  if (!scrollContainer.value) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
  const distanceToBottom = scrollHeight - (scrollTop + clientHeight);

  // 距离底部50px内视为自动滚动区域
  autoScroll.value = distanceToBottom < 50;
};

async function loadTaskStat() {
  try {
    const newLogs = await window.API.getLog();
    logList.value.pop();
    logList.value = [...logList.value, ...newLogs]; // 正确合并新数据
    // console.log("last", logList.value[logList.value.length - 1].text)
    // console.log(logList.value, '任务日志获取结果')
    logList.value.push({ name: "", text: "", t: 0 });
    if (autoScroll.value) {
      nextTick(smoothScrollToBottom);
    }
  } catch (e) {
    console.log("window.API.getLog 任务日志获取报错");
  }
}

onMounted(() => {
  // 初始化数据
  loadTaskStat();

  // 降低数据更新频率（800ms -> 1000ms）
  dataTimer = window.setInterval(loadTaskStat, 1000);
});

onBeforeUnmount(() => {
  cancelAnimationFrame(scrollAnimationFrame as number);
  if (dataTimer) clearInterval(dataTimer);
});

const showCopyTip = ref(false);
const tipPosition = ref({
  top: 0,
  left: 0,
  width: 0,
});
let copyTimeout = ref(null);

// 处理行选择事件
const handleRowSelection = (event, item) => {
  // 清除之前的定时器
  if (copyTimeout.value) {
    clearTimeout(copyTimeout.value);
  }

  const selection = window.getSelection();
  const selectedText = selection.toString().trim();

  if (selectedText) {
    // 获取选中文本的位置信息
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    tipPosition.value = {
      top: rect.top,
      left: rect.left,
      width: rect.width,
    };

    // 3秒后自动复制
    copyTimeout.value = setTimeout(() => {
      // 构建整行内容
      const fullLineContent = buildFullLineContent(item);
      copyToClipboard(fullLineContent);
    }, 400);
  }
};

// 构建整行内容
const buildFullLineContent = (item) => {
  let line = "";
  if (item.t) {
    line += `${dayjs(item.t).format("HH:mm:ss")} `;
  }
  line += `${item.name.split(".")[0]} `;
  line += item.text;
  return line;
};

// 复制到剪贴板
const copyToClipboard = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        showCopyFeedback();
      })
      .catch((err) => {
        console.error("复制失败:", err);
        fallbackCopyText(text);
      });
  } else {
    fallbackCopyText(text);
  }
};

// 显示复制反馈
const showCopyFeedback = () => {
  showCopyTip.value = true;

  // 1.5秒后自动隐藏提示
  setTimeout(() => {
    showCopyTip.value = false;
  }, 3000);
};

// 兼容性降级方案
const fallbackCopyText = (text) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "fixed";
  document.body.appendChild(textarea);
  textarea.select();

  try {
    const successful = document.execCommand("copy");
    if (successful) {
      showCopyFeedback();
    }
  } catch (err) {
    console.error("复制失败:", err);
  }

  document.body.removeChild(textarea);
};
</script>

<style scoped lang="scss">
/* 关键优化样式 */
.scroll-container {
  height: 200px;
  overflow-y: auto;
  scrollbar-gutter: stable; /* 防止布局抖动 */

  /* 兼容 Firefox 和 IE/Edge */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.scroll-content {
  display: flex;
  flex-direction: column;
  transform: translateZ(0); /* 子元素也加速 */
  overflow: hidden;
}

.scroll-item {
  // height: 27px;
  backface-visibility: hidden; /* 避免闪烁 */
  transform: translateZ(0);
  display: flex;
  line-height: 21px;
  user-select: all !important;
  -webkit-user-select: all !important; /* Safari/Chrome */
  -moz-user-select: all !important; /* Firefox */
  -ms-user-select: all !important; /* IE/Edge */
  .scroll-item-text {
    width: calc(100% - 115px);
    word-break: break-all;
    line-height: 21px;
  }
  .scroll-item-type {
    width: 50px;
    text-align: left;
  }
  .scroll-item-time {
    width: 55px;
  }
}
.error-item {
  color: $main-error;
}
.info-item {
  color: $main-color;
}

/* WebKit 浏览器滚动条样式 */
.scroll-container::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.scroll-container::-webkit-scrollbar-track {
  background: transparent; /* 隐藏轨道背景 */
}

.scroll-container::-webkit-scrollbar-thumb {
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

/* 悬停或滚动时显示滑块 */
.scroll-container:hover::-webkit-scrollbar-thumb,
.scroll-container.scrolling::-webkit-scrollbar-thumb {
  opacity: 1;
}
</style>
