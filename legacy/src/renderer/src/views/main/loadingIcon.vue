<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";

const svg = ref<SVGSVGElement | null>(null);
const unregisters: Array<() => void> = [];

onMounted(() => {
    console.log("svg", svg);
    if (!svg.value) return;
    const listener = () => {
        const isPaused = !document.hasFocus();
        if (isPaused) {
            svg.value?.pauseAnimations();
        } else {
            svg.value?.unpauseAnimations();
        }
    };
    window.addEventListener("focus", listener);
    window.addEventListener("blur", listener);
    unregisters.push(() => {
        window.removeEventListener("focus", listener);
        window.removeEventListener("blur", listener);
    });
});
onBeforeUnmount(() => {
    unregisters.forEach((unregister) => unregister());
});
</script>

<template>
    <svg
        ref="svg"
        xmlns="http://www.w3.org/2000/svg"
        width="200"
        height="200"
        viewBox="0 0 200 200"
        fill="none"
        color="currentColor"
        style="width: 1rem; height: 1rem; padding: 2px"
    >
        <defs>
            <linearGradient id="spinner-secondHalf">
                <stop offset="0%" stop-opacity="0" stop-color="currentColor" />
                <stop
                    offset="100%"
                    stop-opacity="0.5"
                    stop-color="currentColor"
                />
            </linearGradient>
            <linearGradient id="spinner-firstHalf">
                <stop offset="0%" stop-opacity="1" stop-color="currentColor" />
                <stop
                    offset="100%"
                    stop-opacity="0.5"
                    stop-color="currentColor"
                />
            </linearGradient>
        </defs>
        <g stroke-width="32">
            <!-- 半圆 1 -->
            <path
                stroke="url(#spinner-secondHalf)"
                d="M 16 100 A 84 84 0 0 1 184 100"
            />
            <!-- 半圆 2 -->
            <path
                stroke="url(#spinner-firstHalf)"
                d="M 184 100 A 84 84 0 0 1 16 100"
            />
            <!-- 圆角起始标记 -->
            <path
                stroke="currentColor"
                stroke-linecap="round"
                d="M 16 100 A 84 84 0 0 1 16 98"
            />
        </g>
        <animateTransform
            from="0 0 0"
            to="360 0 0"
            attributeName="transform"
            type="rotate"
            repeatCount="indefinite"
            dur="3s"
        />
    </svg>
</template>

<style scoped lang="scss"></style>
