<template>
    <div :class="`main-page-wrap ${isFocused ? '' : 'static-bg-gif'}`">
        <div class="main-page-wrap-default" v-show="checkAllTrue">
            <header-com :netStatus="netStatus"></header-com>
            <div class="main-page-wrap-content">
                <div
                    class="failure-rate"
                    :class="
                        netStatus && checkTrueNum === 6
                            ? 'node-sate-success'
                            : 'node-sate-error'
                    "
                    @click="showCheckList()"
                >
                    <div class="failure-rate-title">系统环境设置项</div>
                    <div class="failure-rate-num">
                        {{ checkTrueNum }}/{{ reactiveObj.checkList.length }}
                    </div>
                </div>

                <!-- 添加系统设置开关区域 -->
                <div class="system-settings">
                    <div class="setting-item">
                        <label>
                            <el-switch
                                v-model="autoOrderValue"
                                @change="handleAutoOrderChange"
                                class="custom-switch"
                                size="small"
                            />
                            <span>自动接单</span>
                        </label>

                        <el-tooltip
                            class="auto-order-help"
                            effect="customized"
                            :content="
                                reactiveObj.disabledAutoOrder
                                    ? '当前手动暂停接单，下次程序启动时将重新自动接单'
                                    : '开启后，程序运行时会自动接单'
                            "
                            placement="top-start"
                        >
                            <el-icon
                                ><svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-circle-help-icon lucide-circle-help"
                                >
                                    <circle cx="12" cy="12" r="10" />
                                    <path
                                        d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"
                                    />
                                    <path d="M12 17h.01" /></svg
                            ></el-icon>
                        </el-tooltip>
                    </div>
                    <div class="setting-item">
                        <label>
                            <el-switch
                                v-model="autoStartEnabled"
                                @change="handleAutoStartChange"
                                class="custom-switch"
                                size="small"
                            />
                            <span>开机自启动</span>
                        </label>
                    </div>
                </div>

                <div class="main-page-wrap-content-task">
                    <task-list
                        :isOrdering="isOrdering"
                        :ready="checkTrueNum === 6"
                    ></task-list>
                </div>
                <span
                    v-if="
                        checkTrueNum === 6 &&
                        taskBtnStatus === 'accept' &&
                        !isUpdating
                    "
                    class="task-btn accept"
                    @click="taskBtn('accept')"
                    >开始接单{{ orderToggling ? "中" : "" }}</span
                >
                <span
                    v-if="
                        checkTrueNum !== 6 &&
                        taskBtnStatus === 'accept' &&
                        !isUpdating
                    "
                    class="task-btn-not-allow"
                    >开始接单</span
                >
                <span
                    v-if="taskBtnStatus === 'stop'"
                    class="task-btn stop"
                    @click="taskBtn('stop')"
                    >暂停接单{{ orderToggling ? "中" : "" }}</span
                >
                <span
                    v-if="isUpdating && taskBtnStatus === 'accept'"
                    class="task-btn-not-allow"
                    >系统更新中</span
                >
            </div>
            <div class="main-page-bottom">
                <span> 设备号：{{ reactiveObj.deviceInfo.showId }} </span>
                <div class="version-info">
                    <span>客户端：v{{ reactiveObj.version }}</span>
                    <span
                        v-if="reactiveObj.mirrorVersion"
                        class="mirror-version"
                        >_{{ reactiveObj.mirrorVersion }}</span
                    >
                </div>
            </div>
        </div>
        <div class="main-page-wrap-check" v-if="!checkAllTrue">
            <div class="main-page-wrap-check-list">
                <div
                    class="main-page-wrap-check-list-item"
                    v-for="(item, index) in reactiveObj.checkList"
                    :key="index"
                >
                    <span class="main-page-wrap-check-list-img">
                        <img
                            v-if="item.status === 'loading'"
                            :src="loadingImg"
                            class="check-item-status-loading"
                            style="width: 16px; height: 16px"
                            :class="
                                item.status === 'loading' ? 'animate-spin' : ''
                            "
                        />
                        <img
                            v-if="item.status === 'updating'"
                            :src="loadingImg"
                            class="check-item-status-loading"
                            style="width: 16px; height: 16px"
                            :class="
                                item.status === 'updating' ? 'animate-spin' : ''
                            "
                        />
                        <img
                            v-if="item.status === 'success'"
                            :src="successImg"
                            class="check-item-status-loading"
                        />
                        <img
                            v-if="item.status === 'error'"
                            :src="errorImg"
                            class="check-item-status-loading"
                        />
                        <img
                            v-if="item.status === 'unsatisfied'"
                            :src="errorImg"
                            class="check-item-status-loading"
                        />
                        <img
                            v-if="item.status === 'hasUpdate'"
                            :src="warningImg"
                            class="check-item-status-loading"
                        />
                    </span>

                    <label
                        class="check-item-label"
                        :class="[
                            item.status === 'checkout'
                                ? 'gray-color'
                                : item.status === 'error' ||
                                    item.status === 'unsatisfied'
                                  ? 'error-color'
                                  : '',
                        ]"
                    >
                        {{ item["label"][item.status + "Label"] }}</label
                    >
                    <label
                        v-if="
                            item.status === 'error' ||
                            item.status === 'unsatisfied'
                        "
                        class="check-item-btn"
                    >
                        <label
                            class="check-item-btn-label"
                            v-if="!item.progress"
                            @click="installCheck(item)"
                            >{{ item.btnLabel }}</label
                        >
                        <label
                            class="item-progress"
                            v-if="item.alias === 'mirror' && item.progress"
                        >
                            {{ item.btnLabel }}中 {{ currPercent
                            }}<img
                                v-if="currPercent.length === 0"
                                src="@assets/images/loading.png"
                                style="position: relative; top: 2px"
                                class="check-item-status-loading animate-spin"
                        /></label>
                        <label
                            class="item-progress"
                            v-if="item.alias === 'wsl' && item.progress"
                        >
                            安装中...
                            <img
                                src="@assets/images/loading.png"
                                style="position: relative; top: 2px"
                                class="check-item-status-loading animate-spin"
                            />
                        </label>
                        <label
                            class="item-progress"
                            v-if="
                                item.alias === 'mirror' &&
                                item.status === 'updating'
                            "
                        >
                            镜像更新中 {{ updateDownloadPercent
                            }}<img
                                v-if="updateDownloadPercent.length === 0"
                                src="@assets/images/loading.png"
                                style="position: relative; top: 2px"
                                class="check-item-status-loading animate-spin"
                            />
                        </label>
                    </label>
                    <label
                        v-if="
                            item.status === 'hasUpdate' &&
                            item.alias === 'mirror'
                        "
                        class="check-item-btn"
                    >
                        <label class="item-progress" v-if="item.progress">
                            下载更新中 {{ updateDownloadPercent
                            }}<img
                                v-if="updateDownloadPercent.length === 0"
                                src="@assets/images/loading.png"
                                style="position: relative; top: 2px"
                                class="check-item-status-loading animate-spin"
                            />
                        </label>
                    </label>
                </div>
            </div>
            <x-button
                type="confirm"
                style="width: 60%; margin: auto"
                @click="checkAllTrue = true"
                >关闭</x-button
            >
            <div class="main-page-wrap-check-reload" @click="loadCheckStatus()">
                重新检查
            </div>
        </div>
    </div>
</template>

<script setup>
import headerCom from "@/components/header.vue";
import taskList from "./taskList.vue";
import xButton from "@/components/x-button.vue";
import { LogSource, LogLevel } from "@/types/enums";
import { ElMessage } from "element-plus";
import Log from "@/types/Log";
import Logger from "@/mixins/logger";
import dayjs from "dayjs";
import { _api, _http } from "./../../mixins/service";
import mirrorVersionManager from "@srcUtils/MirrorVersionManager.js";

import loadingImg from "@assets/images/loading.png";
import successImg from "@assets/images/success.png";
import errorImg from "@assets/images/error.png";
import warningImg from "@assets/images/task/preparation.png"; // 使用准备中图标作为警告图标
import { useCheckUpdate } from "@hooks/useCheckUpdate";

let taskBtnStatus = ref("accept");
let checkAllTrue = ref(true);
let checkTrueNum = ref(0);
let checkTrueList = {
    platformCheckResult: false,
    osCheckResult: false,
    powershellCheckResult: false,
    virtualCheckResult: false,
    wslCheckResult: false,
    mirrorCheckResult: false,
};
let netStateInter = ref(false);
let checkEnvInter = ref(false);
let netStatus = ref(false);
let inMirrorStatInter = ref(0.0);
let currPercent = ref("...");
let isOrdering = ref(false);
let updateDownloadPercent = ref(""); // 更新下载进度
let isUpdating = ref(false); // 是否正在更新
let currentUpdateInfo = null; // 保存当前的更新信息
// 添加系统设置相关的响应式变量
let autoOrderEnabled = ref(false); // 开机接单开关
let autoStartEnabled = ref(false); // 开机自启动开关
const orderToggling = ref(false);
let reactiveObj = reactive({
    input: "",
    deviceId: "",
    version: "",
    autoMissingDeps: new Set(["wsl", "mirror"]),
    mirrorVersion: "",
    checkList: [
        {
            status: "checkout", // checkout, loading, success, error
            label: {
                checkoutLabel: "操作系统待检测",
                loadingLabel: "操作系统是否符合要求检测中",
                successLabel: "操作系统符合要求",
                errorLabel: "操作系统不符合要求",
            },
            alias: "platform",
            btnLabel: "",
            progress: false,
        },
        {
            status: "checkout", // checkout, loading, success, error
            label: {
                checkoutLabel: "系统版本待检测",
                loadingLabel: "系统版本是否符合要求检测中",
                successLabel: "系统版本符合要求",
                errorLabel: "系统版本不符合要求",
            },
            alias: "os",
            btnLabel: "",
            progress: false,
        },
        {
            status: "checkout", // checkout, loading, success, error
            label: {
                checkoutLabel: "PowerShell是否安装待检测",
                loadingLabel: "PowerShell是否安装检测中",
                successLabel: "PowerShell已安装",
                errorLabel: "PowerShell未安装",
            },
            alias: "powershell",
            btnLabel: "",
            progress: false,
        },
        {
            status: "checkout", // checkout, loading, success, error
            label: {
                checkoutLabel: "BIOS虚拟化是否开启待检测",
                loadingLabel: "BIOS虚拟化是否开启检测中",
                successLabel: "BIOS虚拟化已开启",
                errorLabel: "BIOS虚拟化未开启",
            },
            alias: "virtual",
            btnLabel: "设置",
            progress: false,
        },
        {
            status: "checkout", // checkout, loading, success, error, unsatisfied
            label: {
                checkoutLabel: "WSL2是否安装待检测",
                loadingLabel: "WSL2是否安装检测中",
                successLabel: "WSL2已安装",
                errorLabel: "WSL2未安装",
                unsatisfiedLabel: "WSL2未安装",
            },
            alias: "wsl",
            btnLabel: "安装",
            progress: false,
        },
        {
            status: "checkout", // checkout, loading, success, error, hasUpdate
            label: {
                checkoutLabel: "任务引擎是否安装待检测",
                loadingLabel: "任务引擎是否安装检测中",
                successLabel: "任务引擎已安装",
                errorLabel: "任务引擎未安装",
                updatingLabel: "任务引擎正在更新",
                hasUpdateLabel: "任务引擎可更新",
            },
            alias: "mirror",
            btnLabel: "安装",
            progress: false,
        },
    ],
    taskList: [],
    /**
     * @type {null | {runStatus: boolean, sourceBtnStatus: string}}
     */
    checkMirrorRunResult: null,
    deviceInfo: {
        id: "",
        showId: "",
    },
    disabledAutoOrder: false,
});
const isFocused = ref(false);
const isVisibility = ref(false);

const autoOrderValue = computed(() => {
    if (reactiveObj.disabledAutoOrder) return false;
    return autoOrderEnabled.value;
});

/**
 * @type {Array<() => void>}
 */
const unregisters = [];
const updater = useCheckUpdate();
/**
 * 自动安装镜像和 WSL
 */
function autoInstallDeps() {
    if (reactiveObj.autoMissingDeps.size === 0) return false;
    const checkList = reactiveObj.checkList;
    let installFlag = undefined;
    const autoInstallItems = ["wsl", "mirror"];
    for (let i = 0; i < checkList.length; i++) {
        const item = checkList[i];
        if (!autoInstallItems.includes(item.alias)) {
            continue;
        }
        if (item.progress) break;
        else if (item.status === "unsatisfied") break;
        else if (item.status === "success") {
            reactiveObj.autoMissingDeps.delete(item.alias);
        } else {
            installFlag = item.alias;
            break;
        }
    }
    let mirrorItem = installFlag
        ? checkList.find((item) => item.alias === installFlag)
        : undefined;
    console.log("自动安装依赖", checkList, reactiveObj.autoMissingDeps, {
        installFlag,
        mirrorItem,
    });
    if (mirrorItem) {
        // debugger
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `开始尝试自动安装 ${mirrorItem.alias}`,
                true,
            ),
        );
        installCheck(mirrorItem);
    }
    return reactiveObj.autoMissingDeps.size !== 0;
}
/**
 *
 * @param {'accept' | 'stop'} alias
 * @param manual 标记是否为用户触发，默认: true
 */
async function taskBtn(alias, manual = true) {
    if (orderToggling.value) {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                "接单状态切换中，屏蔽重复点击",
            ),
        );
        return;
    }
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            `切换为${alias === "accept" ? "开始接单" : "暂停接单"}, 由 ${!manual ? "自动接单" : "用户"} 触发`,
            true,
        ),
    );

    // 如果是开始接单，确保只执行一次镜像检查
    if (alias === "accept") {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                "Check for updates on the client",
                true,
            ),
        );
        // if (await updater.check()){
        //   return
        // }
        // 禁用更新彈窗
        updater.disable();
        console.log("Start accepting tasks process, check image status");
        reactiveObj.disabledAutoOrder = false;
    }
    // 如果是停止接单，重置所有状态
    else if (alias === "stop") {
        updater.resume();
        // 设置用户手动暂停标志
        userManuallyPaused = true;
        console.log(
            "🚫 User manually paused task acceptance, auto-start disabled until app restart",
        );

        // 停止自动接单监听器
        if (autoStartWatcher) {
            clearInterval(autoStartWatcher);
            autoStartWatcher = null;
            console.log("🛑 Auto-start watcher stopped due to manual pause");
        }

        try {
            // 重置自动接单状态
            await window.API.resetTaskAcceptance();
            // 临时禁用自动接单
            reactiveObj.disabledAutoOrder = true;
            console.log("Auto-acceptance state reset");
        } catch (e) {
            console.error("Reset auto-acceptance state failed", e);
        }
    }

    // 检查镜像是否在运行中
    checkMirrorRunning(alias);
}
// 检查镜像是否在运行中
async function checkMirrorRunning(status) {
    // 如果已经有镜像启动进程在进行中，跳过重复检查
    if (orderToggling.value && status === "accept") {
        console.log(
            "Image start process is in progress, skipping repeated check",
        );
        return;
    }
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            `检查镜像是否运行中并标记状态为 ${reactiveObj.checkMirrorRunResult?.sourceBtnStatus} => ${status}`,
        ),
    );
    try {
        let mirrorIsRun = await window.API.checkMirror();
        console.log(
            mirrorIsRun,
            "window.API.checkMirror() check image status result",
        );
        // 返回结果中，running为true时，说明镜像在运行中，为false时，说明镜像为停止状态
        reactiveObj.checkMirrorRunResult = {
            runStatus: mirrorIsRun["running"],
            sourceBtnStatus: status,
        };
    } catch (e) {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.ERROR,
                `检查镜像是否运行失败`,
            ),
        );
        console.log(
            "window.API.checkMirror() check image status result error:",
            e,
        );
    }
}
/**
 * 运行镜像
 * @param {AbortSignal}signal
 * @returns {Promise<boolean>}
 */
async function runMirror(signal) {
    console.log("Starting image...");
    signal.addEventListener("abort", () => {
        window.API.abortRunMirror(signal.reason);
    });
    try {
        let mirrorIsRun = await window.API.runMirror();
        console.log(mirrorIsRun, "window.API.runMirror image start result");

        if (mirrorIsRun) {
            // 启动命令执行成功，等待一段时间后检查镜像是否真正运行起来
            return new Promise((resolve) => {
                console.log(
                    "Image start command executed successfully, waiting for image start...",
                );
                // 增加等待时间，给镜像更多时间启动
                setTimeout(async () => {
                    try {
                        // 检查镜像是否真正运行
                        const checkResult = await window.API.checkMirror();
                        console.log("Image start status check:", checkResult);

                        // reactiveObj.checkMirrorRunResult = {
                        //     runStatus: checkResult.running,
                        //     sourceBtnStatus: status,
                        // };

                        resolve(checkResult.running);
                    } catch (err) {
                        console.error("Image start status check failed:", err);
                        resolve(false);
                    }
                }, 3000); // 增加等待时间到3秒
            });
        } else {
            console.log("Image start command execution failed");
            return Promise.resolve(false);
        }
    } catch (e) {
        if (signal.aborted) return Promise.resolve(false);
        console.error("Image start process error:", e);
        return Promise.reject(e);
    }
}
// 停止镜像
async function stopMirror() {
    try {
        await window.API.stopMirror();
        console.log("window.API.stopMirror image stop success");
    } catch (e) {
        console.log("window.API.stopMirror run image error");
        throw e;
    }
}
// 检查环境状态
async function checkPlatform() {
    reactiveObj.checkList[0]["status"] = "loading";
    try {
        let checkPlatform = window.versions.platform();
        reactiveObj.checkList[0]["status"] =
            checkPlatform === "win32" ? "success" : "error";
        console.log(
            checkPlatform,
            "window.versions.platform check operating system check result",
        );
        if (checkPlatform === "win32") {
            checkTrueList["platformCheckResult"] = true;
            checkOS();
        } else {
            checkTrueList["platformCheckResult"] = false;
        }
        statisticCheckTrueNum();
    } catch (e) {
        console.log("window.versions.platform operating system check error");
        reactiveObj.checkList[0]["status"] = "error";
        reactiveObj.checkList[1]["showLabel"] =
            "Operating System does not meet the requirements";
    }
}
async function checkOS() {
    reactiveObj.checkList[1]["status"] = "loading";
    try {
        let checkOS = await window.API.checkOS();
        reactiveObj.checkList[1]["status"] = checkOS ? "success" : "error";
        console.log(
            checkOS,
            "window.API.checkOS check operating system version result",
        );
        if (checkOS) {
            checkTrueList["osCheckResult"] = true;
            checkPowerShell();
        } else {
            checkTrueList["osCheckResult"] = false;
        }
        statisticCheckTrueNum();
    } catch (e) {
        console.log("window.API.checkOS check operating system version error");
        reactiveObj.checkList[1]["status"] = "error";
        reactiveObj.checkList[1]["showLabel"] =
            "Operating System version does not meet the requirements";
    }
}
async function checkPowerShell() {
    reactiveObj.checkList[2]["status"] = "loading";
    try {
        let isInstallPs = await window.API.checkPowerShell();
        reactiveObj.checkList[2]["status"] = isInstallPs ? "success" : "error";
        console.log(
            isInstallPs,
            "window.API.checkPowerShell PowerShell check result",
        );
        if (isInstallPs) {
            checkTrueList["powershellCheckResult"] = true;
            checkVirtual();
        } else {
            checkTrueList["powershellCheckResult"] = false;
        }
        statisticCheckTrueNum();
    } catch (e) {
        console.log(
            "window.API.checkPowerShell check if install checkPowerShell error",
        );
        reactiveObj.checkList[2]["status"] = "error";
    }
}
async function checkVirtual() {
    reactiveObj.checkList[3]["status"] = "loading";
    try {
        let checkVirtualResult = await window.API.checkVirtual();
        reactiveObj.checkList[3]["status"] = checkVirtualResult
            ? "success"
            : "error";
        console.log(
            checkVirtualResult,
            "window.API.checkVirtual virtual check result",
        );
        if (checkVirtualResult) {
            checkTrueList["virtualCheckResult"] = true;
            checkWSLEnabled();
        } else {
            checkTrueList["virtualCheckResult"] = false;
        }
        statisticCheckTrueNum();
    } catch (e) {
        console.log("window.API.checkVirtual() virtual check error");
        reactiveObj.checkList[3]["status"] = "error";
    }
}
async function checkWSLEnabled() {
    reactiveObj.checkList[4]["status"] = "loading";
    try {
        let checkWlsResult = await window.API.checkWSLEnabled();
        if (checkWlsResult < 0) {
            reactiveObj.checkList[4]["status"] = "unsatisfied";
            checkTrueList["wslCheckResult"] = false;
            reactiveObj.checkList[4].btnLabel = "启用";
            switch (checkWlsResult) {
                case -1:
                    reactiveObj.checkList[4].label.unsatisfiedLabel =
                        "未启用 WSL";
                    break;
                case -2:
                    reactiveObj.checkList[4].label.unsatisfiedLabel =
                        "未启用 HyperV";
            }
            return;
        }
        reactiveObj.checkList[4].btnLabel = "安装";
        reactiveObj.checkList[4]["status"] = checkWlsResult
            ? "success"
            : "error";
        console.log(
            checkWlsResult,
            "window.API.checkWSLEnabled WSL2 check result",
        );
        if (checkWlsResult) {
            checkTrueList["wslCheckResult"] = true;
            checkMirror();
        } else {
            checkTrueList["wslCheckResult"] = false;
        }
        statisticCheckTrueNum();
    } catch (e) {
        console.log("window.API.checkWSLEnabled() WSL2 check error");
        reactiveObj.checkList[4]["status"] = "error";
    }
}
let isFirstInstallWaiting = false; // 首次安装等待期标志

async function checkMirror() {
    // 如果正在首次安装等待期，直接返回
    if (isFirstInstallWaiting) {
        console.log("⏳ 首次安装等待期间，跳过镜像检查");
        return;
    }

    reactiveObj.checkList[5]["status"] = "loading";
    try {
        let checkMissor = await window.API.checkMirror();
        reactiveObj.checkList[5]["status"] = checkMissor["installed"]
            ? "success"
            : "error";
        console.log(
            checkMissor,
            "window.API.checkMirror task engine check result",
        );
        if (checkMissor["installed"]) {
            checkTrueList["mirrorCheckResult"] = true;

            console.log("镜像检查完成，检查版本文件是否存在...");
            console.log("镜像状态:", checkMissor);

            // 检查版本文件是否存在
            const hasVersionFile =
                await mirrorVersionManager.hasVersionRecord();
            console.log("版本文件检查结果:", hasVersionFile);

            if (!hasVersionFile) {
                console.log(
                    "首次安装：没有版本文件，立即保存版本信息并等待3分钟后检查更新",
                );
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        "首次安装：没有版本文件，立即保存版本信息并等待3分钟后检查更新",
                        true,
                    ),
                );

                // 设置等待标志
                isFirstInstallWaiting = true;

                await saveMirrorVersionIfNotExists();
                setTimeout(
                    async () => {
                        console.log("首次安装等待期结束，开始静默更新检查");
                        Logger(
                            new Log(
                                dayjs(),
                                LogSource.UI,
                                LogLevel.INFO,
                                "首次安装等待期结束，开始静默更新检查",
                                true,
                            ),
                        );
                        isFirstInstallWaiting = false; // 清除等待标志
                        await checkMirrorUpdatesQuietly();
                    },
                    3 * 60 * 1000,
                );
                console.log("首次安装静默更新检查已安排在3分钟后执行");
            } else {
                console.log("非首次安装：已有版本文件，立即进行静默更新检查");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        "非首次安装：已有版本文件，立即进行静默更新检查",
                        true,
                    ),
                );
                await checkMirrorUpdatesQuietly();
                console.log("静默更新检查调用完成");
            }
        } else {
            checkTrueList["mirrorCheckResult"] = false;
        }
        statisticCheckTrueNum();
    } catch (e) {
        console.log("window.API.checkMirror() task engine check error");
        reactiveObj.checkList[5]["status"] = "error";
    }
}

/**
 * 静默检查镜像更新
 */
async function checkMirrorUpdatesQuietly() {
    console.log("=== checkMirrorUpdatesQuietly 函数被调用 ===");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            "checkMirrorUpdatesQuietly 函数被调用",
            true,
        ),
    );

    const mirrorIndex = reactiveObj.checkList.findIndex(
        (item) => item.alias === "mirror",
    );
    if (mirrorIndex === -1) {
        console.log("未找到镜像检查项，退出更新检查");
        return;
    }

    try {
        console.log("=== 开始静默检查镜像版本更新 ===");
        console.log("镜像检查项索引:", mirrorIndex);
        console.log(
            "当前镜像检查项状态:",
            reactiveObj.checkList[mirrorIndex].status,
        );
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                "开始静默检查镜像版本更新",
                true,
            ),
        );

        // 3分钟等待期已过，直接进行版本检查
        console.log("⏳ 执行镜像版本更新检查...");
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                "执行镜像版本更新检查",
                true,
            ),
        );

        const result = await mirrorVersionManager.checkAndDownloadUpdates();
        console.log("📋 版本检查结果:", result);
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `版本检查结果: ${result?.status}`,
                true,
            ),
        );

        if (result) {
            switch (result.status) {
                case "up_to_date":
                    console.log("镜像版本已是最新，无需更新");
                    console.log("当前版本:", result.updateInfo?.currentVersion);
                    console.log("服务器版本:", result.updateInfo?.newVersion);
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.UI,
                            LogLevel.INFO,
                            `镜像版本已是最新 (${result.updateInfo?.currentVersion})`,
                            true,
                        ),
                    );
                    // 保持success状态
                    break;

                case "download_started":
                    console.log("发现镜像更新，开始下载...");
                    console.log("当前版本:", result.updateInfo?.currentVersion);
                    console.log("新版本:", result.updateInfo?.newVersion);
                    console.log("下载地址:", result.updateInfo?.downloadUrl);
                    console.log("更新说明:", result.updateInfo?.releaseNotes);
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.UI,
                            LogLevel.INFO,
                            `发现镜像更新 ${result.updateInfo?.currentVersion} -> ${result.updateInfo?.newVersion}`,
                            true,
                        ),
                    );

                    // 保存更新信息供后续使用
                    currentUpdateInfo = result.updateInfo;
                    console.log("💾 保存更新信息供后续使用");

                    console.log("🔄 更新镜像检查项状态为 hasUpdate");
                    reactiveObj.checkList[mirrorIndex]["status"] = "hasUpdate";
                    reactiveObj.checkList[mirrorIndex]["progress"] = true;
                    // 注意：下载期间不设置 isUpdating，用户仍可以接单
                    // isUpdating.value = true
                    console.log("📥 镜像下载期间，用户仍可以接单");
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.UI,
                            LogLevel.INFO,
                            "更新镜像检查项状态为hasUpdate，下载期间用户可以接单",
                            true,
                        ),
                    );

                    console.log("📡 启动镜像更新进度监听器");
                    // 开始监听下载进度
                    startMirrorUpdateProgressWatcher();
                    break;

                case "download_failed":
                case "check_failed":
                case "error":
                    console.error("❌ 镜像更新检查失败:", result.error);
                    console.log("保持镜像success状态，不影响接单");
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.UI,
                            LogLevel.ERROR,
                            `镜像更新检查失败: ${result.error}`,
                            true,
                        ),
                    );
                    // 保持success状态，不影响接单
                    break;
            }
        } else {
            console.warn("⚠️ 版本检查结果为空");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.WARN,
                    "版本检查结果为空",
                    true,
                ),
            );
        }
    } catch (error) {
        console.error("💥 静默检查镜像更新时出错:", error);
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.ERROR,
                `静默检查镜像更新时出错: ${error.message}`,
                true,
            ),
        );
        // 保持success状态，不影响接单
    }

    console.log("=== 静默检查镜像更新完成 ===");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            "静默检查镜像更新完成",
            true,
        ),
    );
}

/**
 * 监听镜像更新下载进度
 */
function startMirrorUpdateProgressWatcher() {
    console.log("=== 启动镜像更新进度监听器 ===");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            "启动镜像更新进度监听器",
            true,
        ),
    );

    // 保存当前的更新信息，防止在监听过程中被清除
    console.log("📋 进度监听器启动时的更新信息:", currentUpdateInfo);

    const progressInterval = setInterval(async () => {
        try {
            const progress = await mirrorVersionManager.getDownloadProgress();
            console.log("获取下载进度:", progress);

            if (progress.isDownloading) {
                updateDownloadPercent.value = `${progress.progress}%`;
                console.log("镜像更新下载进度:", progress.progress + "%");
                console.log("界面显示进度:", updateDownloadPercent.value);
                // 每10%记录一次日志，避免日志过多
                if (progress.progress % 10 === 0) {
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.UI,
                            LogLevel.INFO,
                            `镜像更新下载进度: ${progress.progress}%`,
                            true,
                        ),
                    );
                }
            } else {
                // 下载完成
                console.log("镜像更新下载完成!");
                console.log("📋 下载完成时的更新信息:", currentUpdateInfo);
                console.log("清理进度监听器");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        "镜像更新下载完成",
                        true,
                    ),
                );
                clearInterval(progressInterval);
                updateDownloadPercent.value = "";

                const mirrorIndex = reactiveObj.checkList.findIndex(
                    (item) => item.alias === "mirror",
                );
                if (mirrorIndex !== -1) {
                    console.log("恢复镜像检查项状态为 success");
                    reactiveObj.checkList[mirrorIndex]["status"] = "success";
                    reactiveObj.checkList[mirrorIndex]["progress"] = false;
                }

                // 注意：这里不重置isUpdating，因为后续还要执行镜像安装更新
                // isUpdating.value = false
                console.log("保持更新状态，准备执行安装更新...");
                console.log("准备执行更新后处理...");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        "准备执行更新后处理",
                        true,
                    ),
                );

                // 这里可以添加后续的更新逻辑
                await handleMirrorUpdateAfterDownload();
            }
        } catch (error) {
            console.error("获取镜像更新下载进度失败:", error);
            console.log("出错时清理监听器和状态");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.ERROR,
                    `获取镜像更新下载进度失败: ${error.message}`,
                    true,
                ),
            );
            clearInterval(progressInterval);
            updateDownloadPercent.value = "";
            // 出错时恢复镜像状态
            const mirrorIndex = reactiveObj.checkList.findIndex(
                (item) => item.alias === "mirror",
            );
            if (mirrorIndex !== -1) {
                console.log("出错时恢复镜像检查项状态为 success");
                reactiveObj.checkList[mirrorIndex]["status"] = "success";
                reactiveObj.checkList[mirrorIndex]["progress"] = false;
            }

            // 清除保存的更新信息
            currentUpdateInfo = null;
        }
    }, 1000); // 每秒检查一次进度

    console.log("进度监听器已启动，每1秒检查一次");
}

/**
 * 镜像更新下载完成后处理
 * 注意：此时下载已完成，但用户仍可以接单
 * 只有在调用 performMirrorUpdate 开始执行镜像替换时才会禁止接单
 */
async function handleMirrorUpdateAfterDownload() {
    console.log("=== 开始处理镜像更新下载完成后的逻辑 ===");
    console.log("💡 提示：下载完成，但用户仍可以接单，直到开始执行镜像替换");

    try {
        console.log("检查当前应用状态...");
        console.log("当前任务按钮状态:", taskBtnStatus.value);

        // 检查nomad是否有活跃任务（目前默认返回false，表示没有任务）
        const hasActiveTasks = await checkNomadActiveTasks();
        console.log("检查活跃任务状态:", hasActiveTasks);

        if (!hasActiveTasks) {
            console.log("当前没有活跃任务，立即开始镜像更新流程...");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    "当前没有活跃任务，立即开始镜像更新流程",
                    true,
                ),
            );

            await performMirrorUpdate();

            // 更新完成后恢复按钮状态
            isUpdating.value = false;
            console.log("✅ 镜像替换完成，恢复用户接单能力");
            return;
        } else {
            console.log("当前有活跃任务，延迟镜像更新直到任务完成");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    "当前有活跃任务，延迟镜像更新直到任务完成",
                    true,
                ),
            );

            // 有任务时也需要等待，保持更新状态，禁用开始接单
            console.log("提示：任务完成后需要手动触发更新");
        }
    } catch (error) {
        console.error("处理镜像更新时出错:", error);
        console.error("错误详情:", error.stack);
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.ERROR,
                `处理镜像更新时出错: ${error.message}`,
                true,
            ),
        );

        // 出错时也要恢复按钮状态
        isUpdating.value = false;
    }

    console.log("=== 镜像更新下载完成后处理结束 ===");
}

/**
 * 检查nomad是否有活跃任务
 * @returns {boolean} 是否有活跃任务
 */
async function checkNomadActiveTasks() {
    try {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                "检查任务列表中是否有运行中的任务...",
                true,
            ),
        );

        // 获取设备ID
        let deviceId = null;
        try {
            deviceId = await window.API.getUID();
        } catch (e) {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.WARN,
                    "获取设备ID失败，使用默认值",
                    true,
                ),
            );
        }

        // 调用任务实例列表接口
        const params = {
            is_running: true,
            machine_id: deviceId,
        };

        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `调用任务实例列表接口，参数: ${JSON.stringify(params)}`,
                true,
            ),
        );
        const res = await _http.common.fetchPost(
            params,
            _api.taskInstanceList,
            _api.baseUrl,
        );

        if (res && res["success"] && res.data && res.data.length > 0) {
            // 获取第一个任务（最新的任务）
            const firstTask = res.data[0];

            // 临时修改：手动将 RUNNING 状态改为 COMPLETED，用于测试镜像更新
            // if (firstTask.status === 'RUNNING') {
            //   Logger(new Log(dayjs(), LogSource.UI, LogLevel.WARN, `[测试模式] 检测到任务状态为 RUNNING，手动改为 COMPLETED 以测试镜像更新`, true))
            //   firstTask.status = 'COMPLETED'
            // }

            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `第一个任务状态: ${firstTask.status}`,
                    true,
                ),
            );
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `任务详情: id=${firstTask.id}, status=${firstTask.status}, create_time=${firstTask.create_time}`,
                    true,
                ),
            );

            // 如果第一个任务的状态是 RUNNING，说明有活跃任务
            if (firstTask.status === "RUNNING") {
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        "发现运行中的任务，不允许更新",
                        true,
                    ),
                );
                return true;
            } else {
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        `任务状态不是RUNNING(当前状态: ${firstTask.status})，允许更新`,
                        true,
                    ),
                );
                return false;
            }
        } else {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `没有找到任务或接口返回失败，允许更新。res.success=${res?.success}, data.length=${res?.data?.length}`,
                    true,
                ),
            );
            return false;
        }
    } catch (error) {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.ERROR,
                `检查任务状态时出错: ${error.message}`,
                true,
            ),
        );
        // 出错时假设没有任务，允许更新
        return false;
    }
}

/**
 * 执行镜像更新
 */
async function performMirrorUpdate() {
    console.log("=== 开始执行镜像更新 ===");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            "开始执行镜像更新流程",
            true,
        ),
    );

    // 设置更新状态，禁止用户接单
    isUpdating.value = true;
    console.log("🚫 开始执行镜像替换，禁止用户接单");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            "开始执行镜像替换，禁止用户接单",
            true,
        ),
    );

    try {
        const updateFilePath =
            "C:\\Users\\<USER>\\AppData\\Roaming\\echowave_client\\mock-mirror-v1.1.tar";
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `镜像更新文件路径: ${updateFilePath}`,
                true,
            ),
        );

        // 先调用镜像更新API进行WSL停止、卸载、导入操作
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                "调用镜像更新API进行WSL操作",
                true,
            ),
        );

        const updateResult = await window.API.updateMirror(updateFilePath);
        console.log("📋 镜像更新API结果:", updateResult);
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `镜像更新API结果: ${JSON.stringify(updateResult)}`,
                true,
            ),
        );

        if (updateResult && updateResult.success) {
            // 启动安装进度监听器，检查EchoWaveUbuntu是否导入成功
            let installProgressWatcher = null;
            let installCheckAttempts = 0;
            const maxInstallCheckAttempts = 60; // 最多检查60次，每次间隔2秒，总共2分钟

            installProgressWatcher = setInterval(async () => {
                installCheckAttempts++;

                try {
                    // 使用新的API检查特定WSL实例
                    const checkResult =
                        await window.API.checkWSLInstance("EchoWaveUbuntu");

                    if (checkResult.success && checkResult.exists) {
                        // 清除监听器
                        if (installProgressWatcher) {
                            clearInterval(installProgressWatcher);
                            installProgressWatcher = null;
                        }

                        // 更新本地版本记录
                        // 使用保存的更新信息中的新版本号
                        const newVersion =
                            currentUpdateInfo?.newVersion || "1.1";
                        const releaseNotes =
                            currentUpdateInfo?.releaseNotes ||
                            "镜像更新：修复了一些问题，提升了性能";

                        console.log("💾 准备更新本地版本记录...");
                        console.log("📋 当前更新信息:", currentUpdateInfo);
                        console.log("📋 新版本号:", newVersion);
                        console.log("📋 更新文件路径:", updateFilePath);
                        console.log("📋 更新说明:", releaseNotes);

                        const versionUpdateResult =
                            await mirrorVersionManager.saveMirrorVersion(
                                newVersion, // 使用实际的新版本号
                                updateFilePath,
                                releaseNotes, // 使用实际的更新说明
                            );

                        console.log(
                            "💾 版本记录更新结果:",
                            versionUpdateResult,
                        );

                        if (versionUpdateResult) {
                            console.log("✅ 镜像版本记录更新成功!");

                            // 立即验证版本是否真的保存了
                            const verifyVersion =
                                await mirrorVersionManager.getMirrorVersion();
                            console.log(
                                "🔍 验证保存的版本信息:",
                                verifyVersion,
                            );

                            // 更新界面显示的镜像版本
                            if (verifyVersion && verifyVersion.version) {
                                reactiveObj.mirrorVersion =
                                    verifyVersion.version;
                                console.log(
                                    "✅ 界面镜像版本已更新为:",
                                    verifyVersion.version,
                                );
                            }

                            // 删除下载的 tar 文件
                            try {
                                await window.API.deleteFile(updateFilePath);
                                console.log(
                                    "🗑️ 已删除镜像更新文件:",
                                    updateFilePath,
                                );
                                Logger(
                                    new Log(
                                        dayjs(),
                                        LogSource.UI,
                                        LogLevel.INFO,
                                        `已删除镜像更新文件: ${updateFilePath}`,
                                        true,
                                    ),
                                );
                            } catch (deleteError) {
                                console.error(
                                    "❌ 删除镜像文件失败:",
                                    deleteError,
                                );
                                Logger(
                                    new Log(
                                        dayjs(),
                                        LogSource.UI,
                                        LogLevel.WARN,
                                        `删除镜像文件失败: ${deleteError.message}`,
                                        true,
                                    ),
                                );
                            }

                            Logger(
                                new Log(
                                    dayjs(),
                                    LogSource.UI,
                                    LogLevel.INFO,
                                    "完整的镜像更新流程完成",
                                    true,
                                ),
                            );

                            // 清除保存的更新信息
                            currentUpdateInfo = null;
                        } else {
                            console.error("❌ 镜像版本记录更新失败");
                            Logger(
                                new Log(
                                    dayjs(),
                                    LogSource.UI,
                                    LogLevel.ERROR,
                                    "镜像版本记录更新失败",
                                    true,
                                ),
                            );
                        }

                        // 更新完成后恢复按钮状态
                        isUpdating.value = false;
                        console.log("✅ 镜像替换完成，恢复用户接单能力");
                        return;
                    }

                    // 如果达到最大尝试次数但仍未检测到镜像
                    if (installCheckAttempts >= maxInstallCheckAttempts) {
                        console.log(
                            "⏰ 达到最大检查次数，尝试保存版本信息以防镜像实际已导入成功",
                        );

                        // 尝试保存版本信息，因为镜像可能已经成功导入但检查失败
                        const newVersion =
                            currentUpdateInfo?.newVersion || "1.1";
                        const releaseNotes =
                            currentUpdateInfo?.releaseNotes ||
                            "镜像更新：修复了一些问题，提升了性能";

                        console.log("💾 尝试保存版本信息...");
                        console.log("📋 当前更新信息:", currentUpdateInfo);
                        console.log("📋 新版本号:", newVersion);

                        await mirrorVersionManager.saveMirrorVersion(
                            newVersion,
                            updateFilePath,
                            releaseNotes,
                        );

                        // 清除监听器
                        if (installProgressWatcher) {
                            clearInterval(installProgressWatcher);
                            installProgressWatcher = null;
                        }

                        // 恢复按钮状态
                        isUpdating.value = false;
                        console.log("✅ 镜像替换超时，恢复用户接单能力");
                        // 清除保存的更新信息
                        currentUpdateInfo = null;
                        return;
                    }
                } catch (error) {
                    // 出错时也继续检查，直到达到最大次数
                }
            }, 2000); // 每2秒检查一次
        } else {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.ERROR,
                    `WSL操作失败: ${updateResult?.message}`,
                    true,
                ),
            );

            // 操作失败时恢复按钮状态
            isUpdating.value = false;
            console.log("✅ 镜像替换失败，恢复用户接单能力");
            // 清除保存的更新信息
            currentUpdateInfo = null;
        }
    } catch (error) {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.ERROR,
                `执行镜像更新失败: ${error.message}`,
                true,
            ),
        );
        // 出错时恢复按钮状态
        isUpdating.value = false;
        console.log("✅ 镜像替换出错，恢复用户接单能力");
        // 清除保存的更新信息
        currentUpdateInfo = null;
    }
}

async function statisticCheckTrueNum() {
    checkTrueNum.value = 0;

    // 遍历所有检查项
    for (let i = 0; i < reactiveObj.checkList.length; i++) {
        const item = reactiveObj.checkList[i];

        // 对于 mirror 检查项，只要状态是 success 或 hasUpdate 都算通过
        if (item.alias === "mirror") {
            if (item.status === "success" || item.status === "hasUpdate") {
                checkTrueNum.value++;
            }
        } else {
            // 对于其他检查项，使用 checkTrueList 的值
            const key = item.alias + "CheckResult";
            if (checkTrueList[key]) {
                checkTrueNum.value++;
            }
        }
    }

    if (checkTrueNum.value === 6) {
        checkAllTrue.value = true;
    } else {
        checkAllTrue.value = false;
    }
}
async function loadCheckStatus() {
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            "Environment re-check",
            true,
        ),
    );
    checkTrueNum.value = 0;
    resetCheckListStatus();
    checkPlatform();
}
// 环境状态不成功时，相应的安装功能
async function installCheck(item) {
    switch (item.alias) {
        case "wsl":
            if (item.status === "unsatisfied") {
                await API.openWindowsFeaturesDialog();
                return;
            }
            item.progress = true;
            try {
                let installResult = await API.installWSL();
                console.log(installResult, "wsl install result");
                if (installResult["succeed"]) {
                    item.progress = false;
                    checkWSLEnabled();
                } else {
                    // 安装失败时也要重置进度状态
                    item.progress = false;
                }
                /*if (installResult['needRestart']) {
             ElMessage.success('WSL2 install successful, restart computer for effect')
           } else {
             ElMessage.error('WSL2 install failed')
           }*/
            } catch (e) {
                console.log("window.API.installWSL() install WSL2 error");
                // 出现异常时重置进度状态
                item.progress = false;
            }
            break;
        case "virtual":
            // 弹窗 提示
            break;
        case "mirror": {
            item.progress = true;
            item.btnLabel = "下载";
            let mirrorInstallCompleted = false; // 添加标志位防止重复调用checkMirror
            let mirrorInstallWatcher = null; // 镜像安装监听器
            const start = Date.now();
            console.log(
                dayjs(start).format("HH:mm:ss"),
                `Start install task engine[${start}+0]`,
            );
            // 确保当前镜像不处于安装状态
            const curreltMirror = await window.API.checkMirror();
            if (curreltMirror.installing) {
                console.log("mirror installing, skip...");
            }
            // 首次安装时不进行 MD5 校验，传递空字符串
            const { url, hash } = await window.API.getLatestMirrorInfo();
            // url = "http://************/releases/image.tar";
            API.installMirror(url, hash)
                .then((mirror) => {
                    const now = Date.now();
                    console.log(
                        dayjs(now).format("HH:mm:ss"),
                        mirror,
                        `task engine install result, [${now}+${now - start}]`,
                    );
                    item.btnLabel = "Install";
                    // 不在这里立即调用checkMirror，而是启动轮询检测镜像是否真正安装完成
                    console.log(
                        "Mirror download and import command executed, starting installation completion watcher...",
                    );

                    // 启动轮询检测镜像安装状态
                    let checkAttempts = 0;
                    const maxCheckAttempts = 60; // 最多检查60次，每次间隔2秒，总共2分钟

                    mirrorInstallWatcher = setInterval(async () => {
                        checkAttempts++;
                        console.log(
                            `Checking mirror installation status, attempt ${checkAttempts}/${maxCheckAttempts}`,
                        );

                        try {
                            const mirrorStatus = await window.API.checkMirror();
                            console.log(
                                "Mirror status check result:",
                                mirrorStatus,
                            );

                            // 如果镜像已安装，说明WSL导入完成
                            if (
                                mirrorStatus.installed &&
                                !mirrorInstallCompleted
                            ) {
                                mirrorInstallCompleted = true;
                                console.log(
                                    "Mirror installation truly completed! Checking mirror status...",
                                );

                                // 清除监听器
                                if (mirrorInstallWatcher) {
                                    clearInterval(mirrorInstallWatcher);
                                    mirrorInstallWatcher = null;
                                }

                                // 等待一小段时间确保镜像完全就绪，然后检查镜像状态
                                setTimeout(() => {
                                    checkMirror();
                                }, 1500);
                                return;
                            }

                            // 如果超过最大尝试次数，停止检查
                            if (checkAttempts >= maxCheckAttempts) {
                                console.log(
                                    "Max mirror installation check attempts reached, stopping watcher",
                                );
                                if (mirrorInstallWatcher) {
                                    clearInterval(mirrorInstallWatcher);
                                    mirrorInstallWatcher = null;
                                }
                                // 即使超时也尝试检查一次镜像状态
                                checkMirror();
                            }
                        } catch (error) {
                            console.error(
                                "Mirror installation status check error:",
                                error,
                            );
                            // 出错时也继续检查，直到达到最大次数
                        }
                    }, 2000); // 每2秒检查一次
                })
                .catch((e) => {
                    item.progress = false;
                    item.btnLabel = "Install";
                    console.log(
                        "window.API.installMirror() task engine install error",
                    );
                    ElMessage.error(
                        "Task Engine install failed, please contact the administrator",
                    );

                    // 安装失败时清除监听器
                    if (mirrorInstallWatcher) {
                        clearInterval(mirrorInstallWatcher);
                        mirrorInstallWatcher = null;
                    }
                });
            try {
                inMirrorStatInter = setInterval(async () => {
                    let loading = await API.installMirrorStat();
                    currPercent.value =
                        loading[0] < 1.0
                            ? Math.round(loading[0] * 100.0) + "%"
                            : "";
                    if (loading[0] >= 1.0) item.btnLabel = "Install";

                    // 移除原来的安装完成检查逻辑，因为它不准确
                    // 现在使用上面的轮询检测方式

                    if (loading[0] + loading[1] >= 2.0 || !item.progress) {
                        currPercent.value = "...";
                        if (inMirrorStatInter) {
                            clearInterval(inMirrorStatInter);
                        }
                    }
                    console.log(
                        loading,
                        "image install progress [a, b]************",
                    );
                    // 查询进度 [a, b]，a为下载进度，b为安装进度。
                    // 目前安装进度无法监控，所以只返回0.0或1.0
                }, 1000);
            } catch (e) {
                currPercent.value = "...";
                console.log(
                    "window.API.installMirrorStat() get image install progress error",
                );
            }
            break;
        }
    }
}
async function init() {
    try {
        let deviceId = await window.API.getUID();
        if (deviceId) {
            reactiveObj.deviceInfo.id = deviceId;
            reactiveObj.deviceInfo.showId = deviceId.substr(
                deviceId.length - 8,
                8,
            );
        }
        console.log(reactiveObj.deviceIdInfo, "window.API.getUID() device Id");
    } catch (e) {
        console.log("window.API.getUID() get device Id error");
        // 弹出提示框
    }
    try {
        reactiveObj.version = await window.API.getVersion();
    } catch (e) {
        console.log("window.API.getVersion() get version error");
        // 弹出提示框
    }

    // 读取镜像版本
    try {
        const mirrorVersion = await mirrorVersionManager.getMirrorVersion();
        if (mirrorVersion && mirrorVersion.current_version) {
            reactiveObj.mirrorVersion = mirrorVersion.current_version;
            console.log("镜像版本:", mirrorVersion.current_version);
        }
    } catch (e) {
        console.log("获取镜像版本失败:", e);
    }
}
function showCheckList() {
    checkAllTrue.value = false;
}

/**
 * @param {AbortSignal} signal
 * @returns {Promise<void>}
 */
async function registerNode(signal) {
    let mirrorNodeId = "";
    try {
        Logger(
            new Log(
                dayjs(),
                LogSource.SERVER,
                LogLevel.INFO,
                "任务引擎启动中",
                true,
            ),
        );
        signal.addEventListener("abort", () => {
            window.API.abortMirrorNodeID(signal.reason);
        });
        mirrorNodeId = await window.API.mirrorNodeID();
        console.log(mirrorNodeId, "image instance Id");
    } catch (e) {
        if (signal.aborted) {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.SERVER,
                    LogLevel.ERROR,
                    "已终止任务引擎启动",
                    true,
                ),
            );
            return;
        }
        console.log("get image instance Id error", e);
        Logger(
            new Log(
                dayjs(),
                LogSource.SERVER,
                LogLevel.ERROR,
                "任务引擎启动失败",
                true,
            ),
        );
        return;
    }
    if (signal.aborted) return;
    let uid = localStorage.getItem("uId") || "";
    let params = {
        code: mirrorNodeId,
        machine_id: reactiveObj.deviceInfo.id,
        uid: uid,
    };
    const success = await _http.common
        .fetchPost(params, _api.registerNode, _api.baseUrl, undefined, signal)
        .then(async (res) => {
            console.log(res, "node registration result");
            if (signal.aborted) return;
            if (res && res["success"]) {
                isOrdering.value = true;
                // 节点注册成功后，更新按钮状态为接单中
                taskBtnStatus.value = "stop";
                console.log(res, "node registration successful");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.SERVER,
                        LogLevel.INFO,
                        "node registration successful",
                        true,
                    ),
                );
                return true;
            } else {
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.SERVER,
                        LogLevel.ERROR,
                        "node registration failed",
                        true,
                    ),
                );
                ElMessage.error(res.err_message || "node registration failed");
                throw new Error(
                    (res.err_message || "node registration failed") +
                        " reason: 接口返回未成功",
                );
            }
        })
        .catch(async (error) => {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.SERVER,
                    LogLevel.ERROR,
                    "node registration failed",
                    true,
                ),
            );
            console.error("node registration error:", error);
            throw error;
        });
    if (!success || signal.aborted) return false;
    // 通知服务器启用节点
    for (let i = 0; i <= 999; i++) {
        if (signal.aborted) {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.SERVER,
                    LogLevel.ERROR,
                    "信号终止，不再尝试启用节点",
                ),
            );
            return;
        }
        const isSuccessNotified = await _http.common
            .fetchPost(
                {
                    machine_id: reactiveObj.deviceInfo.id,
                },
                _api.enableNode,
                _api.baseUrl,
                undefined,
            )
            .then(
                (res) => {
                    if (res && res["success"]) {
                        Logger(
                            new Log(
                                dayjs(),
                                LogSource.UI,
                                LogLevel.INFO,
                                `通知服务端启用节点成功`,
                            ),
                        );
                        return true;
                    } else {
                        //if (i == 3) {
                        Logger(
                            new Log(
                                dayjs(),
                                LogSource.SERVER,
                                LogLevel.INFO,
                                "通知服务端启用节点未能成功，原因：" +
                                    res?.err_message || JSON.stringify(res),
                            ),
                        );
                        //}
                        return false;
                    }
                },
                (reason) => {
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.UI,
                            LogLevel.ERROR,
                            `通知服务端启用节点失败，原因：` + reason,
                        ),
                    );
                    return false;
                },
            );
        if (isSuccessNotified) return true;

        if (i < 999) {
            await new Promise((resolve) => setTimeout(resolve, 15000));
        }
    }
    return false;
}

/**
 *
 * @param {number} waitMs
 * @returns {Promise<void>}
 */
async function notifyPauseOrder(waitMs) {
    let timer = null;

    await Promise.race([
        new Promise((resolve) => {
            timer = setTimeout(resolve, waitMs);
        }),
        new Promise((resolve) => {
            const params = {
                machine_id: reactiveObj.deviceInfo.id,
            };
            // 通知服务器暂停节点
            _http.common
                .fetchPost(params, _api.disableNode, _api.baseUrl, undefined)
                .then(
                    (res) => {
                        if (res && res["success"]) {
                            Logger(
                                new Log(
                                    dayjs(),
                                    LogSource.UI,
                                    LogLevel.INFO,
                                    `调用服务端禁用节点成功`,
                                ),
                            );
                        } else {
                            Logger(
                                new Log(
                                    dayjs(),
                                    LogSource.SERVER,
                                    LogLevel.ERROR,
                                    "调用服务端禁用节点失败" +
                                        res?.err_message || JSON.stringify(res),
                                ),
                            );
                        }
                        resolve();
                    },
                    (reason) => {
                        Logger(
                            new Log(
                                dayjs(),
                                LogSource.UI,
                                LogLevel.ERROR,
                                `通知服务端禁用节点失败` + reason,
                            ),
                        );
                        resolve();
                    },
                );
        }),
    ]);
    if (timer) clearTimeout(timer);
    isOrdering.value = false;
}
// 监听网络状态
async function checkNetStatus() {
    try {
        let netResult = await window.API.checkNet();
        netStatus.value = netResult === -1 ? false : true;
        console.log(netStatus.value, "network status monitoring result");
    } catch (e) {
        console.log("window.API.checkNet() network status monitoring error", e);
    }
}
async function resetCheckListStatus() {
    reactiveObj.checkList.forEach((item, index) => {
        item.status = "checkout";
        item.progress = false; // 重置进度状态
    });
}
onMounted(() => {
    init();
    // 定时检查环境情况---每小时检查一次
    loadCheckStatus();
    checkEnvInter.value = setInterval(
        () => {
            // loadCheckStatus()
            checkWSLEnabled();
        },
        1000 * 60 * 60,
    );
    // 定时检测网络连接情况
    checkNetStatus();
    netStateInter.value = setInterval(() => {
        checkNetStatus();
    }, 1000 * 5);
    // 自动安装镜像
    const f = () => {
        if (autoInstallDeps()) setTimeout(f, 5000);
    };
    setTimeout(f, 5000);

    // 通知主进程前端监听器已就绪
    console.log("Frontend listeners preparing, notifying main process...");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            `前端监听器准备中，通知主进程处理`,
        ),
    );
    setTimeout(() => {
        window.electron.ipcRenderer.send("frontend-listeners-ready");
        console.log(
            "Frontend listeners ready notification sent to main process",
        );
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[mainPage.onMounted] 前端监听器已发送通知到主进程`,
                false,
            ),
        );
    }, 1000);

    // 添加自动登录和自动接单功能
    console.log("Setting up auto-login and auto-start listeners...");

    // 添加自动接单监听器
    unregisters.push(
        window.IPCListeners.onAutoStartTask(async () => {
            console.log("[onAutoStartTask] Auto-start task message received");

            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `[onAutoStartTask] 渲染进程接收到自动接单事件`,
                    false,
                ),
            );
            // 检查用户是否手动暂停过
            if (userManuallyPaused) {
                console.log(
                    "User has manually paused task acceptance, ignoring auto-start request",
                );

                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        `[onAutoStartTask] 用户已手动暂停接单，无法继续执行自动接单流程`,
                        false,
                    ),
                );
                return;
            }
            // 检查用户的设置
            const userSettings = await window.API.loadUserSettings();
            if (!userSettings.autoOrder) {
                console.log(
                    "🚫 Auto-order acceptance disabled by user, ignoring auto-start request",
                );
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        `[onAutoStartTask] 自动接单被用户禁止，无法继续执行自动接单流程`,
                        false,
                    ),
                );
                return;
            }

            // 避免重复启动接单 - 如果任务按钮已经是停止状态，说明已经在接单了
            if (taskBtnStatus.value === "stop") {
                console.log("Already accepting tasks, skipping auto-start");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        `[onAutoStartTask] 已经处于接单中，跳过后续自动接单流程`,
                        false,
                    ),
                );
                return;
            }

            console.log("Starting intelligent auto-start process from IPC...");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `[onAutoStartTask] 自动接单前置检测已完成，启动 startIntelligentAutoStart 处理自动接单后续流程`,
                    false,
                ),
            );

            // 启动智能环境检查监听器
            startIntelligentAutoStart();
        }),
    );

    unregisters.push(
        window.IPCListeners.onUserSettingsChange("autoOrder", (v) => {
            autoOrderEnabled.value = v;
        }),
    );

    // 加载用户设置状态
    loadSystemSettings();
});
onMounted(() => {
    const listener = async () => {
        const visibility = document.visibilityState === "visible";
        const focused = document.hasFocus();
        isVisibility.value = visibility;
        isFocused.value = focused;
        // Logger(
        //     new Log(
        //         dayjs(),
        //         LogSource.UI,
        //         LogLevel.INFO,
        //         `窗口状态发生变化，是否隐藏: ${!visibility}, 是否失焦: ${!focused}`,
        //     ),
        // );
        if (!visibility || taskBtnStatus.value === "stop") return;
        let mirrorIsRun = await window.API.checkMirror();
        if (!mirrorIsRun) {
            taskBtnStatus.value = "accept";
            return;
        }
        await window.API.checkSelfNodeStatus();
    };

    document.addEventListener("visibilitychange", listener);
    window.addEventListener("focus", listener);
    window.addEventListener("blur", listener);

    unregisters.push(() => {
        document.removeEventListener("visibilitychange", listener);
        window.removeEventListener("focus", listener);
        window.removeEventListener("blur", listener);
    });
});

// 智能自动接单函数 - 监听环境检查状态
let autoStartWatcher = null;
let autoStartAttempts = 0;
const MAX_AUTO_START_ATTEMPTS = 60; // 最多尝试60次，每次间隔5秒，总共5分钟
let userManuallyPaused = false; // 用户手动暂停标志

function startIntelligentAutoStart() {
    // 检查用户是否手动暂停过
    if (userManuallyPaused) {
        console.log(
            "User has manually paused task acceptance, auto-start disabled until app restart",
        );

        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[startIntelligentAutoStart] 用户手动暂停了接单，无法继续后续自动接单流程`,
                false,
                false,
            ),
        );
        return;
    }

    console.log("Starting intelligent auto-start watcher...");
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            `[startIntelligentAutoStart] 开始“智能”自动接单监视器`,
            false,
        ),
    );

    // 如果已经有监听器在运行，先清除
    if (autoStartWatcher) {
        clearInterval(autoStartWatcher);
        autoStartWatcher = null;
    }

    // 重置尝试次数
    autoStartAttempts = 0;

    // 立即检查一次
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            `[startIntelligentAutoStart] 现在由 checkEnvironmentAndAutoStart 负责后续自动接单流程`,
            false,
        ),
    );
    checkEnvironmentAndAutoStart();

    // 每5秒检查一次环境状态
    autoStartWatcher = setInterval(() => {
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[startIntelligentAutoStart] 前面没成功，现在由 checkEnvironmentAndAutoStart 再负责后续自动接单流程`,
                false,
            ),
        );
        checkEnvironmentAndAutoStart();
    }, 5000);
}

async function checkEnvironmentAndAutoStart() {
    // 再次检查用户是否手动暂停过
    if (userManuallyPaused) {
        console.log("User has manually paused, stopping auto-start watcher");

        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 用户手动暂停了接单，无法继续后续自动接单流程`,
                false,
            ),
        );
        if (autoStartWatcher) {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `[checkEnvironmentAndAutoStart] 清理 checkEnvironmentAndAutoStart 相关的定时器`,
                    false,
                ),
            );
            clearInterval(autoStartWatcher);
            autoStartWatcher = null;
        }
        return;
    }

    autoStartAttempts++;

    console.log(
        `Environment check attempt ${autoStartAttempts}/${MAX_AUTO_START_ATTEMPTS}`,
    );
    Logger(
        new Log(
            dayjs(),
            LogSource.UI,
            LogLevel.INFO,
            `[checkEnvironmentAndAutoStart] Environment 检查尝试了 ${autoStartAttempts}/${MAX_AUTO_START_ATTEMPTS}` +
                `\n当前环境状态为: ${checkTrueNum.value}/6 checks passed` +
                `\n当前按钮状态为：${taskBtnStatus.value}` +
                `\n当前网络状态为：${netStatus.value ? "Connected" : "Disconnected"}` +
                `\n是否更新中：${isUpdating.value}`,
            false,
        ),
    );
    console.log(
        `Current environment status: ${checkTrueNum.value}/6 checks passed`,
    );
    console.log(`Task button status: ${taskBtnStatus.value}`);
    console.log(
        `Network status: ${netStatus.value ? "Connected" : "Disconnected"}`,
    );
    console.log(`Is updating: ${isUpdating.value}`);

    // 检查是否正在更新镜像
    if (isUpdating.value) {
        console.log(
            "Mirror is updating, waiting for update to complete before auto-start",
        );
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 镜像更新中，等待更新完成后开始接单`,
                false,
            ),
        );
        return;
    }

    // 检查是否已经在接单
    if (taskBtnStatus.value === "stop") {
        console.log("Already accepting tasks, stopping auto-start watcher");
        if (autoStartWatcher) {
            clearInterval(autoStartWatcher);
            autoStartWatcher = null;
        }
        return;
    }

    // 检查是否超过最大尝试次数
    if (autoStartAttempts >= MAX_AUTO_START_ATTEMPTS) {
        console.log("Max auto-start attempts reached, stopping watcher");

        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 尝试了 ${autoStartAttempts} 次仍然未能成功，超过了最次尝试次数 ${MAX_AUTO_START_ATTEMPTS}, 放弃自动接单`,
                false,
            ),
        );
        if (autoStartWatcher) {
            clearInterval(autoStartWatcher);
            autoStartWatcher = null;
        }
        return;
    }

    // 检查网络状态
    if (!netStatus.value) {
        console.log("Network not connected, waiting...");
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 网络没有连接，等待中`,
                false,
            ),
        );
        return;
    }

    // 检查环境状态
    if (checkTrueNum.value === 6) {
        console.log("All environment checks passed! Starting auto-accept...");
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 所有检查通过，开始切换接单状态，接下来由 taskBtn 负责`,
                false,
            ),
        );
        try {
            // 停止监听器
            if (autoStartWatcher) {
                clearInterval(autoStartWatcher);
                autoStartWatcher = null;
            }

            // 开始接单
            await taskBtn("accept", false);
            console.log("Auto-start task acceptance completed successfully!");
        } catch (error) {
            console.error("Auto-start task acceptance failed:", error);
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.ERROR,
                    `[checkEnvironmentAndAutoStart] 无法启动 taskBtn, 原因: ${error}`,
                    false,
                ),
            );

            // 如果失败，重新启动监听器（但减少剩余尝试次数）
            if (
                autoStartAttempts < MAX_AUTO_START_ATTEMPTS &&
                !userManuallyPaused
            ) {
                console.log("Restarting auto-start watcher after failure...");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.UI,
                        LogLevel.INFO,
                        `[checkEnvironmentAndAutoStart] 重新开始`,
                        false,
                    ),
                );
                setTimeout(() => {
                    if (!autoStartWatcher && !userManuallyPaused) {
                        autoStartWatcher = setInterval(() => {
                            checkEnvironmentAndAutoStart();
                        }, 5000);
                    }
                }, 10000); // 10秒后重新开始
            }
        }
    } else {
        console.log(
            `Environment checks not ready: ${checkTrueNum.value}/6 passed, waiting...`,
        );
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 环境检查未就绪，${checkTrueNum.value}/6 passwd, 等待中...`,
                false,
            ),
        );

        // 详细显示每项检查的状态
        console.log("Detailed environment status:");
        const checkDetails = Object.entries(checkTrueList)
            .map(([key, value]) => {
                return `${key}: ${value ? "PASS" : "FAIL"}`;
            })
            .join(", ");
        console.log(checkDetails);
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `[checkEnvironmentAndAutoStart] 检查每项结果：${checkDetails}`,
                false,
            ),
        );

        // 如果检查次数是5的倍数，重新触发环境检查
        if (autoStartAttempts % 5 === 0) {
            console.log("Triggering environment re-check...");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `[checkEnvironmentAndAutoStart] 当前检查次数是 5 的倍数，开始 loadCheckStatus`,
                    false,
                ),
            );
            loadCheckStatus();
        }
    }
}

// 在组件卸载时清理监听器
onBeforeUnmount(() => {
    if (netStateInter.value) {
        clearInterval(netStateInter.value);
    }
    if (checkEnvInter.value) {
        clearInterval(checkEnvInter.value);
    }
    if (autoStartWatcher) {
        clearInterval(autoStartWatcher);
        autoStartWatcher = null;
    }
    console.log("unregisters", unregisters);
    unregisters.forEach((unregister) => unregister());
});
/**
 * @type {AbortController | undefined}
 */
let aborter = undefined;
watch(
    () => reactiveObj.checkMirrorRunResult,
    async (r) => {
        if (!r?.sourceBtnStatus) {
            Logger(
                new Log(dayjs(), LogSource.UI, LogLevel.INFO, `没有状态，退出`),
            );
            return;
        }
        if (orderToggling.value) {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    `已处于接单状态切换中，退出`,
                ),
            );
            return;
        }
        Logger(
            new Log(
                dayjs(),
                LogSource.UI,
                LogLevel.INFO,
                `开始处理接单状态切换为${r.sourceBtnStatus}的流程`,
            ),
        );
        if (r.sourceBtnStatus === "accept") {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.SERVER,
                    LogLevel.INFO,
                    `准备开始接单`,
                ),
            );
            aborter = new AbortController();
            // 检测到镜像正常运行后，先注册节点
            if (r.runStatus) {
                const signal = aborter.signal;
                try {
                    orderToggling.value = true;
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.INFO,
                            `镜像运行中，开始向服务器注册节点`,
                        ),
                    );
                    await registerNode(signal);
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.INFO,
                            `注册节点完成`,
                        ),
                    );
                } catch (e) {
                    console.error("注册节点发生错误", e);
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.ERROR,
                            `注册节点发生错误` + e,
                        ),
                    );
                    // 如果启动失败，重置任务按钮状态
                    taskBtnStatus.value = "accept";
                } finally {
                    // 用户点击按钮会触发信号终止，如果这时切换状态会导致状态冲突
                    if (!signal.aborted) {
                        orderToggling.value = false;
                    }
                    aborter = undefined;
                }
            } else {
                taskBtnStatus.value = "stop";
                orderToggling.value = true;

                console.log("Image not running, trying to start image...");
                let stage = undefined;
                const signal = aborter.signal;
                try {
                    stage = "run_mirror";
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.INFO,
                            `开始启动镜像`,
                        ),
                    );
                    const success = await runMirror(signal);
                    if (!success) {
                        console.log(
                            "Image start operation completed, result:",
                            success,
                        );
                        throw new Error("未能启动镜像");
                    }
                    stage = "register_node";
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.INFO,
                            `开始向服务器注册节点`,
                        ),
                    );
                    await registerNode(signal);
                } catch (err) {
                    if (signal.aborted) return;
                    if (stage === "run_mirror") {
                        console.error("Image start failed:", err);
                        Logger(
                            new Log(
                                dayjs(),
                                LogSource.SERVER,
                                LogLevel.ERROR,
                                `启动镜像发生错误` + err,
                            ),
                        );
                    } else {
                        Logger(
                            new Log(
                                dayjs(),
                                LogSource.SERVER,
                                LogLevel.ERROR,
                                `注册节点发生错误` + err,
                            ),
                        );
                    }
                    // 如果启动失败，重置任务按钮状态
                    taskBtnStatus.value = "accept";
                } finally {
                    if (!signal.aborted) {
                        orderToggling.value = false;
                    }
                    aborter = undefined;
                }
            }
        } else {
            Logger(
                new Log(
                    dayjs(),
                    LogSource.SERVER,
                    LogLevel.INFO,
                    `准备暂停接单`,
                ),
            );
            orderToggling.value = true;
            if (aborter) {
                aborter.abort("用户暂停接单");
                Logger(
                    new Log(
                        dayjs(),
                        LogSource.SERVER,
                        LogLevel.INFO,
                        "正在终止开始接单流程",
                    ),
                );
                aborter = undefined;
            }
            await notifyPauseOrder(2000);
            if (r.runStatus) {
                try {
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.INFO,
                            "开始停止镜像",
                        ),
                    );
                    await stopMirror();
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.INFO,
                            "停止镜像成功",
                        ),
                    );
                } catch (e) {
                    Logger(
                        new Log(
                            dayjs(),
                            LogSource.SERVER,
                            LogLevel.ERROR,
                            "停止镜像失败" + e,
                        ),
                    );
                } finally {
                    orderToggling.value = false;
                }
                taskBtnStatus.value = "accept";
                reactiveObj.taskList = [];
            } else {
                taskBtnStatus.value = "accept";
                reactiveObj.taskList = [];
            }
        }
    },
    { deep: true },
);
// 当按钮状态发生已发生变化时重置标记
watch(
    () => taskBtnStatus.value,
    (v) => {
        orderToggling.value = false;
    },
);

/**
 * 如果本地没有镜像版本记录，则保存当前版本信息
 */
async function saveMirrorVersionIfNotExists() {
    try {
        console.log("开始检查是否需要保存镜像版本信息...");
        const hasVersion = await mirrorVersionManager.hasVersionRecord();
        console.log("版本文件检查结果:", hasVersion);

        if (!hasVersion) {
            console.log("本地没有镜像版本记录，正在保存当前版本信息...");
            Logger(
                new Log(
                    dayjs(),
                    LogSource.UI,
                    LogLevel.INFO,
                    "本地没有镜像版本记录，正在保存当前版本信息",
                    true,
                ),
            );

            const { url, version } = await window.API.getLatestMirrorInfo();
            const success = await mirrorVersionManager.saveMirrorVersion(
                version,
                url,
                "初始安装版本",
            );
            if (success) {
                console.log("镜像版本信息保存成功，版本:", version);
                // 立即更新界面显示的镜像版本
                reactiveObj.mirrorVersion = version;
                console.log("界面镜像版本已更新为:", version);
            } else {
                console.error("镜像版本信息保存失败");
            }
        }
    } catch (error) {
        console.error("检查或保存镜像版本时出错:", error);
    }
}

// 加载系统设置状态
async function loadSystemSettings() {
    try {
        // 加载用户设置
        const userSettings = await window.API.loadUserSettings();
        if (userSettings) {
            autoOrderEnabled.value = userSettings.autoOrder || false;
            console.log("用户设置加载成功:", userSettings);
        }

        // 加载开机自启动状态
        const autoStartStatus = await window.API.getAutoStartStatus();
        autoStartEnabled.value = autoStartStatus || false;
        console.log("开机自启动状态:", autoStartStatus);
    } catch (error) {
        console.error("加载系统设置失败:", error);
        ElMessage.error("加载系统设置失败");
    }
}

// 处理开机接单开关变化
async function handleAutoOrderChange(value) {
    try {
        if (reactiveObj.disabledAutoOrder) {
            reactiveObj.disabledAutoOrder = false;
            taskBtn("accept");
            return;
        }
        const success = await window.API.saveUserSettings({ autoOrder: value });
        if (success) {
            ElMessage.success(value ? "已启用开机接单" : "已禁用开机接单");
            console.log("开机接单设置已更新:", value);
        } else {
            ElMessage.error("设置失败");
            // 回滚开关状态
            autoOrderEnabled.value = !value;
        }
    } catch (error) {
        console.error("更新开机接单设置失败:", error);
        ElMessage.error("设置失败");
        // 回滚开关状态
        autoOrderEnabled.value = !value;
    }
}

// 处理开机自启动开关变化
async function handleAutoStartChange(value) {
    try {
        const result = await window.API.toggleAutoStart(value);
        if (typeof result === "boolean") {
            // 使用API返回的实际状态
            autoStartEnabled.value = result;
            ElMessage.success(result ? "已启用开机自启动" : "已禁用开机自启动");
            console.log("开机自启动设置已更新:", result);
        } else {
            ElMessage.error("设置失败");
            // 回滚开关状态
            autoStartEnabled.value = !value;
        }
    } catch (error) {
        console.error("更新开机自启动设置失败:", error);
        ElMessage.error("设置失败");
        // 回滚开关状态
        autoStartEnabled.value = !value;
    }
}
</script>
<style lang="scss">
.main-page-wrap {
    height: 100%;
    background: url("@assets/images/line-green.gif") no-repeat bottom;
    position: relative;
    &.static-bg-gif {
        background: url("@assets/images/line-green-static.png") no-repeat bottom;
    }
    .main-page-wrap-default {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .main-page-wrap-content {
            width: 100%;
            margin-top: 30px;
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
            flex: 1;
            .failure-rate {
                font-family: "Arial", sans-serif; /* 使用无衬线字体 */
                padding-left: 10px;
                color: #333; /* 文字颜色 */
                display: flex;
                width: 300px;
                height: 40px;
                border-radius: 4px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                .failure-rate-title {
                    width: 75%; /* 调整左梯形宽度 */
                    height: 38px;
                    line-height: 38px;
                    color: #fff;
                }

                .failure-rate-num {
                    width: 25%; /* 右梯形宽度与左梯形斜边匹配 */
                    height: 38px;
                    clip-path: polygon(
                        30% 0,
                        100% 0,
                        100% 100%,
                        0 100%
                    ); /* 左侧垂直 */
                    line-height: 38px;
                    padding-left: 35px;
                    color: $black;
                }
            }
            .node-sate-success {
                border: 1px $main-color solid;
                border-left: none;
                color: $main-color;
                .failure-rate-num {
                    background-color: $main-color;
                }
            }
            .node-sate-error {
                border: 1px $main-error solid;
                border-left: none;
                color: $main-error;
                .failure-rate-num {
                    background-color: $main-error;
                    color: $black;
                }
            }
            .el-input {
                height: 40px !important;
                border: none !important;
                .el-input__inner {
                    color: $white;
                }
                /* 修改所有 el-input 的 placeholder 颜色 */
                input::placeholder {
                    color: $white; /* 红色 */
                    opacity: 1; /* 确保颜色不透明 */
                }

                /* 兼容不同浏览器 */
                input::-webkit-input-placeholder {
                    color: $white;
                }
                input::-moz-placeholder {
                    color: $white;
                }
                input:-ms-input-placeholder {
                    color: $white;
                }
                .el-input-group__append,
                .el-input-group__prepend {
                    padding: 0 !important;
                    width: 60px !important;
                    text-align: center;
                    background: none !important;
                    box-shadow: none !important;
                    color: $black !important;
                    background: $main-color !important;
                }
                .el-input__wrapper {
                    width: calc(100% - 50px) !important;
                    background: none !important;
                    box-shadow: none !important;
                    border: 1px $main-color solid !important;
                }
            }
            .task-btn {
                width: 153px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                color: $black;
                border-radius: 4px;
                margin: auto;
                cursor: pointer;
                font-weight: bold;
            }
            .task-btn-not-allow {
                width: 153px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                color: $black;
                font-weight: bold;
                background: #44484c;
                border-radius: 4px;
                margin: auto;
            }
            .main-page-wrap-content-task {
                // width: calc(100% - 34px);
                height: calc(100% - 69px);
                margin: 0 17px;
                display: flex;
                flex-direction: column;
                font-size: 12px;
            }
            .accept {
                background: $main-color;
                color: $black;
            }
            .stop {
                background: $main-error;
                color: $white;
            }
            .client-version {
                margin: 0 auto;
            }
        }
        .main-page-bottom {
            display: flex;
            margin: 0 17px 17px;
            font-size: 14px;
            color: $white;
            font-family: "Regular";
            flex-direction: row;
            align-items: center;
            span:nth-child(1) {
                display: flex;
                white-space: nowrap; /* 禁止换行 */
                overflow: hidden; /* 隐藏超出部分 */
                text-overflow: ellipsis; /* 超出显示省略号(可选) */
            }
            .version-info {
                color: #9a9a9a;
                margin-left: auto;
                display: flex;
                font-size: 12px;

                span {
                    line-height: 1.4;
                }
            }
        }
    }
    .success-bg {
        background: $main-color;
    }
    .error-bg {
        background: $main-error;
    }
    .error-color {
        color: $main-error !important;
    }
    .gray-color {
        color: #368992 !important;
    }
    .main-page-wrap-check {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        position: absolute;
        top: 0;
        left: 0;
        .main-page-wrap-check-list {
            margin: 70px auto 87px;
            .main-page-wrap-check-list-item {
                width: calc(100% - 17px * 2);
                font-size: 16px;
                line-height: 40px;
                padding: 0 17px;
                display: flex;
                .main-page-wrap-check-list-img {
                    width: 16px;
                }
                .check-item-status {
                    width: 12px;
                    height: 12px;
                    margin-top: 14px;
                    border-radius: 6px;
                    text-align: center;
                    line-height: 10px;
                    font-size: 10px;
                }
                .check-item-status-loading {
                    width: 16px;
                    height: 16px;
                    margin-top: 14px;
                }
                .loading-img-size {
                }

                .check-item-label {
                    margin-left: 15px;
                    color: $white;
                }
                .check-item-btn {
                    margin-left: auto;
                    color: $main-color;
                    .check-item-btn-label {
                        cursor: pointer;
                    }
                }
                .item-progress {
                    color: $main-color;
                }
            }
        }
        .main-page-wrap-check-reload {
            margin-top: 17px;
            text-align: center;
            color: $main-color;
            font-size: 14px;
            cursor: pointer;
        }
    }
    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .animate-spin {
        animation: spin 2s linear infinite;
        transform-origin: center; /* 添加这行，确保从中心点旋转 */
        display: inline-block; /* 添加这行，确保图标正确渲染 */
    }

    // 系统设置开关样式
    .system-settings {
        display: flex;
        align-items: center;
        margin: 15px 0 15px 10px;
        width: 280px;
        justify-content: space-between;
        .setting-item {
            display: flex;
            align-items: center;
            span {
                font-size: 12px;
                margin-left: 8px;
                margin-right: 4px;
            }
            .el-icon {
                opacity: 0.6;
            }
        }
    }
}

.el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
    padding: 6px 12px;
    background-color: #3e77a4;
    margin-left: -5px;
    width: 200px;
}
.el-popper.is-customized .el-popper__arrow::before {
    background-color: #3e77a4;
    left: 5px;
}
// Element Plus switch 全局样式覆盖
:deep(.el-switch) {
    --el-switch-on-color: rgb(119, 224, 162) !important; // 项目主色调
    --el-switch-off-color: #505050 !important; // 关闭状态的颜色
    --el-switch-border-color: #666666 !important; // 边框颜色
}

:deep(.el-switch.is-checked) {
    --el-switch-on-color: rgb(119, 224, 162) !important;
}

:deep(.el-switch__core) {
    background-color: var(--el-switch-off-color) !important;
    border-color: var(--el-switch-border-color) !important;
}

:deep(.el-switch.is-checked .el-switch__core) {
    background-color: rgb(119, 224, 162) !important;
    border-color: rgb(119, 224, 162) !important;
}

/* 更强的选择器来覆盖Element Plus默认样式 */
.system-settings :deep(.el-switch.is-checked .el-switch__core) {
    background-color: rgb(119, 224, 162) !important;
    border-color: rgb(119, 224, 162) !important;
}

.system-settings :deep(.el-switch__core) {
    background-color: #505050 !important;
    border-color: #666666 !important;
    transition: all 0.3s ease !important;
}

.system-settings :deep(.el-switch.is-checked) {
    border-color: rgb(119, 224, 162) !important;
}

.system-settings :deep(.el-switch.is-checked .el-switch__core) {
    background-color: rgb(119, 224, 162) !important;
    border-color: rgb(119, 224, 162) !important;
}

/* 确保开关按钮本身也使用正确的颜色 */
.system-settings :deep(.el-switch.is-checked::after) {
    border-color: rgb(119, 224, 162) !important;
}

.system-settings :deep(.el-switch__action) {
    background-color: #fff !important;
}

/* 自定义开关样式 - 专门针对项目主色调 */
.custom-switch.el-switch {
    --el-switch-on-color: rgb(119, 224, 162) !important;
    --el-switch-off-color: #505050 !important;
}

.custom-switch.el-switch.is-checked .el-switch__core {
    background-color: rgb(119, 224, 162) !important;
    border-color: rgb(119, 224, 162) !important;
}

.custom-switch.el-switch .el-switch__core {
    background-color: #505050 !important;
    border-color: #666666 !important;
}

/* 全局覆盖 - 最强优先级 */
.el-switch.custom-switch.is-checked .el-switch__core {
    background-color: rgb(119, 224, 162) !important;
}

.el-switch.custom-switch .el-switch__core {
    background-color: #505050 !important;
}

/* 最终强制覆盖 - 确保开关颜色正确 */
body .el-switch.custom-switch.is-checked .el-switch__core,
.main-page-wrap .el-switch.custom-switch.is-checked .el-switch__core,
.system-settings .el-switch.custom-switch.is-checked .el-switch__core {
    background-color: rgb(119, 224, 162) !important;
    border-color: rgb(119, 224, 162) !important;
}

body .el-switch.custom-switch .el-switch__core,
.main-page-wrap .el-switch.custom-switch .el-switch__core,
.system-settings .el-switch.custom-switch .el-switch__core {
    background-color: #505050 !important;
    border-color: #666666 !important;
    transition: all 0.3s ease !important;
}
</style>
