<template>
    <div class="login-wrap">
        <div v-if="autoLogging" class="auto-logging-wrap">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-loader-circle-icon lucide-loader-circle"
            >
                <path d="M21 12a9 9 0 1 1-6.219-8.56" />
            </svg>
        </div>
        <img class="login-logo" src="@assets/images/logo.png" />
        <div class="login-content">
            <div
                v-if="!route.query.flag || route.query.flag === 'loginForm'"
                class="login-wrap-form"
            >
                <div
                    class="login-wrap-form-title flex h-[30px] mb-[20px] mt-[20px]"
                >
                    <el-tabs
                        v-model="currentTab"
                        class="demo-tabs"
                        @tab-click="tabClick"
                    >
                        <el-tab-pane
                            label="密码登录"
                            name="password"
                        ></el-tab-pane>
                        <el-tab-pane
                            label="验证码登录"
                            name="code"
                        ></el-tab-pane>
                    </el-tabs>
                </div>
                <el-form
                    ref="formsRef"
                    :model="forms"
                    :rules="rules"
                    label-width="0px"
                >
                    <div>
                        <el-form-item prop="phone">
                            <el-input
                                v-model.trim="forms.phone"
                                class="h-[40px]"
                                clearable
                                placeholder="请输入手机号码"
                            >
                                <template #prepend>+86</template>
                            </el-input>
                        </el-form-item>
                    </div>
                    <div v-if="currentTab === 'password'">
                        <el-form-item prop="password">
                            <el-input
                                show-password
                                @keyup.enter="login"
                                v-model.trim="forms.password"
                                type="password"
                                class="h-[40px]"
                                clearable
                                placeholder="请输入密码"
                            >
                                <template #prepend>密码</template>
                            </el-input>
                        </el-form-item>
                    </div>
                    <div v-if="currentTab === 'code'">
                        <el-form-item>
                            <div class="login-code-input-code">
                                <span class="login-code-input">
                                    <el-input
                                        v-model.trim="forms.code"
                                        class="h-[40px]"
                                        clearable
                                        placeholder="请输入验证码"
                                    >
                                        <template #prepend>验证码</template>
                                        <template #append>
                                            <span
                                                v-if="!isSendCode && !sending"
                                                class="login-code-btn"
                                                @click="sendCode"
                                                >发送验证码</span
                                            >
                                            <span
                                                v-if="!isSendCode && sending"
                                                class="login-code-btn"
                                                style="cursor: not-allowed"
                                                >发送验证码</span
                                            >
                                            <span
                                                v-if="isSendCode && !sending"
                                                class="login-code-btn"
                                                style="cursor: not-allowed"
                                                >{{ countDown }}s</span
                                            >
                                        </template>
                                    </el-input>
                                </span>
                            </div>
                        </el-form-item>
                    </div>
                    <div class="login-forget-pwd" @click="forgetPwd">
                        忘记密码?
                    </div>

                    <!-- 添加记住登录复选框 -->
                    <div class="login-remember">
                        <el-checkbox
                            v-model="forms.rememberLogin"
                            label="记住登录状态"
                            size="small"
                        />
                    </div>
                </el-form>
                <x-button
                    type="confirm"
                    style="width: 100%"
                    @click="login"
                    :loading="loginLoading"
                    >登录</x-button
                >
                <div class="login-register-btn">
                    <label @click="goRegister()">去注册></label>
                </div>
            </div>
            <div
                v-if="route.query.flag === 'updatePwdForm'"
                class="login-wrap-form w-[320px] h-[420px] bg-white border-rounded-lg p-[25px] box-border"
            >
                <!-- <div class="flex flex-justify-between items-center mb-[20px]">
          <span class="text-size-24px color-black font-700">修改密码</span>
          <span class="text-size-14px color-#2181ff cursor-pointer"
                @click="$router.push({ name: 'register' })">去注册></span>
        </div>-->
                <el-form
                    ref="formsRef"
                    :model="forms"
                    :rules="rules"
                    label-width="0px"
                >
                    <el-form-item prop="phone">
                        <el-input
                            v-model.trim="forms.phone"
                            class="h-[40px]"
                            clearable
                            placeholder="请输入手机号码"
                        >
                            <template #prepend>+86</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="code">
                        <div class="login-code-input-code">
                            <span class="login-code-input">
                                <el-input
                                    v-model.trim="forms.code"
                                    class="h-[40px]"
                                    @keyup.enter="login"
                                    clearable
                                    placeholder="请输入验证码"
                                >
                                    <template #prepend>验证码</template>
                                    <template #append>
                                        <span
                                            v-if="!isSendCode && !sending"
                                            class="login-code-btn"
                                            @click="sendCode"
                                            >发送验证码</span
                                        >
                                        <span
                                            v-if="!isSendCode && sending"
                                            class="login-code-btn"
                                            style="cursor: not-allowed"
                                            >发送验证码</span
                                        >
                                        <span
                                            v-if="isSendCode && !sending"
                                            class="login-code-btn"
                                            style="cursor: not-allowed"
                                            >{{ countDown }}s</span
                                        >
                                    </template>
                                </el-input>
                            </span>
                        </div>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input
                            show-password
                            v-model.trim="forms.password"
                            class="h-[40px]"
                            clearable
                            placeholder="请输入密码"
                        >
                            <template #prepend>密码</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="confirmPassword">
                        <el-input
                            show-password
                            v-model.trim="forms.confirmPassword"
                            @keyup.enter="updatePwd"
                            class="h-[40px]"
                            clearable
                            placeholder="请再次输入密码"
                        >
                            <template #prepend>确认密码</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="agree">
                        <div class="form-tirm">
                            <el-checkbox
                                v-model="forms.agree"
                                label="我已阅读并同意《xx用户协议》"
                                size="small"
                            />
                        </div>
                    </el-form-item>
                </el-form>
                <x-button
                    type="confirm"
                    style="width: 100%"
                    @click="updatePwd"
                    :loading="loginLoading"
                    >确定</x-button
                >
                <div class="login-register-btn">
                    <label @click="goRegister()">去注册></label>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { app } from "electron";
import xButton from "@/components/x-button.vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { reactive, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { _api, _http } from "./../mixins/service";
import { removeCookie } from "../global/cookie";
import { LogSource, LogLevel } from "@/types/enums";
import Log from "@/types/Log";
import Logger from "@/mixins/logger";
import dayjs from "dayjs";
const $router = useRouter();
const route = useRoute();
let loginLoading = ref(false);
const autoLogging = ref(true);

interface userForm {
    phone: string;
    password: string;
    confirmPassword: string;
    code: string;
    agree: boolean;
    rememberLogin: boolean;
}
let codeImg = ref("");
const formsRef = ref<FormInstance>();
let forms = reactive<userForm>({
    phone: "",
    password: "",
    confirmPassword: "",
    code: "",
    agree: false,
    rememberLogin: true,
});
const validateMobile = (rule, value, callback) => {
    if (value === "") {
        callback(new Error("请输入手机号"));
    } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error("请输入正确的11位手机号"));
    } else {
        callback();
    }
};

const validatePass = (rule, value, callback) => {
    if (value === "") {
        callback(new Error("请输入密码"));
    } else {
        callback();
    }
};

const validatePass2 = (rule, value, callback) => {
    if (value === "") {
        callback(new Error("请再次输入密码"));
    } else if (value !== forms.password) {
        callback(new Error("两次输入密码不一致!"));
    } else {
        callback();
    }
};
const rules = reactive<FormRules<userForm>>({
    phone: [{ validator: validateMobile, trigger: "blur" }],
    password: [
        { required: true, message: "请输入密码", trigger: "blur" },
        { validator: validatePass, trigger: "blur" },
    ],
    confirmPassword: [
        { required: true, message: "请再次输入密码", trigger: "blur" },
        { validator: validatePass2, trigger: "blur" },
    ],
    code: [{ required: true, message: "请输入验证码", trigger: "blur" }],
    agree: [{ required: true, message: "请同意用户协议", trigger: "change" }],
});
let currentTab = ref("password");
// 是否发送验证码
let isSendCode = ref(false);
// 点击发送验证码后，发送成功前的状态
let sending = ref(false);
// 倒计时
let countDown = ref(59);
let countDownInter = ref(null);

function tabClick(alias) {
    resetForm();
    initCountDown();
    currentTab.value = alias;
}
function forgetPwd() {
    resetForm();
    initCountDown();
    $router.push({ name: "login", query: { flag: "updatePwdForm" } });
}
function resetForm() {
    forms.phone = "";
    forms.password = "";
    forms.code = "";
    forms.agree = false;
    forms.confirmPassword = "";
}
removeCookie("user");
// 发送验证码方法
async function sendCode() {
    formsRef.value.validateField("phone", (valid: boolean) => {
        if (valid) {
            sending.value = true;
            _http.common
                .fetchPost(forms, _api.sendCode, _api.baseUrl)
                .then((res) => {
                    ElMessage.success("验证码发送成功");
                    // 开始倒计时
                    isSendCode.value = true;
                    sending.value = false;
                    countDownInter.value = setInterval(() => {
                        if (countDown.value === 1) {
                            if (countDownInter.value) {
                                clearInterval(countDownInter.value);
                                countDownInter.value = null;
                            }
                            isSendCode.value = false;
                            countDown.value = 59;
                        } else {
                            countDown.value = countDown.value - 1;
                        }
                    }, 1000);
                })
                .catch((error) => {
                    console.error("验证码发送错误:", error);
                });
        }
    });
}
// 登录按钮
function login() {
    let myForm = {
        password: forms.password,
        phone: forms.phone,
        code: currentTab.value === "code" ? forms.code : "",
        login_model: currentTab.value === "password" ? 0 : 1, // 0-密码登录 1-验证码登录
        ts: Date.now(),
    };

    loginLoading.value = true;
    formsRef.value?.validate(async (valid: boolean, fileds) => {
        if (valid) {
            Logger(
                new Log(
                    dayjs().format(),
                    LogSource.UI,
                    LogLevel.INFO,
                    "用户登录",
                    true,
                ),
            );
            _http.common
                .fetchPost(myForm, _api.login, _api.baseUrl)
                .then(async (res) => {
                    loginLoading.value = false;
                    if (res && res["success"]) {
                        await initSuccess(res);
                        Logger(
                            new Log(
                                dayjs().format(),
                                LogSource.UI,
                                LogLevel.INFO,
                                "登录成功",
                                true, // 是否需要再客户端UI显示
                            ),
                        );
                    } else {
                        ElMessage.error(res.err_message || "登录失败");
                        Logger(
                            new Log(
                                dayjs().format(),
                                LogSource.UI,
                                LogLevel.ERROR,
                                "登录失败",
                                true,
                            ),
                        );
                    }
                    console.log(_api.logList, "日志列表");
                })
                .catch(async (error) => {
                    console.error("登录错误:", error);
                    loginLoading.value = false;
                    Logger(
                        new Log(
                            dayjs().format(),
                            LogSource.UI,
                            LogLevel.ERROR,
                            "登录错误",
                            true,
                        ),
                    );
                });
        } else {
            loginLoading.value = false;
        }
    });
}
async function initSuccess(res: any) {
    localStorage.setItem("authToken", res.data);

    // 如果用户选择了记住登录，保存凭据
    if (forms.rememberLogin) {
        try {
            const credentialsData = {
                phone: forms.phone,
                token: res.data,
                autoLogin: true,
            };
            await window.API.saveUserCredentials(credentialsData);
            console.log("用户凭据已保存");
        } catch (error) {
            console.error("保存用户凭据失败:", error);
        }
    }

    ElMessage.success("登录成功");
    // 返回来时页面或首页
    if (route.query.back && typeof route.query.back === "string") {
        $router.push(route.query.back);
    } else {
        $router.push({ name: "main-page" });
    }
}
// 忘记密码
function updatePwd() {
    let myForm = {
        phone: forms.phone,
        code: route.query.flag === "updatePwdForm" ? forms.code : "",
        password: forms.password,
        ts: Date.now(),
    };

    loginLoading.value = true;
    formsRef.value?.validate(async (valid: boolean, fileds) => {
        if (valid) {
            Logger(
                new Log(
                    dayjs().format(),
                    LogSource.UI,
                    LogLevel.INFO,
                    "用户登录",
                    true,
                ),
            );
            _http.common
                .fetchPost(myForm, _api.resetPwd, _api.baseUrl)
                .then(async (res) => {
                    loginLoading.value = false;
                    if (res && res["success"]) {
                        ElMessage.success("密码修改成功");
                        // 跳转到登录页
                        $router.push({
                            name: "login",
                            query: { flag: "loginForm" },
                        });
                        Logger(
                            new Log(
                                dayjs().format(),
                                LogSource.UI,
                                LogLevel.INFO,
                                "密码修改成功",
                                true, // 是否需要再客户端UI显示
                            ),
                        );
                    } else {
                        ElMessage.error(res.err_message || "登录失败");
                        Logger(
                            new Log(
                                dayjs().format(),
                                LogSource.UI,
                                LogLevel.ERROR,
                                "密码修改失败",
                                true,
                            ),
                        );
                    }
                })
                .catch(async (error) => {
                    console.error("密码修改错误:", error);
                    loginLoading.value = false;
                    Logger(
                        new Log(
                            dayjs().format(),
                            LogSource.UI,
                            LogLevel.ERROR,
                            "密码修改错误",
                            true,
                        ),
                    );
                });
        } else {
            loginLoading.value = false;
        }
    });
}
// 初始化验证码倒计时
function initCountDown() {
    // 是否发送验证码
    isSendCode.value = false;
    // 倒计时
    countDown.value = 59;
    countDownInter.value = null;
}
function goRegister() {
    $router.push({ name: "register" });
}
// 加载已保存的用户凭据
async function loadSavedCredentials() {
    try {
        autoLogging.value = true;
        const credentials = await window.API.loadUserCredentials();
        if (credentials && credentials.autoLogin) {
            forms.phone = credentials.phone;
            forms.rememberLogin = true;

            // 如果有有效的token，直接跳转到主页面
            if (credentials.token) {
                // 借助 checkNet 函数来验证 Token 是否失效
                const result = await window.API.checkSelfNodeStatus();
                localStorage.setItem("authToken", credentials.token);
                console.log("检测到有效的登录凭据，自动登录中...", result);
                ElMessage.success("自动登录成功");
                $router.push({ name: "main-page" });
                return true;
            }
        }
    } catch (error) {
        console.error("加载用户凭据失败:", error);
    } finally {
        autoLogging.value = false;
    }
    return false;
}
async function getClientVersion() {
    try {
        let version = await window.API.getVersion();
        localStorage.setItem("clientVersion", version);
    } catch (e) {
        console.log("window.API.getVersion() 获取版本报错");
        // 弹出提示框
    }
}
onMounted(async () => {
    autoLogging.value = true;
    getClientVersion();
    // 加载已保存的凭据
    await loadSavedCredentials();
});
</script>
<style lang="scss">
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.login-wrap {
    .auto-logging-wrap {
        position: fixed;
        display: flex;
        width: 100vw;
        height: 100vh;
        left: 0;
        background: #0a2133;
        align-items: center;
        justify-content: center;
        z-index: 9;
        svg {
            width: 32px;
            height: 32px;
            animation: rotate 1s infinite linear;
        }
    }
    .login-logo {
        margin: 38px 0 50px 31px;
    }

    .login-content {
        width: calc(100% - 62px);
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .login-forget-pwd {
            float: right;
            color: $input-border-color;
            margin-bottom: 17px;
            font-size: 12px;
            cursor: pointer;
        }
        .login-remember {
            margin-bottom: 17px;
            color: $white;
            font-size: 12px;
            .el-checkbox__label {
                color: $white !important;
            }
        }
        .login-register-btn {
            margin-top: 17px;
            text-align: center;
            color: $main-color;
            font-size: 14px;
            label {
                cursor: pointer;
            }
        }
    }

    .el-form-item {
        margin-bottom: 23px !important;
    }

    .login-wrap-form {
        width: 100%;
        .login-wrap-form-title {
            .el-tabs__nav-wrap:after {
                display: none !important;
            }
            .el-tabs__item {
                color: $white;
            }
            .el-tabs__item.is-active,
            .el-tabs__item:hover {
                color: $main-color;
            }
            .el-tabs__active-bar {
                background-color: $main-color;
            }
        }
        .form-tirm {
            color: $white;
            .el-checkbox__label {
                color: $white !important;
            }
        }
        .login-code-input-code {
            display: flex;
            flex-direction: row;
            .login-code-input {
                width: 100%;
                display: flex;
            }
        }

        .login-code-btn {
            width: 100px;
            text-align: center;
            height: 36px;
            line-height: 36px;
            background: $input-border-color;
            cursor: pointer;
            color: $white;
        }
        .el-input-group__append {
            padding: 0 !important;
            background: none !important;
            border: none !important;
            box-shadow: none !important;
            margin-right: 2px;
        }
        .el-input-group__prepend {
            padding: 0 !important;
            width: 60px !important;
            text-align: center;
            background: none !important;
            box-shadow: none !important;
            color: $white;
        }
        .el-input {
            border: 1px $input-border-color solid !important;
            .el-input__inner {
                color: $white;
                height: 40px !important;
            }
            /* 修改所有 el-input 的 placeholder 颜色 */
            input::placeholder {
                color: $input-border-color; /* 红色 */
                opacity: 1; /* 确保颜色不透明 */
            }

            /* 兼容不同浏览器 */
            input::-webkit-input-placeholder {
                color: $input-border-color;
            }
            input::-moz-placeholder {
                color: $input-border-color;
            }
            input:-ms-input-placeholder {
                color: $input-border-color;
            }
        }
        .el-input__wrapper {
            width: calc(100% - 160px) !important;
            background: none !important;
            box-shadow: none !important;
        }
    }
}
</style>
