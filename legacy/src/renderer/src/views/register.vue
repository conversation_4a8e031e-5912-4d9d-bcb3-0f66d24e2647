<template>
  <div class="register-wrap">
    <div class="register-wrap-form">
      <div class="register-wrap-form-title">
        <span>注册账号</span>
      </div>
      <el-form ref="formsRef" :model="forms" :rules="rules" label-width="0px">
        <el-form-item prop="phone">
          <el-input v-model.trim="forms.phone" clearable placeholder="请输入手机号码">
            <template #prepend>+86</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code">
          <div class="login-code-input-code">
            <span class="login-code-input">
              <el-input v-model.trim="forms.code" clearable placeholder="请输入验证码">
                <template #prepend>验证码</template>
                <template #append>
                  <span v-if="!isSendCode && !sending" class="login-code-btn" @click="sendCode">发送验证码</span>
                  <span v-if="!isSendCode && sending" class="login-code-btn" style="cursor: not-allowed">发送验证码</span>
                  <span v-if="isSendCode && !sending" class="login-code-btn" style="cursor: not-allowed">{{ countDown }}s</span>
                </template>
              </el-input>
            </span>
          </div>
        </el-form-item>
        <el-form-item prop="password">
          <el-input show-password type="password" v-model.trim="forms.password" class="h-[40px]" clearable placeholder="请输入密码">
            <template #prepend>密码</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="confirmPassword">
          <el-input show-password @keyup.enter="register" type="password" v-model.trim="forms.confirmPassword" class="h-[40px]" clearable placeholder="请输入密码">
            <template #prepend>确认密码</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="forms.invitation_code" class="h-[40px]" clearable placeholder="选填">
            <template #prepend>邀请码</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="agree">
          <div class="form-tirm">
            <el-checkbox v-model="forms.agree" label="我已阅读并同意《xx用户协议》" size="small" />
          </div>
        </el-form-item>
      </el-form>
      <x-button type="confirm" style="width: 100%" @click="register">注册</x-button>
      <div class="register-login-btn">
        <label @click="goLogin()">去登录></label></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { app } from 'electron'
  import xButton from '@/components/x-button.vue'
  import {ElMessage, ElMessageBox, FormInstance, FormRules} from 'element-plus'
  import { reactive, ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { _api, _http } from './../mixins/service'
  import { removeCookie } from '../global/cookie'

  const $router = useRouter()
  const route = useRoute()
  let loginLoading = ref(false)
  let codeImg = ref('')

  interface userForm {
    phone: string
    password: string
    confirmPassword: string
    code: string
  }
  const formsRef = ref<FormInstance>()
  let forms = reactive<userForm>({
    phone: '',
    password: '',
    confirmPassword: '',
    code: '',
    invitation_code: ''
  })
  const validateMobile = (rule, value, callback) => {
    if (value === '') {
      callback(new Error('请输入手机号'));
    } else if (!/^1[3-9]\d{9}$/.test(value)) {
      callback(new Error('请输入正确的11位手机号'));
    } else {
      callback();
    }
  }
  const validatePass = (rule, value, callback) => {
    if (value === '') {
      callback(new Error('请输入密码'));
    } else {
      callback();
    }
  };

  const validatePass2 = (rule, value, callback) => {
    if (value === '') {
      callback(new Error('请再次输入密码'));
    } else if (value !== forms.password) {
      callback(new Error('两次输入密码不一致!'));
    } else {
      callback();
    }
  };
  const rules = reactive<FormRules<userForm>>({
    phone: [{ validator: validateMobile, trigger: 'blur' }],
    password: [{ validator: validatePass, trigger: 'blur' }],
    confirmPassword: [{ validator: validatePass2, trigger: 'blur' }],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
    agree: [{ required: true, message: '请同意用户协议', trigger: 'change' }]
  })

  // 添加验证码相关的响应式变量
  let isSendCode = ref(false)
  // 点击发送验证码后，发送成功前的状态
  let sending = ref(false)
  let countDown = ref(59)
  let countDownInter = ref<any>(null)

  // 发送验证码方法
  async function sendCode() {
    formsRef.value.validateField('phone', (valid: boolean) => {
      if (valid) {
        sending.value = true
        _http.common
          .fetchPost(forms, _api.sendCode, _api.baseUrl)
          .then((res) => {
            ElMessage.success('验证码发送成功')
            // 开始倒计时
            isSendCode.value = true
            sending.value = false
            countDownInter.value = setInterval(() => {
              if (countDown.value === 1) {
                if (countDownInter.value) {
                  clearInterval(countDownInter.value)
                  countDownInter.value = null
                }
                isSendCode.value = false
                countDown.value = 59
              } else {
                countDown.value = countDown.value - 1
              }
            }, 1000)
          })
          .catch((error) => {
            sending.value = false
            console.error('验证码发送错误:', error)
          })
      }
    })
  }
  // 初始化验证码倒计时
  function initCountDown() {
    // 是否发送验证码
    isSendCode.value = false
    // 倒计时
    countDown.value = 59
    countDownInter.value = null
  }
  async function register() {
    formsRef.value.validate((valid: boolean, fileds) => {
      if (valid) {
        loginLoading.value = true
        _http.common
          .fetchPost(forms, _api.register, _api.baseUrl)
          .then((res) => {
            loginLoading.value = false
            if (res && res['success']) {
              ElMessage.success('注册成功')
              // 跳转到登录页面
              $router.push({ name: 'login' })
            } else {
              ElMessage.error(res.err_message || '注册失败')
            }
          })
          .catch((error) => {
            console.error('登录错误:', error)
          })
      }
    })
  }
  function goLogin () {
    $router.push({ name: 'login' })
  }
</script>

<style lang="scss">
.register-wrap {
  width: calc(100% - 62px);
  margin: 0 auto;
  user-select: none; /* 标准语法 */
  -webkit-user-select: none; /* Safari/Chrome */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
  .register-wrap-form {
    .register-wrap-form-title{
      margin: 50px 0 15px;
      display: flex;
      font-size: 20px;
    }
    .form-tirm{
      color: $white;
      .el-checkbox__label{
        color: $white !important;
      }
    }
    .el-input-group__prepend {
      width: 60px !important;
      text-align: center;
      background: none !important;
    }
    
    .el-input__wrapper {
      box-shadow: 0px 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset !important;
    }

    login-code-input-code{
      display: flex;
      flex-direction: row;
      .login-code-input {
        width: 100%;
        display: flex;
      }
    }

    .login-code-btn {
      width: 100px;
      text-align: center;
      height: 36px;
      line-height: 36px;
      background: $input-border-color;
      cursor: pointer;
      color: $white;
    }

    .el-input-group__append{
      padding: 0 !important;
      background: none !important;
      border: none !important;
      box-shadow: none !important;
      margin-right: 2px;
    }
    .el-input-group__prepend {
      padding: 0 !important;
      width: 60px !important;
      text-align: center;
      background: none !important;
      box-shadow: none !important;
      color: $white;
    }
    .el-input{
      border: 1px $input-border-color solid !important;
      .el-input__inner{
        color: $white;
        height: 40px !important;
      }
      /* 修改所有 el-input 的 placeholder 颜色 */
      input::placeholder {
        color: $input-border-color; /* 红色 */
        opacity: 1; /* 确保颜色不透明 */
      }

      /* 兼容不同浏览器 */
      input::-webkit-input-placeholder {
        color: $input-border-color;
      }
      input::-moz-placeholder {
        color: $input-border-color;
      }
      input:-ms-input-placeholder {
        color: $input-border-color;
      }
    }
    .el-input__wrapper {
      width: calc(100% - 160px) !important;
      background: none !important;
      box-shadow: none !important;
    }
    .register-login-btn{
      margin-top: 17px;
      text-align: center;
      color: $main-color;
      font-size: 14px;
      label{
        cursor: pointer;
      }
    }
  }
}
</style>
