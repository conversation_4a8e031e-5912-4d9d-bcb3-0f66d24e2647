import {type UpdateInfo, type ProgressInfo} from "electron-updater";
import {provide, inject} from "vue";
import {ElMessage} from "element-plus";

type Metadata = {
    timer: number | undefined,
    checking: boolean,
}
type State = {
    updateInfo: UpdateInfo | undefined;
    error: Error | undefined;
    progress: ProgressInfo | undefined;
    downloaded: boolean;
    disabled: boolean
    confirming: boolean
}
type CheckUpdater = {
    state: State,
    check: (force?: boolean) => Promise<boolean>,
    disable: () => void,
    resume: () => void,
}
const KEY = Symbol('checkUpdate') as InjectionKey<CheckUpdater>;

export const useCheckUpdateProvider = () => {

    const state = reactive<State>({
        updateInfo: undefined,
        error: undefined,
        progress: undefined,
        downloaded: false,
        confirming: false,
        disabled: false
    })
    const postponed = ref<number>(0);
    const metadata: Metadata = {
        timer: undefined,
        checking: false,
    }
    const onProgress = (progressObj: ProgressInfo) => {
        console.log('onDownloadProgress',progressObj);
        state.progress = progressObj;
    }
    const onDownloaded = () => {
        console.log('Update downloaded');
        state.downloaded = true;
        state.progress = undefined;
    }
    const onError = (err: Error) => {
        console.log('Update error', err);
        state.error = err;
        state.progress = undefined;
        state.updateInfo = undefined;
    }
    const onUpdateAvailable = async (info: UpdateInfo) => {
        console.log('update available', info);
        if (state.updateInfo && state.updateInfo.version === info.version) {
            return
        }
        state.updateInfo = info;
    }
    const onUpdateCanceled = () => {
        console.log('update canceled');
        state.progress = undefined;
    }
    const onImmediateCheck = async () => {
        console.log('immediate check');
        const isUpdateAvailable = await check(true);
        if (!isUpdateAvailable) {
            ElMessage.info('已经是最新版本')
        }
    }
    const check = async (focus = false) => {
        if (metadata.checking || (state.disabled && !focus)) return false;
        metadata.checking = true;
        state.disabled = false
        postponed.value = 0
        try {
            const result = await window.API.checkForUpdates()
            console.log('check update result', result)
            if (result) {
                return result.isUpdateAvailable;
            } else {
                return false
            }
        } catch (e) {
            console.log('window.API.checkForUpdates() get update check result error, reason: ' + e)
            return false
            // 弹出提示框
        } finally {
            metadata.checking = false;
        }
    }
    const confirm = async () => {
        try {
            state.confirming = true;
            const result = await window.API.confirmUpdate()
            console.log(result)
        } catch (e) {
            console.log('window.API.confirmUpdate() get result error', e)
            ElMessage.error(e instanceof Error ? e.message : '更新失败')
            // 弹出提示框
        } finally {
            state.confirming = false;
        }
    }
    const cancel = async () => {
        try {
            const result = await window.API.cancelUpdate()
            console.log(result)
        } catch (e) {
            console.log('window.API.cancelUpdate() get result error')
            // 弹出提示框
        }
    }
    const restartNow = async () => {
        try {
            const result = await window.API.restartNow()
            console.log(result)
        } catch (e) {
            console.log('window.API.cancelUpdate() get result error')
            // 弹出提示框
        }
    }
    const postponingUpdate = () => {
        postponed.value = Date.now() + 60 * 60 * 1000;
    }
    const close = () => {
        state.error = undefined;
        state.progress = undefined;
        state.updateInfo = undefined;
    }
    // const interval = () => {
    //     metadata.timer = window.setTimeout(() => {
    //         check(true).catch(console.error);
    //     }, 2 * 60 * 1000)
    // }
    const unregisters: (() => void)[] = []
    onMounted(() => {
        unregisters.push(
            window.IPCListeners.onDownloadProgress(onProgress),
            window.IPCListeners.onUpdateDownloaded(onDownloaded),
            window.IPCListeners.onUpdateError(onError),
            window.IPCListeners.onUpdateAvailable(onUpdateAvailable),
            window.IPCListeners.onUpdateCanceled(onUpdateCanceled),
            window.IPCListeners.onImmediateCheck(onImmediateCheck)
        )
        check()
        // interval()
    })
    onUnmounted(() => {
        unregisters.forEach((unregister) => unregister())
        if (metadata.timer) {
            clearTimeout(metadata.timer)
            metadata.timer = undefined;
        }
    })
    const visible = computed(() => {
        return state.downloaded && !state.disabled && postponed.value < Date.now()
    })
    provide(KEY, {state, check, disable: () => state.disabled = true, resume: () => state.disabled = false})
    return {
        visible,
        state,
        check,
        confirm,
        cancel,
        restartNow,
        close,
        postponingUpdate
    }
}
export const useCheckUpdate = (options: {
    immediate?: boolean;
} = {}) => {
    const value = inject(KEY);
    if (!value) throw new Error("Unable to locate the provided 'useCheckUpdateProvider'.");
    onMounted(() => {
        if (options.immediate) {
            value.check(true)
        }
    })
    const updateAvailable = computed(() => {
        return value.state.updateInfo !== undefined
    })
    return {
        state: value.state,
        check: value.check,
        disable: value.disable,
        resume: value.resume,
        updateAvailable
    }
}