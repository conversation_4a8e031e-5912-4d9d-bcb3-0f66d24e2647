import { _http } from './service.js'
import dayjs from 'dayjs'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { CommonEdit, PageForm } from '../interfaceAndClass/common.js'

function table() {
  interface TablePageData<Row> {
    total: number
    rows: Row[]
  }

  type LoadingAlias = (string | boolean)[]
  type TableIds = string[]

  const loading = ref<boolean>(false)
  const saveLoading = ref<boolean>(false)
  const dialogShow = ref<boolean>(false)
  const mapFuGaiModel = ref<string>('table')
  const isBig = ref<boolean>(true)
  const type = ref<string>('list')

  const reactiveTableObj = reactive({
    tableData: [],
    ids: <TableIds>[],
    loadingAlias: <LoadingAlias>[], // 封装的按钮保存是使用的loading
  })
  const pageForm = ref<PageForm>({
    current: 1,
    limit: 10,
    total: 0,
  })
  const tableObj = reactive({
    optionProps: {
      value: 'id',
      label: 'label',
      checkStrictly: true,
    },
    headers: {},
  })

  /**
   *  @par {Object} 请求参数
   *  @path {String} 请求url路径
   *  @url {String} 请求url地址
   */
  function getTableData<T>(
    par: object,
    path: string,
    url: string,
    callback?: (data: TablePageData<T>, pageForm: PageForm) => void,
  ) {
    const params = {
      ...pageForm.value,
      ...par,
    }
    loading.value = true
    _http.common
      .fetchPost(params, path, url)
      .then((val: any) => {
        loading.value = false
        if (val && val.rows) {
          reactiveTableObj.tableData = val.rows
        } else {
          reactiveTableObj.tableData = []
        }
        if (val && val.total) {
          pageForm.value.total = val.total
        } else {
          pageForm.value.total = 0
        }
        if (callback) return callback(val, pageForm.value)
      })
      .catch(() => {
        loading.value = false
      })
  }

  function addTableData(
    params: object,
    path: string,
    url: string,
    callback: (val: any) => void,
    sucessMessage: string,
    failureMessage: string,
    operateAlias: string,
  ) {
    sucessMessage = sucessMessage ? sucessMessage : '新增成功!'
    failureMessage = failureMessage ? failureMessage : '新增失败!'

    reactiveTableObj.loadingAlias = [operateAlias, true]
    // saveLoading.value = true
    _http.common
      .fetchPost(params, path, url)
      .then((val: any) => {
        // saveLoading.value = false
        reactiveTableObj.loadingAlias = [operateAlias, false]
        if (val) {
          ElMessage({
            type: 'success',
            message: sucessMessage,
          })
          dialogShow.value = false
          callback(val)
        } else {
          // 错误的提示在响应拦截中已经有了
          // ElMessage({
          //   type: 'error',
          //   message: failureMessage,
          // })
        }
      })
      .catch(() => {
        // ElMessage({
        //   type: 'error',
        //   message: failureMessage,
        // })
        // saveLoading.value = false
        reactiveTableObj.loadingAlias = [operateAlias, false]
      })
  }
  /*
    编辑
  */
  /**
   * @params {Object} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   * @callback {} , 编辑成功的回调
   */
  function editTableData(
    params: object,
    path: string,
    url: string,
    callback: (val: object) => void,
    sucessMessage: string,
    failureMessage: string,
    operateAlias: string,
  ) {
    sucessMessage = sucessMessage ? sucessMessage : '编辑成功!'
    failureMessage = failureMessage ? failureMessage : '编辑失败!'
    reactiveTableObj.loadingAlias = [operateAlias, true]
    _http.common
      .fetchPost(params, path, url)
      .then((val: any) => {
        reactiveTableObj.loadingAlias = [operateAlias, false]
        // saveLoading.value = false
        if (val) {
          dialogShow.value = false
          ElMessage({
            type: 'success',
            message: sucessMessage,
          })
          callback(val)
        } else {
          // 错误的提示在响应拦截中已经有了
          // ElMessage({
          //   type: 'error',
          //   message: failureMessage,
          // })
        }
      })
      .catch((e) => {
        console.log(e)
        // ElMessage({
        //   type: 'error',
        //   message: failureMessage,
        // })
        // saveLoading.value = false
        reactiveTableObj.loadingAlias = [operateAlias, false]
      })
  }

  /*
    根据id删除
  */
  /**
   * @id {String} 请求参数
    @path {String} , 请求url路径
    @url {String} , 请求url地址
    @callback {function}  删除成功的回调
  */
  function deleteTabaleData(
    id: string,
    path: string,
    url: string,
    callback: () => void,
  ) {
    _http.common
      .fetchGet(id, path, url)
      .then((val: any) => {
        if (val) {
          ElMessage.success('删除成功')
          callback()
        } else {
          // ElMessage.error('删除失败')
        }
      })
      .catch(() => {
        // ElMessage.error('删除失败')
      })
  }

  /*
    根据id批量删除删除
  */
  /**
   * @ids {Object} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   * @callback {} , 删除成功的回调
   */
  function deleteAllTabaleData(
    ids: Array<any>,
    path: string,
    url: string,
    callback: () => void,
  ) {
    _http.common
      .fetchPost(ids, path, url)
      .then((val: any) => {
        if (val) {
          ElMessage.success('删除成功')
          callback()
        } else {
          // ElMessage.error('删除失败')
        }
      })
      .catch(() => {
        // ElMessage.error('删除失败')
      })
  }
  /**
   * @id {String} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   * @callback {function} , 删除成功的回调
   */
  function handleClickDel(
    id: string,
    path: string,
    url: string,
    callback: () => void,
  ) {
    ElMessageBox.confirm('您确定要删除这条数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        deleteTabaleData(id, path, url, () => {
          if (
            pageForm.value.current ===
              Math.ceil(pageForm.value.total / pageForm.value.limit) &&
            pageForm.value.total % pageForm.value.limit === 1
          ) {
            pageForm.value.current = pageForm.value.current - 1 || 1
          }
          callback()
        })
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除',
        })
      })
  }
  /*
    根据id批量删除逻辑
  */
  /**
   * @ids {Object} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   * @callback {function} , 删除成功的回调
   */
  function handleClickDelAll(
    ids: Array<any>,
    path: string,
    url: string,
    callback: () => void,
    data?: Array<any>,
  ) {
    const realData = data ? data : reactiveTableObj.tableData
    if (realData) {
      if (realData.length) {
        if (ids.length) {
          ElMessageBox.confirm('确定要删除这些数据吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deleteAllTabaleData(ids, path, url, () => {
                if (
                  pageForm.value.current ===
                    Math.ceil(pageForm.value.total / pageForm.value.limit) &&
                  (pageForm.value.total % pageForm.value.limit === ids.length ||
                    pageForm.value.limit === ids.length)
                ) {
                  pageForm.value.current = pageForm.value.current - 1 || 1
                }
                callback()
              })
            })
            .catch(() => {
              ElMessage({
                type: 'info',
                message: '已取消删除',
              })
            })
        } else {
          ElMessage.warning('请先选择数据！')
        }
      } else {
        ElMessage.warning('暂无数据！')
      }
    } else {
      ElMessage.warning('暂无数据！')
    }
  }

  /*
    表单验证
  */
  /**
   * 表单参数
   * @param {Object} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   * 表格参数, 表单新增成功之后刷新列表， isGetList需设置为true
   * @listParams {Object} , 请求参数
   * @listPath {String} , 请求url路径
   * @listUrl {String} , 请求url地址
   * @callback {} , 请求成功的回调
   * @alias {String}, 判断是新增还是编辑
   */
  function validateDialog(
    dialogForm: any,
    params: object,
    path: string,
    url: string,
    callback: (val: any) => void,
    alias: string,
    sucessMessage: string,
    failureMessage: string,
    operateAlias: string,
  ) {
    dialogForm.value.validate((valid: boolean, fileds) => {
      if (valid) {
        if (alias === 'add') {
          addTableData(
            params,
            path,
            url,
            callback,
            sucessMessage,
            failureMessage,
            operateAlias,
          )
        } else if (alias === 'edit') {
          editTableData(
            params,
            path,
            url,
            callback,
            sucessMessage,
            failureMessage,
            operateAlias,
          )
        } else {
          reactiveTableObj.loadingAlias = [operateAlias, false]
          // saveLoading.value = false
        }
      } else {
        console.log('error submit!!')
        reactiveTableObj.loadingAlias = [operateAlias, false]
        // saveLoading.value = false
      }
    })
  }

  function validateTipDialog(
    dialogForm: any,
    params: object,
    path: string,
    url: string,
    callback: (val: any) => void,
    alias: string,
    sucessMessage: string,
    failureMessage: string,
    confirmFun: () => void,
    isShowCheck: boolean,
    operateAlias: string,
  ) {
    dialogForm.validate((valid: boolean, errorMessage: any) => {
      if (valid) {
        ElMessageBox.confirm('请认真核对，确认填写内容是否正确。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            if (alias === 'add') {
              addTableData(
                params,
                path,
                url,
                callback,
                sucessMessage,
                failureMessage,
                operateAlias,
              )
            } else if (alias === 'edit') {
              editTableData(
                params,
                path,
                url,
                callback,
                sucessMessage,
                failureMessage,
                operateAlias,
              )
            } else {
              reactiveTableObj.loadingAlias = [operateAlias, false]
              return
            }
            if (confirmFun) confirmFun()
          })
          .catch(() => {
            ElMessage({
              type: 'info',
              message: '已取消操作',
            })
            reactiveTableObj.loadingAlias = [operateAlias, false]
          })
      } else {
        console.log('error submit!!', isShowCheck)
        reactiveTableObj.loadingAlias = [operateAlias, false]
        if (isShowCheck) {
          let errMes = ''
          let length = 0
          for (const key in errorMessage) {
            length++
            const err = errorMessage[key][0].errorMessage
              .split(',')[0]
              .split('，')[0]
            errMes += '<p>' + err.slice(3) + ';</p>'
          }
          ElMessageBox.alert(
            `<div><p>填写错误共 <span style="color: red;font-weight: bold;">${length}</span>处，具体如下：</p>` +
              errMes +
              '</div>',
            '校验提示',
            {
              customClass: 'jiaoyan-box',
              confirmButtonClass: 'jiaoyan-but',
              dangerouslyUseHTMLString: true,
            },
          )
        }
        return false
      }
    })
  }
  /*
    根据id查询详情
  */
  /**
   * @id {Object} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   */
  function detailTableData(
    id: string,
    path: string,
    url: string,
    callback: (val: any) => void,
  ) {
    _http.common.fetchGet(id, path, url).then((val: any) => {
      if (val) {
        if (callback) {
          callback(val)
        } else {
          // form = val
        }
      }
    })
  }

  /*
    导出的点击方法
  */
  /**
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   */
  function handleExport(search: object, path: string, url: string, data?: any) {
    const realData = data ? data : reactiveTableObj.tableData
    if (!realData.length) {
      return ElMessage.warning('暂无数据')
    }
    if (reactiveTableObj.ids.length === 0) {
      // ElMessage({
      //   message: '请先选择要导出的数据',
      //   type: 'warning'
      // })
      // 不勾选 导出所有
      exportTableData({ ...search }, path, url)
      return false
    }
    if (realData.length) {
      ElMessageBox.confirm('确定要导出这些数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          exportTableData({ ...search, ids: reactiveTableObj.ids }, path, url)
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消导出',
          })
        })
    } else {
      ElMessage.warning('暂无数据！')
    }
  }
  /*
    通过的点击方法
  */
  /**
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   */
  function handlePass(
    params: object,
    path: string,
    url: string,
    data: any,
    callback: () => void,
  ) {
    const realData = data ? data : reactiveTableObj.tableData
    if (realData.length) {
      if (reactiveTableObj.ids.length) {
        ElMessageBox.confirm('确定要通过吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            _http.common.fetchExportPost(params, path, url).then((val: any) => {
              console.log(val)
              ElMessage.success('已通过')
              callback()
            })
          })
          .catch(() => {
            ElMessage({
              type: 'info',
              message: '已取消通过操作',
            })
          })
      } else {
        ElMessage.warning('请先选择数据！')
      }
    } else {
      ElMessage.warning('暂无数据！')
    }
  }
  /*
    不通过的点击方法
  */
  /**
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   */
  function handleNoPass(
    params: object,
    path: string,
    url: string,
    data: any,
    callback: () => void,
  ) {
    const realData = data ? data : reactiveTableObj.tableData
    if (realData.length) {
      if (reactiveTableObj.ids.length) {
        ElMessageBox.confirm('确定要不通过吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            _http.common.fetchExportPost(params, path, url).then((val: any) => {
              console.log(val)
              ElMessage.success('已不通过')
              callback()
            })
          })
          .catch(() => {
            ElMessage({
              type: 'info',
              message: '已取消不通过操作',
            })
          })
      } else {
        ElMessage.warning('请先选择数据！')
      }
    } else {
      ElMessage.warning('暂无数据！')
    }
  }
  /*
    导出的接口方法
  */
  /**
   * @params {Object} 请求参数
   * @path {String} , 请求url路径
   * @url {String} , 请求url地址
   */
  function exportTableData(params: object, path: string, url: string) {
    const loadingMask = ElLoading.service({
      lock: true,
      text: '拼命加载中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    _http.common
      .fetchExportPost(params, path, url)
      .then((val: any) => {
        console.log(val)
        loading.value = false
        ElMessage.success('导出成功')
        loadingMask.close()
      })
      .catch(() => {
        loadingMask.close()
      })
  }

  // 分页
  function handlePage(params: any, callBack: () => void) {
    pageForm.value = params
    callBack()
  }
  function initPageForm() {
    pageForm.value = {
      ...pageForm.value,
      current: 1,
      total: 0,
    }
  }
  // 多选
  function handleSelectionChange(e: Array<any>) {
    reactiveTableObj.ids = []
    const chooseIds: Array<string> = []
    if (e.length) {
      e.map((item) => {
        if (item.id) {
          chooseIds.push(item.id)
        }
      })
    }
    reactiveTableObj.ids = chooseIds
  }

  function checkSelectable(row: any, id: string) {
    return id === row.institutionId
  }
  //
  // uploadExcelComm (data, importUrl, callBack) {
  //   let params = new FormData()
  //   const Authorization = sessionStorage.getItem('token')
  //   params.append("file", data.file)
  //   ElMessageBox.axios
  //     .post(ElMessageBox.api.baseUrl + importUrl, params, {
  //       responseType: "blob",
  //       headers: {
  //         "Content-Type": "application/json",
  //         Authorization
  //       },
  //     })
  //     .then((response) => {
  //       if (response && response.data.type === 'application/json') {
  //         const reader = new FileReader()  //创建一个FileReader实例
  //         reader.readAsText(response.data, 'utf-8') //读取文件,结果用字符串形式表示
  //         reader.onload = (e) => {//读取完成后,**获取reader.result**
  //           let result = JSON.parse(e.target.result)
  //           if (result.code === 200) {
  //             ElMessage.success("导入成功")
  //           } else {
  //             ElMessage.error(result.message || result.msg)
  //           }
  //         }
  //       }
  //       callBack()
  //     })
  //     .catch((error) => {
  //       console.log(error)
  //     })
  // },
  function winHandle(alias: string) {
    switch (alias) {
      case 'big':
        isBig.value = !isBig.value
        mapFuGaiModel.value = isBig.value ? 'table' : 'quanMap'
        break
      case 'small':
        mapFuGaiModel.value = 'huanyuanBtn'
        break
      case 'huanyuan':
        type.value = 'list'
        mapFuGaiModel.value = isBig.value ? 'table' : 'quanMap'
    }
  }

  function rgbaInit(color: any, num: number) {
    const color1 = color.split('(')[0] + 'a'
    const color2 = '(' + color.split('(')[1]
    const color3 = color2.split(')')[0] + ',' + num + ')'
    const opaBg = color1 + color3
    return opaBg
  }
  // 弹窗内表格批量删除
  function tableActionBatchRemove<T extends CommonEdit>(
    myLists: Array<T>,
    myIds: Array<string>,
  ) {
    // 全选
    if (
      myIds &&
      myIds.length &&
      myLists &&
      myLists.length &&
      myLists.length === myIds.length
    ) {
      return []
    }
    // -----------------------------------------
    // 删除某几个
    const handleTableLists: Array<T> = []
    if (myIds && myIds.length) {
      myLists.forEach((item) => {
        if (item.id && !myIds.includes(item.id)) handleTableLists.push(item)
      })
      return handleTableLists
    } else {
      ElMessage.info('请勾选数据')
      return myLists
    }
  }
  return {
    loading,
    saveLoading,
    dialogShow,
    reactiveTableObj,
    tableObj,
    mapFuGaiModel,
    isBig,
    type,
    pageForm,
    getTableData,
    addTableData,
    editTableData,
    handleClickDel,
    handleClickDelAll,
    detailTableData,
    deleteAllTabaleData,
    handleExport,
    handlePass,
    handleNoPass,
    validateDialog,
    validateTipDialog,
    handlePage,
    handleSelectionChange,
    checkSelectable,
    winHandle,
    initPageForm,
    rgbaInit,
    dayjs,
    tableActionBatchRemove,
    deleteTabaleData,
  }
}

export default table
