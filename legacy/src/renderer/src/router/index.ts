import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'

const modules: any = import.meta.glob('./modules/**/*.ts', { eager: true })

const routeChildren = []
for (const path in modules) {
  if (path.indexOf('zCode') > -1 || path.indexOf('zTableTemplate') > -1) {
    continue
  }
  for (const itemPath in modules[path]) {
    routeChildren.push(...modules[path][itemPath])
  }
}

export const menu = routeChildren
const router = createRouter({
  // scrollBehavior: () => ({ left: 0, top: 0 }),
  // history: createWebHistory(),
  history: createWebHashHistory(),
  routes: menu
})

router.beforeEach((to, from, next) => {
  next()
  /*if (to.fullPath && to.fullPath.length < 200) {
    sessionStorage.setItem('lastRoute', to.fullPath)
  }
  if (to.path === '/electronic-table') {
    const clientId = window.sessionStorage.getItem('clientId')
    window.open(
      import.meta.env.VITE_earth +
        '?token=' +
        window.sessionStorage.getItem('token') +
        '&clientId=' +
        clientId,
    )
    return
  }
  // console.log('beforeEach')
  if (window.__axiosCancelTokenArr) {
    window.__axiosCancelTokenArr.forEach((ele) => {
      ele.cancel('取消请求')
    })
    window.__axiosCancelTokenArr = []
  }
  console.log(to, from, 'router')
  if (to.meta.requireAuth) {
    const user = JSON.parse(window.sessionStorage.getItem('userInfo'))
    if (user) {
      next()
    } else {
      next('/')
    }
  } else {
    next()
  }*/
})

export default router
