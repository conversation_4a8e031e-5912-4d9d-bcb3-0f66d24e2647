import login from './../../views/login.vue'
import register from './../../views/register.vue'
import mainPage from './../../views/main/mainPage.vue'
import {RouteRecordRaw} from "vue-router";
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: '',
    redirect: '/login',
  },
  {
    path: '/login',
    name: 'login',
    component: login,
  },
  {
    path: '/register',
    name: 'register',
    component: register,
  },
  {
    path: '/main-page',
    name: 'main-page',
    component: mainPage
  }
]
export default routes
