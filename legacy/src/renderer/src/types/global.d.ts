// 全局API类型定义
declare global {
    // import type { UpdateCheckResult } from 'electron-updater';

    interface Window {
        API: {
            // 用户凭据相关
            saveUserCredentials: (data: any) => Promise<boolean>;
            loadUserCredentials: () => Promise<any>;
            clearUserCredentials: () => Promise<boolean>;
            updateAutoLoginSetting: (autoLogin: boolean) => Promise<boolean>;

            // 系统相关
            getVersion: () => Promise<string>;
            showUrl: (url: string) => Promise<void>;
            mirrorNodeID: () => Promise<string>;
            abortMirrorNodeID: (reason?: unknown) => Promise<void>;
            writeInfo: (info: string) => Promise<void>;
            checkNet: () => Promise<number>;
            getLog: () => Promise<
                {
                    text: string;
                    level: number;
                    t: number;
                    name: string;
                    flag: number;
                    type: number;
                }[]
            >;
            packLog: (fileName: string) => Promise<void>;
            writeLogInfo: (
                text: string,
                level?: number,
                type?: string,
                flag?: number,
            ) => Promise<void>;
            writeLogError: (
                text: string,
                level?: number,
                type?: string,
                flag?: number,
            ) => Promise<void>;
            getUID: () => Promise<string>;
            mirrorIP: () => Promise<[string, number]>;
            runMirror: () => Promise<void>;
            abortRunMirror: (reason?: unknown) => Promise<void>;
            stopMirror: () => Promise<void>;
            installMirror: (url: string, md5?: string) => Promise<void>;
            installMirrorStat: () => Promise<[number, number]>;
            uninstallMirror: () => Promise<void>;
            getLatestMirrorInfo: () => Promise<{
                url: string;
                hash: string;
                version: string;
                release_note: string;
            }>;

            // 开机自启
            enableAutoStart: () => Promise<void>;
            disableAutoStart: () => Promise<void>;
            checkAutoStart: () => Promise<boolean>;

            // 检查项
            checkPowerShell: () => Promise<boolean>;
            checkMirror: () => Promise<{
                installed: boolean;
                running: boolean;
                installing: boolean;
            }>;
            installWSL: () => Promise<{
                succeed: boolean;
                needRestart: boolean;
            }>;
            checkWSLEnabled: () => Promise<number>;
            checkOS: () => Promise<boolean>;
            checkVirtual: () => Promise<boolean>;
            checkMirrorNodeIsIdle: () => Promise<boolean>;

            // 自动更新
            checkForUpdates: () => Promise<
                import("electron-updater").UpdateCheckResult
            >;
            confirmUpdate: () => Promise<string[]>;
            cancelUpdate: () => Promise<void>;
            restartNow: () => Promise<void>;

            loadUserSettings: () => Promise<{ autoOrder: boolean }>;

            openWindowsFeaturesDialog: () => Promise<void>;
            checkSelfNodeStatus: () => Promise<
                { status: "idle" } | { status: "running"; createdAt: string }
            >;

            // 其他API
            [key: string]: any;
        };
        IPCListeners: {
            onAutoStartTask(cb: () => void): () => void;
            onDownloadProgress(cb: () => void): () => void;
            onUpdateDownloaded(cb: () => void): () => void;
            onUpdateError(cb: () => void): () => void;
            onUpdateAvailable(cb: () => void): () => void;
            onUpdateCanceled(cb: () => void): () => void;
            onImmediateCheck(cb: () => void): () => void;
            onLoginExpired(cb: () => void): () => void;
            onUserSettingsChange(
                key: string,
                cb: (value: unknown) => void,
            ): () => void;
            onSelfNodeStatusChange(
                cb: (
                    cur:
                        | { status: "idle" }
                        | { status: "running"; createdAt: string },
                ) => void,
            ): () => void;
        };
        AppConfig: {
            getSync(): {
                apiUrl: string;
                portalUrl: string;
                clientName: string;
            };
        };
    }
}

export {};
