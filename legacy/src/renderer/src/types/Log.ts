import { LogSource, LogLevel } from './enums';
export default class Log {
  time: string; //时间
  logSource: LogSource; // 日志来源
  logLevel: LogLevel; // 日志等级
  content: string; // 日志内容
  isShowOnUI: boolean; // 是否需要再客户端

  constructor(time: string, logSource: LogSource, logLevel: LogLevel, content: string, isShowOnUI: boolean) {
    this.time = time;
    this.logSource = logSource;
    this.logLevel = logLevel;
    this.content = content;
    this.isShowOnUI = isShowOnUI;
  }
}