<script setup lang="ts">
import {useCheckUpdateProvider} from "@hooks/useCheckUpdate.ts";

const { visible, state, postponingUpdate, confirm, restartNow, cancel, close } = useCheckUpdateProvider()
</script>

<template>
  <slot/>
  <el-dialog
      v-model="visible"
      title="有可用更新"
      width="88%"
      align-center
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancel"
  >
    <el-alert v-if="state.error" type="error" :title="state.error?.message || String(state.error)"/>
    <div>
      <template v-if="state.downloaded">
        <span>下载完成</span>
      </template>
      <template v-else-if="state.progress">
        <el-progress :percentage="Math.ceil(state.progress.percent)" />
      </template>
      <template v-else>
        <span>发现新版本v{{state.updateInfo?.version??'--'}}，是否升级？</span>
      </template>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <template v-if="state.downloaded">
          <el-button @click="postponingUpdate">以后再说</el-button>
          <el-button type="primary" @click="restartNow">
            立即重启
          </el-button>
        </template>
        <template v-else-if="state.progress">\
          <el-button @click="cancel">
            取消
          </el-button>
        </template>
        <template v-else-if="state.error">
          <el-button @click="close">
            关闭
          </el-button>
        </template>
        <template v-else>
          <el-button @click="postponingUpdate">以后再说</el-button>
          <el-button type="primary" @click="confirm" :loading="state.confirming">
            立即升级
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>