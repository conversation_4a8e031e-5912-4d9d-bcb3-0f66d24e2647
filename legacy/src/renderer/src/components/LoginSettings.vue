<template>
  <div class="login-settings">
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>登录设置</span>
        </div>
      </template>
      
      <div class="settings-content">
        <div class="setting-item">
          <div class="setting-label">自动登录</div>
          <div class="setting-control">
            <el-switch 
              v-model="autoLoginEnabled" 
              @change="handleAutoLoginChange"
              :disabled="!hasSavedCredentials"
            />
          </div>
          <div class="setting-desc">
            {{ hasSavedCredentials ? '启用后下次打开应用将自动登录' : '请先登录并选择记住登录状态' }}
          </div>
        </div>
        
        <div class="setting-item" v-if="hasSavedCredentials">
          <div class="setting-label">已保存的账号</div>
          <div class="setting-control">
            <span class="saved-phone">{{ savedCredentials?.phone || '未知' }}</span>
          </div>
          <div class="setting-desc">
            保存时间: {{ formatSaveTime(savedCredentials?.savedAt) }}
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-label">清除登录数据</div>
          <div class="setting-control">
            <el-button 
              type="danger" 
              size="small" 
              @click="handleClearCredentials"
              :disabled="!hasSavedCredentials"
            >
              清除数据
            </el-button>
          </div>
          <div class="setting-desc">
            清除所有保存的登录信息，下次需要重新登录
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const autoLoginEnabled = ref(false)
const hasSavedCredentials = ref(false)
const savedCredentials = ref(null)

// 加载当前设置
async function loadSettings() {
  try {
    const credentials = await window.API.loadUserCredentials()
    if (credentials) {
      hasSavedCredentials.value = true
      savedCredentials.value = credentials
      autoLoginEnabled.value = credentials.autoLogin !== false
    } else {
      hasSavedCredentials.value = false
      savedCredentials.value = null
      autoLoginEnabled.value = false
    }
  } catch (error) {
    console.error('加载登录设置失败:', error)
    ElMessage.error('加载设置失败')
  }
}

// 处理自动登录开关变化
async function handleAutoLoginChange(value: boolean) {
  try {
    const success = await window.API.updateAutoLoginSetting(value)
    if (success) {
      ElMessage.success(value ? '已启用自动登录' : '已禁用自动登录')
    } else {
      ElMessage.error('设置失败')
      // 回滚开关状态
      autoLoginEnabled.value = !value
    }
  } catch (error) {
    console.error('更新自动登录设置失败:', error)
    ElMessage.error('设置失败')
    // 回滚开关状态
    autoLoginEnabled.value = !value
  }
}

// 清除登录凭据
async function handleClearCredentials() {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有保存的登录信息吗？清除后需要重新登录。',
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const success = await window.API.clearUserCredentials()
    if (success) {
      ElMessage.success('登录数据已清除')
      // 重新加载设置
      await loadSettings()
    } else {
      ElMessage.error('清除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除登录凭据失败:', error)
      ElMessage.error('清除失败')
    }
  }
}

// 格式化保存时间
function formatSaveTime(timestamp: number) {
  if (!timestamp) return '未知'
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadSettings()
})
</script>

<style lang="scss" scoped>
.login-settings {
  padding: 20px;
  
  .settings-card {
    max-width: 500px;
    margin: 0 auto;
  }
  
  .card-header {
    font-size: 18px;
    font-weight: 600;
  }
  
  .settings-content {
    .setting-item {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #eee;
      
      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }
      
      .setting-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }
      
      .setting-control {
        margin-bottom: 4px;
        
        .saved-phone {
          font-weight: 500;
          color: #409eff;
        }
      }
      
      .setting-desc {
        font-size: 12px;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}
</style> 