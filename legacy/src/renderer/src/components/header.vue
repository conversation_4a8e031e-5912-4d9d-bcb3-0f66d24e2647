<template>
    <div class="header-wrap">
        <img class="logo" src="@assets/images/logo-green.png" />
        <div class="header-wrap-right">
            <img
                class="header-wrap-sign"
                v-if="netStatus"
                src="@assets/images/wifi-green.png"
            />
            <img
                class="header-wrap-sign"
                v-if="!netStatus"
                src="@assets/images/wifi-error.png"
            />
            <img
                class="header-wrap-wallet-img"
                src="@assets/images/wallet-b.svg"
            />
            <span class="header-wrap-amount">{{
                reactiveObj.walletSum.remain_amount
            }}</span>
            <label class="header-wrap-recharge">提现</label>
            <el-dropdown trigger="click">
                <div class="br-dropdown cn-flex-c-r">
                    <img
                        class="header-usericon"
                        src="@assets/images/usericon1.png"
                    />
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item>
                            <span class="header-dropdown-phone">{{
                                reactiveObj.personInfo.phone
                            }}</span>
                        </el-dropdown-item>
                        <el-dropdown-item
                            :key="item.alias + '_' + index"
                            @click.stop="handleClickDropdown(item)"
                            v-for="(item, index) in reactiveObj.dropdownData"
                        >
                            <span>{{ item.label }}</span>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { _api, _http } from "./../mixins/service";
import { removeCookie } from "../global/cookie";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
const $router = useRouter();
const props = defineProps({
    netStatus: {
        type: Boolean,
    },
});
let reactiveObj = reactive({
    personInfo: {
        phone: "", // 手机号
        create_time: "", // 加入时间
        invitation_code: "", // 邀请码
    },
    walletSum: {
        remain_amount: 0.0,
    },
    dropdownData: [
        {
            label: "个人中心",
            alias: "personal",
        },
        {
            label: "退出登录",
            alias: "logout",
        },
    ],
});
function handleClickDropdown(item) {
    switch (item.alias) {
        case "personal":
            // 获取路由的完整路径
            window.API.showUrl(_api.portalUrl);
            // 打开新窗口
            break;
        case "updatePwd":
            // $router.push({ name: 'userPersonal' })
            break;
        case "logout":
            logout();
            break;
    }
}
// 退出登录
async function logout() {
    _http.common
        .fetchPost({}, _api.logout, _api.baseUrl)
        .then(async (res) => {
            console.log(res, "用户注销");
            if (res && res.success) {
                ElMessage.success("退出成功");
                removeCookie("user");

                // 清除本地存储的用户凭据
                try {
                    await window.API.clearUserCredentials();
                    console.log("已清除保存的登录凭据");
                } catch (error) {
                    console.error("清除登录凭据失败:", error);
                }

                // 清除localStorage中的token
                localStorage.removeItem("authToken");
                localStorage.removeItem("uId");
                localStorage.removeItem("token");

                $router.push({ name: "login", query: { flag: "loginForm" } });
            } else {
                ElMessage.error(res.err_message || "退出失败");
            }
        })
        .catch((error) => {
            console.error("用户登出失败:", error);
        });
}
function loadWalletSum() {
    let params = {
        type: "SUPPLIER",
    };
    _http.common
        .fetchPost(params, _api.walletNum, _api.baseUrl)
        .then((res) => {
            console.log(res, "钱包明细 ");
            if (res && res.success) {
                reactiveObj.walletSum.remain_amount = res.data.remain_amount;
            }
        })
        .catch((error) => {
            console.error("钱包明细加载失败:", error);
        });
}
// 加载个人基本信息
function loadPersonInfo() {
    _http.common
        .fetchGet(null, _api.userDetail, _api.baseUrl)
        .then((res) => {
            console.log(res, "用户基本信息");
            if (res && res.success) {
                localStorage.setItem("uId", res.data["id"]);
                reactiveObj.personInfo = res.data;
            }
        })
        .catch((error) => {
            console.error("个人基本信息加载失败:", error);
        });
}
function init() {
    loadWalletSum();
    loadPersonInfo();
}
const unregisters = [];
onMounted(() => {
    init();
    const listener = () => {
        loadWalletSum();
    };
    window.addEventListener("focus", listener);
    unregisters.push(() => {
        window.removeEventListener("focus", listener);
    });
});
onBeforeUnmount(() => {
    unregisters.forEach((unregister) => unregister());
});
</script>
<style lang="scss">
.header-wrap {
    width: 100%;
    height: 64px;
    display: flex;
    border-bottom: 1px $main-color solid;
    .logo {
        width: 102px;
        height: 24px;
        line-height: 64px;
        margin-top: 19px;
        margin-left: 17px;
    }
    .header-wrap-right {
        margin-left: auto;
        margin-right: 17px;
        margin-top: 19px;
        .header-wrap-sign {
            margin-right: 10px;
            width: 16px;
            height: 13px;
        }
        .header-wrap-wallet-img {
            margin-right: 10px;
        }
        .header-wrap-amount {
            margin-right: 10px;
        }
        .header-wrap-recharge {
            color: $main-color;
            margin-right: 10px;
        }
        .header-usericon {
            width: 28px;
            height: 28px;
            position: relative;
        }
    }
}
.el-popper.is-light {
    border: 1px $main-color solid !important;
}
.el-dropdown__popper .el-dropdown-menu {
    background: $background-color !important;
    color: $white !important;
    border: none !important;
    .el-dropdown-menu__item {
        color: $white !important;
        &:hover {
            background: none !important;
            color: $main-color !important;
        }
        &:focus {
            background: none !important;
        }
        .header-dropdown-phone {
            color: #bbcfdf !important;
            &:hover {
                color: #bbcfdf !important;
            }
        }
    }
}
</style>
