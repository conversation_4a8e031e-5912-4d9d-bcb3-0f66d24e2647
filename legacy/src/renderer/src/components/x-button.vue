<template>
  <component :is="is" :class="className" :disabled="disabled" :style="{ display }" type="button" @click="checkClick">
    <x-icon v-if="loading" :size="16" class="icon-loading mr-1" icon="loading" />
    <x-icon v-if="!loading && icon" :icon="icon" :size="16" class="mr-8px" />
    <slot v-if="!text"></slot>
    {{ text }}
  </component>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    type?: 'default' | 'info' | 'confirm' | 'seed' | 'danger' | 'cancel' | 'white' | 'white-border'
    size?: 'normal' | 'small'
    disabled?: boolean
    icon?: string
    text?: string
    is?: string
    loading?: boolean
    display?: string
  }>(),
  {
    type: 'default',
    size: 'normal',
    disabled: false,
    text: '',
    is: 'button',
    icon: '',
    display: 'flex',
  },
)
const emits = defineEmits(['click'])

const className = computed(() => ['x-button', `x-button_type-${props.type}`, `x-button_size-${props.size}`, `x-button_${props.disabled ? 'disabled' : 'ok'}`])

const checkClick = (e: MouseEvent) => {
  if (props.disabled) return
  emits('click', e)
}
</script>
<style lang="scss">
.x-button {
  height: 40px;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  padding: 0 14px;
  gap: 4px;
  border: 0 none;
  cursor: pointer;
  font-size: 16px;
  transition: 0.2s;
  @keyframes loading {
    0% {
      transform: rotate(0);
      opacity: 0.5;
    }
    50% {
      transform: rotate(180deg);
      opacity: 1;
    }
    100% {
      transform: rotate(360deg);
      opacity: 0.5;
    }
  }
  .icon-loading {
    animation: loading 0.4s ease-in-out infinite;
  }
}
.x-button_type-default {
  background-color: #000;
  color: #fff;
  &:focus {
    background-color: #262626;
  }
  &:hover {
    background-color: #262626;
  }
  &.x-button_disabled {
    background: rgba(0, 0, 0, 0.5);
    cursor: default;
  }
}
.x-button_type-info {
  border: 1px solid #dcdcdc;
  background-color: #fff;
  color: #f76812;
  &:focus {
    background-color: rgba(247, 104, 18, 0.05);
  }
  &:hover {
    border-color: #d1d1d1;
  }
  &.x-button_disabled {
    opacity: 0.5;
    cursor: default;
  }
}
.x-button_type-confirm {
  background-color: $main-color;
  color: #000;
  &:focus {
    background-color: #74E3B0;
  }
  &:hover {
    background-color: #7EE3BB;
  }
  &.x-button_disabled {
    background-color: #4e4e4e;
    cursor: default;
  }
}
.x-button_type-seed {
  background-color: rgba(249, 249, 249, 0.5);
  color: #4e4e4e;
  &:focus {
    background-color: #fff;
  }
  &:hover {
    background-color: #fff;
  }
  &.x-button_disabled {
    background-color: rgba(249, 249, 249, 0.9);
    cursor: default;
  }
}

.x-button_type-danger {
  background-color: #f02828;
  color: #fff;
  &:hover {
    background-color: #ff3333;
  }
  &:active {
    background-color: #d61f1f;
  }
  &.x-button_disabled {
    opacity: 0.5;
    cursor: default;
  }
}

.x-button_type-cancel {
  background-color: #fff;
  color: #b1b1b1;
  border: 1px solid #dcdcdc;
  &:hover {
    border-color: #f0f0f0;
  }
  &:active {
    border-color: #c7c7c7;
  }
  &.x-button_disabled {
    opacity: 0.5;
    cursor: default;
  }
}

.x-button_type-white {
  background-color: #fff;
  color: #ff7c2d;
  &:hover {
    transform: scale(1.01);
  }
  &:active {
    transform: scale(0.99);
  }
  &.x-button_disabled {
    opacity: 0.5;
    cursor: default;
  }
}

.x-button_type-white-border {
  background-color: transparent;
  color: #fff;
  border: 1px solid #fff;
  &:hover {
    transform: scale(1.01);
  }
  &:active {
    transform: scale(0.99);
  }
  &.x-button_disabled {
    opacity: 0.5;
    cursor: default;
  }
}
</style>
