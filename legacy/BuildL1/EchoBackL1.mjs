import{createServer as t}from"node:http";import{Readable as e}from"node:stream";import s from"node:crypto";import{writeFile as r}from"node:fs";const o=(t,e)=>null!=t&&(void 0===e||t instanceof e),n=t=>"function"==typeof t,i=t=>"string"==typeof t,A=t=>"number"==typeof t&&!isNaN(t),a=(m=A,t=>m(t)&&t===Math.round(t));var m;const M=(l=a,t=>l(t)&&t>=0);var l;const g=new Map([[1,"传参错误"],[2,"目录不存在"],[3,"实例无效"],[4,"方法未实现"]]),u=(t=1,e=g.get(t)??"",s=!1)=>{const r=new Error(e,t);if(!s)throw r;return r},c=Buffer.from("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","base64"),d=new Map,p=new Map,y=['                                                 <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">                <html><head>                                                        <title>404 Not Found</title>                                        </head><body>                                                       <h1>Not Found</h1>                                                  <p>The requested URL '," was not found on this server.</p>         </body></html>"],V=['                                                         <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">                <html><head>                                                        <title>500 Internal Error</title>                                   </head><body>                                                       <h1>Internal Error</h1>                                             <p>Server error:',".</p>                                           </body></html>"],w=(t,e,s)=>{e.statusCode=500,e.setHeader("Content-Type","text/html; charset=utf-8"),e.write(V[0]+s+V[1]),e.end()},D=(t,e)=>{e.statusCode=404,e.setHeader("Content-Type","text/html; charset=utf-8"),e.write(y[0]+t.url+y[1]),e.end()};class z extends Date{Format(t){i(t)||u();const e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()},s=/(y+)/.exec(t);let r,o;for(r in s&&(t=t.replace(s[1],String(this.getFullYear()).substring(4-s[1].length))),e)o=new RegExp("("+r+")").exec(t),o&&(t=t.replace(o[1],1==o[1].length?String(e[r]):("00"+e[r]).substring(String(e[r]).length)));return t}FromStr(t){let e;i(t)||u();const s=t.split(" ");return s[0]?(e=s[0].split("-"),this.setFullYear(Number(e[0])),this.setMonth(Number(e[1])-1),this.setDate(Number(e[2]))):(this.setFullYear(1970),this.setMonth(0),this.setDate(1)),s[1]?(e=s[1].split(":"),this.setHours(Number(e[0])),this.setMinutes(Number(e[1])),this.setSeconds(Number(e[2]))):(this.setHours(0),this.setMinutes(0),this.setSeconds(0)),isNaN(this.getFullYear())&&this.setTime(0),this}GetTSSec(){const t=this.GetTS().toString();return parseInt(t.substring(0,t.length-3))}SetTSSec(t){return A(t)||u(),this.SetTS(Number(String(t)+"000")),this}GetTS(){return this.valueOf()}SetTS(t){return A(t)||u(),this.setTime(t),this}Now(){return this.setTime(Date.now()),this}get utc(){return this._utc}set utc(t){(t=>"boolean"==typeof t)(t)||u(),this._utc=t;const e=Date.prototype;this._utc?(this.getFullYear=e.getUTCFullYear,this.getMonth=e.getUTCMonth,this.getDate=e.getUTCDate,this.getHours=e.getUTCHours,this.getMinutes=e.getUTCMinutes,this.getSeconds=e.getUTCSeconds,this.getMilliseconds=e.getUTCMilliseconds):(this.getFullYear=e.getFullYear,this.getMonth=e.getMonth,this.getDate=e.getDate,this.getHours=e.getHours,this.getMinutes=e.getMinutes,this.getSeconds=e.getSeconds,this.getMilliseconds=e.getMilliseconds)}}class T{constructor(t,e=10,s=500){o(t,z)&&a(e)&&A(s)||u(),this._date=t,this._noisyTolMax=e,this._dtMin=s,this._noisyTol=0,this._lastTime=0}Beat(){const t=this._date.Now().GetTS();return t-this._lastTime>=this._dtMin?(this._noisyTol=0,this._lastTime=t,!0):++this._noisyTol<=this._noisyTolMax}}const C=(t,e,r="base64")=>{"string"!=typeof t&&(t=JSON.stringify(t));const o=[],n=s.scryptSync(e,"salt",16),i=s.createCipheriv("aes-128-cbc",n,n);return i.setAutoPadding(!0),o.push(i.update(t,"utf8",r)),o.push(i.final(r)),o.join("")},Z="yyyy-MM-dd hh:mm:ss.S";class S{constructor(t,e,s=new z){i(t)&&o(e,_)&&o(s,z)||u(),this._name=t,this._parent=e,this._date=s,this._beatInfo=new T(s,10),this._beatError=new T(s,50)}Info(t,e=0,s=0){return this._beatInfo.Beat()&&this._parent._Info(`[${this._date.Format(Z)}] - Info - ${this._name} - ${t}`,e,this._date.GetTS(),this._name,t,s),this}Error(t,e=100,s=0){return this._beatError.Beat()&&this._parent._Error(`[${this._date.Format(Z)}] - Error - ${this._name} - ${t}`,e,this._date.GetTS(),this._name,t,s),this}get name(){return this._name}set name(t){i(t)||u(),this._name=t}}S.prototype.__otype="Logger";class _{constructor(t=0,e="./log",s="yyyy-MM-dd",r=void 0,o=()=>{},n=()=>{},i=0){a(t)||u(),this._date=new z,this._showLevel=t,this._logDir=e,this._logNameDateFormat=s,this._encryptKey=r,this._onInfo=o,this._onError=n,this._mode=i}Neo(t="Anonymous"){return i(t)||u(),new S(t,this,this._date)}_Info(t,e=0,s=0,o="",n="",A=0){return e<this._showLevel||(this._onInfo(t,e,s,o,n,A),i(this._encryptKey)?t=C(t,this._encryptKey):0===this._mode&&console.log(t),0!==this._mode&&0===n.length?r(this._LogFile(0),"",{flag:"w"},(t=>{})):r(this._LogFile(0),t+"\n",{flag:"a"},(t=>{}))),this}_Error(t,e=100,s=0,o="",n="",A=0){return e<this._showLevel||(this._onError(t,e,s,o,n,A),i(this._encryptKey)?t=C(t,this._encryptKey):0===this._mode&&console.error(t),0!==this._mode&&0===n.length?r(this._LogFile(0),"",{flag:"w"},(t=>{})):r(this._LogFile(1),t+"\n",{flag:"a"},(t=>{}))),this}_LogFile(t=0){return this._logDir+"/"+(0===this._mode?this._date.Format(this._logNameDateFormat):this._logNameDateFormat)+".log"}}class L{constructor(t){n(t)||u(),this._type=1,this._call=t}get type(){return L._type}get Call(){return this._call}}class Y{constructor(t){n(t)||u(),this._call=t,this._type=2}get type(){return Y._type}get Call(){return this._call}}const E=new L(((t,e,s)=>new Promise(((t,s)=>{const r=new z;e.setHeader("Content-Type","text/html; charset=utf-8"),e.write(r.Format("yyyy-MM-dd hh:mm:ss")),t(200)}))));d.set("/time",E);const G=new Y((async(t,e,s,r)=>{e.setHeader("Content-Type","application/json");const o={success:!0,code:200,msg:null,result:null},n=JSON.parse(s.toString("utf-8"));return r.Info("客户端上传UID: "+n),console.log("uid/muid",n.uid,n.muid),o.result=!0,e.write(JSON.stringify(o)),o.code}));p.set("/userinfo/upload",G);const P=s=>{const r=new _(s.logLevel,s.dirname);((s,r)=>{o(s)&&i(s.host)&&M(s.port)||u();const n=t();n.on("request",((t,s)=>{const n=t.url??"",i=n.split("?")[0];var A;r.Info(`Request URL: ${n}`),"GET"===t.method?"/favicon.ico"===n?(s.statusCode=200,s.setHeader("Content-Type","x-icon"),s.write(c),s.end()):d.has(i)?d.get(i).Call(t,s,r).then((t=>{s.statusCode=t,s.end(),r.Info(`Response URL: ${n} : GET-${t}`)})).catch((t=>{w(0,s,t),r.Error(`Response URL: ${n} : GET-ROUTE-500 : ${t}`)})):(D(t,s),r.Info(`Response URL: ${n} : GET-404`,1)):"POST"===t.method&&(p.has(i)?(A=t,o(A,e)||u(),new Promise(((t,e)=>{const s=[];A.on("end",(()=>t(Buffer.concat(s)))),A.on("error",e),A.on("data",(t=>s.push(t)))}))).then((e=>{p.get(i).Call(t,s,e,r).then((t=>{s.statusCode=t,s.end(),r.Info(`Response URL: ${n} : POST-${t}`)})).catch((t=>{w(0,s,t),r.Error(`Response URL: ${n} : POST-ROUTE-500 : ${t}`)}))})).catch((t=>{w(0,s,t),r.Error(`Response URL: ${n} : POST-S2B-500 : ${t}`)})):(D(t,s),r.Info(`Response URL: ${n} : POST-404`,1)))})),n.listen(s.port,s.host,(()=>{r.Info(`服务启动于: http://${s.host}:${s.port}/`,10)}))})({host:s.host,port:s.port},r.Neo("HttpAPI"))};export{P as Main,P as default};
