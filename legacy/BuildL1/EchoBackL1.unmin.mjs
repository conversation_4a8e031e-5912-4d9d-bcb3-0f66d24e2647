import { createServer } from 'node:http';
import { Readable } from 'node:stream';
import crypto from 'node:crypto';
import { writeFile } from 'node:fs';

const Defined = (object, type) => (undefined !== object && null !== object && (undefined === type || object instanceof type));
const DefinedTS = (cclass, type) => (cclass.prototype.__otype = type, void 0);
const IsBool = (v) => (typeof v === "boolean");
const IsFunc = (v) => (typeof v === "function");
const IsString = (v) => (typeof v === "string");
const IsNum = (v) => (typeof v === "number" && !isNaN(v));
const IsNumI = (_IsNum => {
    return (v) => (_IsNum(v) && v === Math.round(v));
})(IsNum);
const IsNumUI = (_IsNumI => {
    return (v) => (_IsNumI(v) && v >= 0);
})(IsNumI);

const _errStrMap = new Map([
    [1, "传参错误"],
    [2, "目录不存在"],
    [3, "实例无效"],
    [4, "方法未实现"]
]);

// 打印栈
const _PrintStack = (code = 1, msg = _errStrMap.get(code) ?? "", onlyRet = false) => {
    const errObj = new Error(msg, code);
    if(!onlyRet) throw errObj;
    return errObj;
};

const Stream2Buffer = (s) => {
    if(!Defined(s, Readable)) _PrintStack();
    return new Promise((resolve, reject) => {
        const buffers = [];
        s.on("end", () => resolve(Buffer.concat(buffers)));
        s.on("error", reject);
        s.on("data", (chunk) => buffers.push(chunk));
    });
};

const favicon = Buffer.from("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", "base64");

const scopeGet = new Map();
const scopePost = new Map();

const H404 = ["                                             \
    <!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">            \
    <html><head>                                                    \
    <title>404 Not Found</title>                                    \
    </head><body>                                                   \
    <h1>Not Found</h1>                                              \
    <p>The requested URL ", " was not found on this server.</p>     \
    </body></html>"];

const H500 = ["                                                     \
    <!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">            \
    <html><head>                                                    \
    <title>500 Internal Error</title>                               \
    </head><body>                                                   \
    <h1>Internal Error</h1>                                         \
    <p>Server error:", ".</p>                                       \
    </body></html>"];

const _500 = (request, response, e) => {
    response.statusCode = 500;
    response.setHeader("Content-Type", "text/html; charset=utf-8");
    response.write(H500[0] + e + H500[1]);
    response.end();
};

const _404 = (request, response) => {
    response.statusCode = 404;
    response.setHeader("Content-Type", "text/html; charset=utf-8");
    response.write(H404[0] + request.url + H404[1]);
    response.end();
};

const HttpAPIStart = (param, logger) => {
    if(!Defined(param) || !IsString(param.host) || !IsNumUI(param.port)) _PrintStack();
    const server = createServer();
    server.on("request", (request, response) => {
        const url = request.url ?? "";
        const routeUrl = url.split('?')[0];
        logger.Info(`Request URL: ${url}`);
        if("GET" === request.method) {
            if("/favicon.ico" === url) {
                response.statusCode = 200;
                response.setHeader("Content-Type", "x-icon");
                response.write(favicon);
                response.end();
            }
            else if(scopeGet.has(routeUrl)) {
                scopeGet.get(routeUrl).Call(request, response, logger).then(code => {
                    response.statusCode = code;
                    response.end();
                    logger.Info(`Response URL: ${url} : GET-${code}`);
                }).catch(e => {
                    _500(request, response, e);
                    logger.Error(`Response URL: ${url} : GET-ROUTE-500 : ${e}`);
                });
            }
            else {
                _404(request, response);
                logger.Info(`Response URL: ${url} : GET-404`, 1);
            }
        }
        else if("POST" === request.method) {
            if(scopePost.has(routeUrl)) {
                Stream2Buffer(request).then(buffer => {
                    scopePost.get(routeUrl).Call(request, response, buffer, logger).then(code => {
                        response.statusCode = code;
                        response.end();
                        logger.Info(`Response URL: ${url} : POST-${code}`);
                    }).catch(e => {
                        _500(request, response, e);
                        logger.Error(`Response URL: ${url} : POST-ROUTE-500 : ${e}`);
                    });
                }).catch(e => {
                    _500(request, response, e);
                    logger.Error(`Response URL: ${url} : POST-S2B-500 : ${e}`);
                });
            }
            else {
                _404(request, response);
                logger.Info(`Response URL: ${url} : POST-404`, 1);
            }
        }
        return;
    });
    server.listen(param.port, param.host, () => {
        logger.Info(`服务启动于: http://${param.host}:${param.port}/`, 10);
    });
    return server;
};

class DateEx extends Date {
    Format(fmt) {
        if(!IsString(fmt)) _PrintStack();
        const o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        const y = /(y+)/.exec(fmt);
        if(y) fmt = fmt.replace(y[1], String(this.getFullYear()).substring(4 - y[1].length));
        let k, rk;
        for(k in o) {
            rk = new RegExp("(" + k + ")").exec(fmt);
            if(rk) fmt = fmt.replace(rk[1], (rk[1].length == 1) ? String(o[k]) : (("00" + o[k]).substring(String(o[k]).length)));
        }
        return fmt;
    }
    FromStr(str) {
        if(!IsString(str)) _PrintStack();
        let c;
        const dateTime = str.split(' ');
        if(dateTime[0]) {
            c = dateTime[0].split('-');
            this.setFullYear(Number(c[0]));
            this.setMonth(Number(c[1]) - 1);
            this.setDate(Number(c[2]));
        }
        else {
            this.setFullYear(1970);
            this.setMonth(0);
            this.setDate(1);
        }
        if(dateTime[1]) {
            c = dateTime[1].split(':');
            this.setHours(Number(c[0]));
            this.setMinutes(Number(c[1]));
            this.setSeconds(Number(c[2]));
        }
        else {
            this.setHours(0);
            this.setMinutes(0);
            this.setSeconds(0);
        }
        if(isNaN(this.getFullYear())) this.setTime(0);
        return this;
    }
    GetTSSec() {
        const s = this.GetTS().toString();
        return parseInt(s.substring(0, s.length - 3));
    }
    SetTSSec(t) {
        if(!IsNum(t)) _PrintStack();
        this.SetTS(Number(String(t) + "000"));
        return this;
    }
    GetTS() {
        return this.valueOf();
    }
    SetTS(t) {
        if(!IsNum(t)) _PrintStack();
        this.setTime(t);
        return this;
    }
    Now() {
        this.setTime(Date.now());
        return this;
    }
    get utc() {return this._utc;}
    set utc(v) {
        if(!IsBool(v)) _PrintStack();
        this._utc = v;
        const p = Date.prototype;
        if(this._utc) {
            this.getFullYear = p.getUTCFullYear;
            this.getMonth = p.getUTCMonth;
            this.getDate = p.getUTCDate;
            this.getHours = p.getUTCHours;
            this.getMinutes = p.getUTCMinutes;
            this.getSeconds = p.getUTCSeconds;
            this.getMilliseconds = p.getUTCMilliseconds;
        }
        else {
            this.getFullYear = p.getFullYear;
            this.getMonth = p.getMonth;
            this.getDate = p.getDate;
            this.getHours = p.getHours;
            this.getMinutes = p.getMinutes;
            this.getSeconds = p.getSeconds;
            this.getMilliseconds = p.getMilliseconds;
        }
    }
}

class Beat {
    constructor(date, noisyTolMax = 10, dtMin = 500) {
        if(!Defined(date, DateEx) || !IsNumI(noisyTolMax) || !IsNum(dtMin)) _PrintStack();
        this._date = date;
        this._noisyTolMax = noisyTolMax;
        this._dtMin = dtMin;
        this._noisyTol = 0;
        this._lastTime = 0;
    }
    Beat() {
        const now = this._date.Now().GetTS();
        const dt = now - this._lastTime;
        if(dt >= this._dtMin) {this._noisyTol = 0; this._lastTime = now; return true;}
        return (++ this._noisyTol <= this._noisyTolMax);
    }
}

/** aes 加密 */
const Encrypt = (data, passKey, outputEncoding = "base64") => {
    if (typeof data !== "string") data = JSON.stringify(data);
    const cipherChunks = [];
    // const key = Buffer.from(passKey, 'utf8');
    // 对原始秘钥点加盐
    const key = crypto.scryptSync(passKey, "salt", 16);
    const iv = key; // Buffer.alloc(16, 0);
    const cipher = crypto.createCipheriv("aes-128-cbc", key, iv);
 
    cipher.setAutoPadding(true);
    cipherChunks.push(cipher.update(data, "utf8", outputEncoding));
    cipherChunks.push(cipher.final(outputEncoding));
 
    return cipherChunks.join("");
};

const _dateFormat = "yyyy-MM-dd hh:mm:ss.S";

class Logger {
    constructor(name, parent, date = new DateEx()) {
        if(!IsString(name) || !Defined(parent, LoggerFactory) || !Defined(date, DateEx)) _PrintStack();
        this._name = name;
        this._parent = parent;
        this._date = date;
        this._beatInfo = new Beat(date, 10);
        this._beatError = new Beat(date, 50);
    }
    Info(text, level = 0, flag = 0) {
        if(this._beatInfo.Beat()) {
            this._parent._Info(`[${this._date.Format(_dateFormat)}] - Info - ${this._name} - ${text}`, level, this._date.GetTS(), this._name, text, flag);
        }
        return this;
    }
    Error(text, level = 100, flag = 0) {
        if(this._beatError.Beat()) {
            this._parent._Error(`[${this._date.Format(_dateFormat)}] - Error - ${this._name} - ${text}`, level, this._date.GetTS(), this._name, text, flag);
        }
        return this;
    }
    get name() {return this._name;}
    set name(v) {if(!IsString(v)) _PrintStack(); this._name = v;}
}
DefinedTS(Logger, "Logger");

class LoggerFactory {
    constructor(showLevel = 0, logDir = "./log", logNameDateFormat = "yyyy-MM-dd", encryptKey = undefined, onInfo = () => {}, onError = () => {}, mode = 0) {
        if(!IsNumI(showLevel)) _PrintStack();
        this._date = new DateEx();
        //this._date.utc = true;  /* Uncomment if NEED UTC */
        this._showLevel = showLevel;
        this._logDir = logDir;
        this._logNameDateFormat = logNameDateFormat;
        this._encryptKey = encryptKey;
        this._onInfo = onInfo;
        this._onError = onError;
        this._mode = mode;
    }
    Neo(name = "Anonymous") {
        if(!IsString(name)) _PrintStack();
        return new Logger(name, this, this._date);
    }
    _Info(text, level = 0, t = 0, name = "", textRaw = "", flag = 0) {
        if(level < this._showLevel) return this;
        this._onInfo(text, level, t, name, textRaw, flag);
        if(IsString(this._encryptKey)) text = Encrypt(text, this._encryptKey);
        else if(0 === this._mode) console.log(text);
        if(0 !== this._mode && 0 === textRaw.length) writeFile(this._LogFile(0), "", {flag: "w"}, (err) => {});
        else writeFile(this._LogFile(0), text + "\n", {flag: "a"}, (err) => {});
        return this;
    }
    _Error(text, level = 100, t = 0, name = "", textRaw = "", flag = 0) {
        if(level < this._showLevel) return this;
        this._onError(text, level, t, name, textRaw, flag);
        if(IsString(this._encryptKey)) text = Encrypt(text, this._encryptKey);
        else if(0 === this._mode) console.error(text);
        if(0 !== this._mode && 0 === textRaw.length) writeFile(this._LogFile(0), "", {flag: "w"}, (err) => {});
        else writeFile(this._LogFile(1), text + "\n", {flag: "a"}, (err) => {});
        return this;
    }
    _LogFile(t = 0) {
        return this._logDir + "/" + (0 === this._mode ? this._date.Format(this._logNameDateFormat) : this._logNameDateFormat) + ".log";
    }
}

// import {IncomingMessage, ServerResponse} from "node:http";

const APIRouterType = {
    GET: 1,
    POST: 2
};

class APIRouterGet {
    // protected _call: (request: IncomingMessage, response: ServerResponse, logger: ILogger) => Promise<GLint>;
    constructor(call) {
        if(!IsFunc(call)) _PrintStack();
        this._type = APIRouterType.GET;
        this._call = call;
    }
    get type() {return APIRouterGet._type;}
    get Call() {return this._call;}
}

class APIRouterPost {
    // protected _call: (request: IncomingMessage, response: ServerResponse, requestData: Buffer, logger: ILogger) => Promise<GLint>;
    constructor(call) {
        if(!IsFunc(call)) _PrintStack();
        this._call = call;
        this._type = APIRouterType.POST;
    }
    get type() {return APIRouterPost._type;}
    get Call() {return this._call;}
}

// 测试服务(返回时间)
const f$1 = (request, response, logger) => {
    return new Promise((resolve, reject) => {
        const c = new DateEx();
        response.setHeader("Content-Type", "text/html; charset=utf-8");
        response.write(c.Format("yyyy-MM-dd hh:mm:ss"));
        resolve(200);
    });
};

const executor$1 = new APIRouterGet(f$1);
scopeGet.set("/time", executor$1);

// 列出所有数据源
const f = async (request, response, requestData, logger) => {
    response.setHeader("Content-Type", "application/json");
    const responseJson = {
        success: true,
        code: 200,
        msg: null,
        result: null
    };
    const requestJson = JSON.parse(requestData.toString("utf-8"));
    logger.Info("客户端上传UID: " + requestJson);
    // TODO
    console.log("uid/muid", requestJson.uid, requestJson.muid);
    responseJson.result = true;
    response.write(JSON.stringify(responseJson));
    return responseJson.code;
};

const executor = new APIRouterPost(f);
scopePost.set("/userinfo/upload", executor);

const Main = (config) => {
    const LoggerFactory$1 = new LoggerFactory(config.logLevel, config.dirname);
    HttpAPIStart({host: config.host, port: config.port}, LoggerFactory$1.Neo("HttpAPI"));
    // TODO 异常捕捉
    // const LoggerMain = LoggerFactory.Neo("Main");
    // process.on("uncaughtException", err => {
    //     LoggerMain.Error("uncaughtException: " + err);
    // });
    // throw new Error("Test Error"); // 测试异常捕捉
};

export { Main, Main as default };
