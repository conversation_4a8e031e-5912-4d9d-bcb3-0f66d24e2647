'use strict';
import {fileURLToPath} from "node:url";
import path from "node:path";
import {exec} from "node:child_process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const config = {
    // host: "**********",
    host: "0.0.0.0",
    port: 11121,
    logLevel: 10,
    dirname: __dirname
};

const _iptester = /[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/;

const ConfigFn = () => {
    return new Promise((resolve, reject) => {
        exec("hostname -I", (error, stdout, stderr) => {
            if(error) {
                console.error(`exec error: ${stderr}`);
                return resolve(config);
            }
            if(stderr) {
                console.error(`stderr: ${stderr}`);
                return resolve(config);
            }
            const ip = stdout.trim().split(" ")[0];
            if(!_iptester.test(ip)) {
                console.error("IP地址不合法: " + ip);
                return resolve(config);
            }
            config.host = ip;
            resolve(config);
        });
    });
};

export {ConfigFn as Config};
export default ConfigFn;