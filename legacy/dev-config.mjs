/**
 * 开发环境配置文件
 * 用于在不同平台上运行 EchoWave Windows Client
 */

export const DEV_CONFIG = {
    // 当前运行平台
    platform: process.platform,
    
    // 是否为开发模式
    isDevelopment: process.env.NODE_ENV === 'development' || process.env.NODE_ENV !== 'production',
    
    // 平台特定配置
    platformConfig: {
        win32: {
            supportedFeatures: ['wsl', 'hyperv', 'virtualization', 'mirror'],
            mockMode: false
        },
        darwin: {
            supportedFeatures: [],
            mockMode: true,
            mockResponses: {
                checkVirtual: true,
                checkWSL: 0, // 未安装
                checkMirror: false,
                checkOS: false, // macOS 不符合 Windows 要求
                checkPowerShell: false
            }
        },
        linux: {
            supportedFeatures: [],
            mockMode: true,
            mockResponses: {
                checkVirtual: true,
                checkWSL: 0, // 未安装
                checkMirror: false,
                checkOS: false, // Linux 不符合 Windows 要求
                checkPowerShell: false
            }
        }
    },
    
    // 获取当前平台配置
    getCurrentPlatformConfig() {
        return this.platformConfig[this.platform] || this.platformConfig.linux;
    },
    
    // 检查是否应该使用 mock 模式
    shouldUseMockMode() {
        const config = this.getCurrentPlatformConfig();
        return config.mockMode || false;
    },
    
    // 获取 mock 响应
    getMockResponse(feature) {
        const config = this.getCurrentPlatformConfig();
        return config.mockResponses?.[feature];
    },
    
    // 检查功能是否支持
    isFeatureSupported(feature) {
        const config = this.getCurrentPlatformConfig();
        return config.supportedFeatures.includes(feature);
    }
};

// 开发环境日志
export const devLog = (message, ...args) => {
    if (DEV_CONFIG.isDevelopment) {
        console.log(`[DEV-${DEV_CONFIG.platform.toUpperCase()}]`, message, ...args);
    }
};

// 开发环境警告
export const devWarn = (message, ...args) => {
    if (DEV_CONFIG.isDevelopment) {
        console.warn(`[DEV-WARN-${DEV_CONFIG.platform.toUpperCase()}]`, message, ...args);
    }
};

export default DEV_CONFIG;
