import {app, BrowserWindow} from "electron";
import {fileURLToPath} from "node:url";
import path from "node:path";
import InitAPI from "./BackL0/InitAPI.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const __execname = path.dirname(process.execPath);

const config = {
    dirname: __execname,
    logLevel: 10,
    logEncryptKey: undefined,
    // logEncryptKey: "echo_wave",
    vport: 11121,
    ver: "0.10",
    wslVer: "2.4.13.0",
    wslCheckVer: "2.0.4",
    appName: "echowave_client",
    distroName: "EchoWaveUbuntu",
    productName: "EchoWave"
};

const createWindow = () => {
    const win = new BrowserWindow({
        width: 800,
        height: 600,
        webPreferences: {
            preload: path.join(__dirname + "/BackL0", "test_preload.js")
        }
    });
    win.loadFile("test.html");
    win.webContents.openDevTools();
};

app.whenReady().then(() => {
    InitAPI(config);
    createWindow();
    app.on("activate", () => {
        if(BrowserWindow.getAllWindows().length === 0) createWindow();
    });
});
