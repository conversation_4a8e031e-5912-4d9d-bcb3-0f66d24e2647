'use strict';
import crypto from "node:crypto";
 
/** aes 加密 */
const Encrypt = (data, passKey, outputEncoding = "base64") => {
    if (typeof data !== "string") data = JSON.stringify(data);
    const cipherChunks = [];
    // const key = Buffer.from(passKey, 'utf8');
    // 对原始秘钥点加盐
    const key = crypto.scryptSync(passKey, "salt", 16);
    const iv = key; // Buffer.alloc(16, 0);
    const cipher = crypto.createCipheriv("aes-128-cbc", key, iv);
 
    cipher.setAutoPadding(true);
    cipherChunks.push(cipher.update(data, "utf8", outputEncoding));
    cipherChunks.push(cipher.final(outputEncoding));
 
    return cipherChunks.join("");
};


/** aes 解密 */
const Decrypt = (data, passKey, inputEncoding = "base64") => {
    const cipherChunks = [];
    // const key = Buffer.from(passKey, 'utf8');
    const key = crypto.scryptSync(passKey, "salt", 16);
    const iv = key; // Buffer.alloc(16, 0);
    const decipher = crypto.createDecipheriv("aes-128-cbc", key, iv);
 
    decipher.setAutoPadding(true);
    cipherChunks.push(decipher.update(data, inputEncoding, "utf8"));
    cipherChunks.push(decipher.final("utf8"));
 
    return cipherChunks.join("");
};

// let o0 = Encrypt("jiumialskdjfpowiejrf;alksdjf;oiawe", "hahayo");
// console.log("e", o0);
// let o1 = Decrypt(o0, "hahayo");
// console.log("d", o1);

export {Encrypt, Decrypt};
export default Encrypt;