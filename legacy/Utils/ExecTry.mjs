'use strict';
import {ExecCoding} from "./ExecCoding.mjs";

const ExecTry = (cmds, callback, encoding) => {
    const _call = i => {
        ExecCoding(cmds[i], (error, stdout, stderr) => {
            if(error || stderr) {
                if(i < cmds.length - 1) _call(i + 1);
                else callback(error, stdout, stderr);
                return;
            }
            callback(error, stdout, stderr);
        }, encoding);
    };
    _call(0);
};

// ExecTry(["echo2 哈0", "echo 哈1", "echo 哈2"], (error, stdout, stderr) => {
//     if(error) console.error(`exec error: ${error}`);
//     if(stderr) console.error(`stderr: ${stderr}`);
//     console.log(`stdout: ${stdout}`);
// });

export {ExecTry};
export default ExecTry;