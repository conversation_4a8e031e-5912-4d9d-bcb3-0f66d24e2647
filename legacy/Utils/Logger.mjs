"use strict";
import { Defined, DefinedTS, IsString, IsNumI } from "./Defined.mjs";
import { _PrintStack } from "./PrintStack.mjs";
import { DateEx } from "./DateEx.mjs";
import { Beat } from "./Beat.mjs";
import { Encrypt } from "./AES.mjs";
import { writeFile } from "node:fs";
import { is } from "@electron-toolkit/utils";
import fs from "fs";

const _dateFormat = "yyyy-MM-dd hh:mm:ss.S";

class Logger {
  constructor(name, parent, date = new DateEx()) {
    if (
      !IsString(name) ||
      !Defined(parent, LoggerFactory) ||
      !Defined(date, DateEx)
    )
      _PrintStack();
    this._name = name;
    this._parent = parent;
    this._date = date;
    this._beatInfo = new Beat(date, 10);
    this._beatError = new Beat(date, 50);
  }
  Debug(text, level = 0, flag = 0) {
    if (this._beatInfo.Beat()) {
      this._parent._Info(
        `[${this._date.Format(_dateFormat)}] - Debug - ${this._name} - ${text}`,
        level,
        this._date.GetTS(),
        this._name,
        text,
        flag,
      );
    }
    return this;
  }
  Info(text, level = 0, flag = 0) {
    if (this._beatInfo.Beat()) {
      this._parent._Info(
        `[${this._date.Format(_dateFormat)}] - Info - ${this._name} - ${text}`,
        level,
        this._date.GetTS(),
        this._name,
        text,
        flag,
      );
    }
    return this;
  }
  Warn(text, level = 60, flag = 0) {
    if (this._beatInfo.Beat()) {
      this._parent._Info(
        `[${this._date.Format(_dateFormat)}] - Warn - ${this._name} - ${text}`,
        level,
        this._date.GetTS(),
        this._name,
        text,
        flag,
      );
    }
    return this;
  }
  Error(text, level = 100, flag = 0) {
    if (this._beatError.Beat()) {
      this._parent._Error(
        `[${this._date.Format(_dateFormat)}] - Error - ${this._name} - ${text}`,
        level,
        this._date.GetTS(),
        this._name,
        text,
        flag,
      );
    }
    return this;
  }
  Neo(name = "Anonymous") {
    if (!IsString(name)) _PrintStack();
    return new Logger(name, this._parent, this._date);
  }
  get name() {
    return this._name;
  }
  set name(v) {
    if (!IsString(v)) _PrintStack();
    this._name = v;
  }
}
DefinedTS(Logger, "Logger");

class LoggerFactory {
  constructor(
    showLevel = 0,
    logDir = "./log",
    logNameDateFormat = "yyyy-MM-dd",
    encryptKey = undefined,
    onInfo = () => {},
    onError = () => {},
    mode = 0,
  ) {
    if (!IsNumI(showLevel)) _PrintStack();
    this._date = new DateEx();
    //this._date.utc = true;  /* Uncomment if NEED UTC */
    this._showLevel = showLevel;
    this._logDir = logDir;
    if (!fs.existsSync(logDir)) {
      console.log("日志存放目录", logDir);
      fs.mkdirSync(logDir);
    }
    this._logNameDateFormat = logNameDateFormat;
    this._encryptKey = encryptKey;
    this._onInfo = onInfo;
    this._onError = onError;
    this._mode = mode;
  }
  Neo(name = "Anonymous") {
    if (!IsString(name)) _PrintStack();
    return new Logger(name, this, this._date);
  }
  _Info(text, level = 0, t = 0, name = "", textRaw = "", flag = 0) {
    if (level < this._showLevel) {
      console.log(text);
      writeFile(this._LogFile(0), text + "\n", { flag: "a" }, (err) => {});
      return this;
    }
    this._onInfo(text, level, t, name, textRaw, flag);
    if (IsString(this._encryptKey)) text = Encrypt(text, this._encryptKey);
    else if (0 === this._mode) console.log(text);
    if (0 !== this._mode && 0 === textRaw.length)
      writeFile(this._LogFile(0), "", { flag: "a" }, (err) => {});
    else writeFile(this._LogFile(0), text + "\n", { flag: "a" }, (err) => {});
    return this;
  }
  _Error(text, level = 100, t = 0, name = "", textRaw = "", flag = 0) {
    if (level < this._showLevel) {
      console.error(text);
      writeFile(this._LogFile(0), text + "\n", { flag: "a" }, (err) => {});
      return this;
    }
    this._onError(text, level, t, name, textRaw, flag);
    if (IsString(this._encryptKey)) text = Encrypt(text, this._encryptKey);
    else if (0 === this._mode) console.error(text);
    if (0 !== this._mode && 0 === textRaw.length)
      writeFile(this._LogFile(0), "", { flag: "a" }, (err) => {});
    else writeFile(this._LogFile(1), text + "\n", { flag: "a" }, (err) => {});
    return this;
  }
  _LogFile(t = 0) {
    return (
      this._logDir +
      "/" +
      (0 === this._mode
        ? this._date.Format(this._logNameDateFormat)
        : this._logNameDateFormat) +
      ".log"
    );
  }
}

export { LoggerFactory };
export default LoggerFactory;
