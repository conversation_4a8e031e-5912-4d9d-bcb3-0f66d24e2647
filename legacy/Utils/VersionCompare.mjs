'use strict';

const VersionCompare = (v1, v2) => {
    const v1a = v1.split(".");
    const v2a = v2.split(".");
    const len = Math.max(v1a.length, v2a.length);
    let c1, c2;
    for(let i = 0; i < len; ++ i) {
        c1 = parseInt(v1a[i] || "0", 10);
        c2 = parseInt(v2a[i] || "0", 10);
        if(c1 > c2) return 1;
        if(c1 < c2) return -1;
    }
    return 0;
};

// console.log(VersionCompare("1.2.3", "1.2.3")); // 0
// console.log(VersionCompare("1.2.3", "1.2.4")); // -1
// console.log(VersionCompare("1.2.4", "1.2.3")); // 1
// console.log(VersionCompare("1.2.3", "1.2"));   // 1
// console.log(VersionCompare("1.2", "1.2.3"));   // -1

export {VersionCompare};
export default VersionCompare;