'use strict';
import {Readable, Writable} from "node:stream";
import {Defined} from "./Defined.mjs";
import {_PrintStack} from "./PrintStack.mjs";

const Stream2Buffer = (s) => {
    if(!Defined(s, Readable)) _PrintStack();
    return new Promise((resolve, reject) => {
        const buffers = [];
        s.on("end", () => resolve(Buffer.concat(buffers)));
        s.on("error", reject);
        s.on("data", (chunk) => buffers.push(chunk));
    });
};

const Buffer2ReadStream = (b, s = new Readable()) => {
    if(!Defined(b, Buffer) || !Defined(s, Readable)) _PrintStack();
    s.push(b); s.push(null);
    return s;
};

const Buffer2WriteStream = (b, s = new Writable()) => {
    if(!Defined(b, Buffer) || !Defined(s, Writable)) _PrintStack();
    s.write(b); s.end();
    return s;
};

export {Stream2Buffer, Buffer2ReadStream, Buffer2WriteStream};
export default Stream2Buffer;