"use strict";
import axios from "axios";
import { createWriteStream } from "node:fs";
import { CheckMD5 } from "./CheckMD5.mjs";

const _EmptyFn = () => {};

const DownloadFile = (
  url,
  savePath,
  checkCode = "",
  handler = { Progress: _EmptyFn, Cancel: _EmptyFn },
  signal,
  logger,
) => {
  return new Promise((resolve, reject) => {
    const f = () => {
      axios
        .get(url, { responseType: "stream", signal: signal })
        .then((response) => {
          const writer = createWriteStream(savePath);
          writer.on("finish", resolve);
          writer.on("error", reject);
          response.data.pipe(writer); // 流式保存到本地‌
          const total = response.headers["content-length"];
          const chunkLen = [0];
          response.data.on("data", (chunk) => {
            chunkLen[0] += chunk.length;
          });
          handler.Progress = () => {
            return chunkLen[0] / total;
          };
          handler.Cancel = () => {
            response.data.close();
            response.data.destroy();
            writer.close();
            writer.destroy();
          };
        });
    };
    if (!checkCode.length) {
      f();
      return;
    }
    CheckMD5(savePath)
      .then((fileCode) => {
        if (fileCode.toLowerCase() === checkCode.toLowerCase()) {
          if (logger) {
            logger.Info("本地已存在，跳过下载");
          }
          handler.Progress = () => 1.0;
          handler.Cancel = _EmptyFn;
          resolve();
          return;
        }
        logger.Info(`本地已存在文件，但文件不完整，重新下载, received: ${fileCode.toLowerCase()}, excepted: ${checkCode.toLowerCase()}`);
        f();
      })
      .catch(f);
  });
};

export { DownloadFile };
export default DownloadFile;
