'use strict';
const Defined = (object, type) => (undefined !== object && null !== object && (undefined === type || object instanceof type));
const DefinedTS = (cclass, type) => (cclass.prototype.__otype = type, void 0);
const IsBool = (v) => (typeof v === "boolean");
const IsFunc = (v) => (typeof v === "function");
const IsString = (v) => (typeof v === "string");
const IsFileName = (_IsString => {
    const _NAME_REG = /^[A-Za-z0-9_.~!@#$%^&=+-]+$/;
    const _IsFileName = (v) => (_IsString(v) && _NAME_REG.test(v));
    return _IsFileName;
})(IsString);
const IsArray = (() => {
    const _ToString = Object.prototype.toString;
    const _IsArray = (v) => {
        const vStr = _ToString.call(v);
        return vStr === "[object Array]" || vStr === "[object Arguments]";
    };
    return _IsArray;
})();
const DefinedT = (_IsArray => {
    const _DefinedT = (object, type) => {
        if(undefined === object || null === object) return false;
        if(undefined === type) return true;
        let i, otype;
        while((object = Object.getPrototypeOf(object)) && object.__otype) {
            if(!_IsArray(object.__otype)) {
                if(object.__otype === type) return true;
            }
            else {
                otype = object.__otype;
                for(i = otype.length; i --;) {
                    if(otype[i] === type) return true;
                }
            }
        }
        return false;
    };
    return _DefinedT;
})(IsArray);
const IsNum = (v) => (typeof v === "number" && !isNaN(v));
const IsNumI = (_IsNum => {
    return (v) => (_IsNum(v) && v === Math.round(v));
})(IsNum);
const IsNumUI = (_IsNumI => {
    return (v) => (_IsNumI(v) && v >= 0);
})(IsNumI);
const IsGenArray = (_IsNum => {
    return (v, l = 0, f = _IsNum) => {
        if(v && _IsNum(v.length) && v.length >= l) {
            let i;
            for(i = v.length; i --;) {
                if(!f(v[i])) return false;
            }
            return true;
        }
        return false;
    }
})(IsNum);
const IsGenArrayEx = (_IsNum => {
    return (v, l = 0, p, f = _IsNum) => {
        if(v && _IsNum(v.length) && v.length >= l) {
            let i;
            for(i = v.length; i --;) {
                if(!f(v[i], p)) return false;
            }
            return true;
        }
        return false;
    }
})(IsNum);
const IsTypedArray = (_IsNum => {
    return (v) => (v && _IsNum(v.byteLength) && v.buffer && v.buffer.constructor === ArrayBuffer);
})(IsNum);
const IsGenMap = (_Defined => {
    return (v, f = _Defined) => {
        if(!_Defined(v, Map)) return false;
        const k = v.keys();
        let i;
        while(!(i = k.next()).done) {
            if(!f(v.get(i.value))) return false;
        }
        return true;
    };
})(Defined);
const IsGenMapEx = (_Defined => {
    return (v, p, f = _Defined) => {
        if(!_Defined(v, Map)) return false;
        const k = v.keys();
        let i;
        while(!(i = k.next()).done) {
            if(!f(v.get(i.value), p)) return false;
        }
        return true;
    };
})(Defined);
export {Defined, DefinedT, DefinedTS, IsBool, IsFunc, IsString, IsFileName, IsArray, IsNum, IsNumI, IsNumUI, IsGenArray, IsGenArrayEx, IsTypedArray, IsGenMap, IsGenMapEx};
export default Defined;