"use strict";
import { exec } from "node:child_process";
import iconv from "iconv-lite";
import vtd from "../externals/virt-detect";

const systemEncoding = (() => {
    try {
        // 只在Windows平台上使用原生模块
        if (process.platform === 'win32') {
            const encoding = vtd.getSystemEncoding();
            switch (encoding.oemCode) {
                case 936: // gb2312
                case 54936: // gb18030
                    return "gbk";
                case 65001:
                    return "utf-8";
                default:
                    return "utf-8";
            }
        } else {
            // 非Windows平台默认使用UTF-8
            return "utf-8";
        }
    } catch (e) {
        return "utf-8";
    }
})();

/**
 *
 * @param {string} cmd
 * @param callback
 * @param {?string}encoding
 * @param {?AbortSignal}signal
 * @constructor
 */
const ExecCoding = (cmd, callback, encoding = systemEncoding, signal) => {
    exec(cmd, { encoding: "buffer", signal }, (error, stdout, stderr) => {
        // ShowMsg(stdout, false);
        callback(
            error,
            stdout ? iconv.decode(stdout, encoding) : "",
            stderr ? iconv.decode(stderr, encoding) : "",
        );
    });
};

/**
 * @typedef {Object} Options
 * @property {?number} retry
 * @property {?number} interval
 * @property {?string} encoding
 * @property {?AbortSignal} signal
 * @property {(count: number, total: number, error: ExecException, stderr: string)=>void} onRetry
 */
/**
 *
 * @param {string} cmd
 * @param {(error: ExecException | number, stdout: string, stderr: string)=>void}callback
 * @param {Options} options
 * @constructor
 */
const ExecCodingRetry = (
    cmd,
    callback,
    options = {
        retry: 5,
        interval: 1000,
        encoding: systemEncoding,
    },
) => {
    let count = 0;
    let timer = undefined;
    if (options.signal) {
        options.signal.addEventListener("abort", () => {
            if (timer) {
                clearTimeout(timer);
            }
            timer = undefined;
        });
    }
    const f = () => {
        ExecCoding(
            cmd,
            (error, stdout, stderr) => {
                if (options.signal?.aborted) {
                    return;
                }
                if ((error || stderr) && count < options.retry) {
                    if (count > 0) {
                        options?.onRetry(count, options.retry, error, stderr);
                    }
                    count++;
                    // console.log(`Retrying... (${count}/${retry})`);
                    timer = setTimeout(f, options.interval);
                } else callback(error, stdout, stderr);
            },
            options.encoding,
            options.signal,
        );
    };
    f();
};

export { ExecCoding, ExecCodingRetry };
export default ExecCoding;
