'use strict';
import {IsString} from "./Defined.mjs";
import {_PrintStack} from "./PrintStack.mjs";

const HttpGet = function(url) {
    if(!IsString(url)) _PrintStack();
    return new Promise((resolve, reject) => {
        const xmlObj = new XMLHttpRequest();
        xmlObj.open("GET", url, true);

        // xmlObj.setRequestHeader("Origin", STATIC_PATH);
        // xmlObj.setRequestHeader("Access-Control-Allow-Origin", "*");
        // xmlObj.setRequestHeader('Access-Control-Allow-Methods', '*');
        // xmlObj.setRequestHeader('Access-Control-Allow-Headers', 'x-requested-with,content-type');

        xmlObj.onreadystatechange = () => {
            if(4 == xmlObj.readyState) {
                // xmlObj.responseText
                if(200 == xmlObj.status) resolve(xmlObj);
                else reject(xmlObj);
            }
        };

        xmlObj.send(null);
    });
};

const HttpGetBin = function(url) {
    if(!IsString(url)) _PrintStack();
    return new Promise((resolve, reject) => {
        const xmlObj = new XMLHttpRequest();
        xmlObj.responseType = "arraybuffer";
        xmlObj.open("GET", url, true);

        xmlObj.onreadystatechange = () => {
            if(4 == xmlObj.readyState) {
                if(200 == xmlObj.status) resolve(xmlObj.response);
                else reject(xmlObj);
            }
        };

        xmlObj.send(null);
    });
};

const HttpGetStr = function(url) {
    if(!IsString(url)) _PrintStack();
    return new Promise((resolve, reject) => {
        const xmlObj = new XMLHttpRequest();
        xmlObj.responseType = "text";
        xmlObj.open("GET", url, true);

        xmlObj.onreadystatechange = () => {
            if(4 == xmlObj.readyState) {
                if(200 == xmlObj.status) resolve(xmlObj.response);
                else reject(xmlObj);
            }
        };

        xmlObj.send(null);
    });
};

const HttpGetJson = function(url) {
    if(!IsString(url)) _PrintStack();
    return new Promise((resolve, reject) => {
        const xmlObj = new XMLHttpRequest();
        xmlObj.responseType = "json";
        xmlObj.open("GET", url, true);

        xmlObj.onreadystatechange = () => {
            if(4 == xmlObj.readyState) {
                if(200 == xmlObj.status) resolve(xmlObj.response);
                else reject(xmlObj);
            }
        };

        xmlObj.send(null);
    });
};

export {HttpGet, HttpGetBin, HttpGetStr, HttpGetJson};
export default HttpGet;