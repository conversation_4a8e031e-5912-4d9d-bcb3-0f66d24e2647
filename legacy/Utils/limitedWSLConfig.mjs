import path from "node:path";
import fs from "node:fs";
import fsp from "node:fs/promises";
import child_process from "node:child_process";
import os from "node:os";

const banner = "# Generated By EchoWave";

// 获取 CPU 和内存信息
const getSystemInfo = () => {
  // CPU 信息
  const cpus = os.cpus();
  const cpuCount = cpus.length; // CPU 核心数
  const cpuModel = cpus[0].model; // CPU 型号
  // 内存信息（转换为 GB）
  const totalMemory = os.totalmem() / (1024 * 1024 * 1024); // 总内存（GB）
  const freeMemory = os.freemem() / (1024 * 1024 * 1024); // 可用内存（GB）
  return {
    cpu: {
      count: cpuCount,
      model: cpuModel,
    },
    memory: {
      total: totalMemory.toFixed(2), // 保留 2 位小数
      free: freeMemory.toFixed(2),
    },
  };
};

const loadWslConfig = (logger) => {
  const systemInfo = getSystemInfo();
  const processors = Math.max(
    Math.floor(systemInfo.cpu.count * 0.7),
    2,
  ).toString();
  const memory =
    Math.max(Math.floor(systemInfo.memory.total * 0.8), 1).toString() + "GB";
  logger.Info(`processors=${processors} memory=${memory}GB`);
  return [
    [
      banner,
      "[wsl2]",
      `processors=${processors}`, // 最少两核
      `memory=${memory}`, // 最小 1 GB
      `swap=8GB`,
    ].join("\n"),
    {
      processors: processors,
      memory: memory,
      swap: "8GB",
    },
  ];
};
const getHomeDir = () => {
  return os.homedir();
};
const getWslConfigPath = () => {
  return path.join(getHomeDir(), ".wslconfig");
};
const getWslConfigBackupPath = () => {
  return path.join(getHomeDir(), ".wslconfig.bak.6563686f77617665");
};
const backupUserWslConfigFile = async (logger) => {
  const src = getWslConfigPath();
  const dst = getWslConfigBackupPath();
  if (!fs.existsSync(src)) return;
  if (fs.existsSync(dst)) return;
  const content = await fsp.readFile(src, { encoding: "utf-8" });
  if (content.startsWith(banner)) {
    logger.Info("找到遗留配置文件");
    return;
  }
  logger.Info("存在用户配置文件，开始备份");
  await fsp.rename(src, dst);
};
const restoreUserWslConfigFile = (logger) => {
  const src = getWslConfigBackupPath();
  const dst = getWslConfigPath();
  if (!fs.existsSync(dst)) {
    logger.Error("意外错误，没有找到 WSL 配置文件");
    return false;
  }
  if (!fs.existsSync(src)) {
    logger.Info("没有用户配置文件，保留配置文件供下次使用");
    return false;
  }
  logger.Info("移除 WSL 配置文件");
  fs.unlinkSync(dst);

  logger.Info("还原用户配置文件");
  fs.renameSync(src, dst);
  return true;
};
export const setLimitedWSLConfig = async (logger) => {
  const [content, config] = loadWslConfig(logger);
  const src = getWslConfigPath();
  try {
    if (fs.existsSync(src)) {
      logger.Info("存在 WSL 配置文件，开始比较");
      const _content = await fsp.readFile(src, { encoding: "utf-8" });
      const matches = _content
        .split("\n")
        .map((line) => {
          const parts = line.trim().split("=");
          return [parts[0].trim(), parts[1]?.trim() || ""];
        })
        .filter(([k, v]) => config[k] === v);
      //console.log(matches, config);
      if (matches.length === Reflect.ownKeys(config).length) {
        logger.Info("设置成功");
        return;
      }
    }
    await backupUserWslConfigFile(logger);
    await fsp.writeFile(src, content, { encoding: "utf-8" });
    await new Promise((resolve, reject) => {
      logger.Info("重启 WSL");
      child_process.exec("wsl --shutdown", (err) => {
        if (err) {
          logger.Error("设置失败，" + err);
          resolve();
        } else {
          logger.Info("设置成功");
          resolve();
        }
      });
    });
  } catch (e) {
    logger.Error("设置失败，" + e);
  }
};
export const unsetLimitedWSLConfig = (logger) => {
  try {
    const restored = restoreUserWslConfigFile(logger);
    if (restored) {
      logger.Info("重启 WSL");
      child_process.execSync("wsl --shutdown");
    }
    logger.Info("取消设置成功");
  } catch (e) {
    logger.Error("取消设置失败" + e);
  }
};
