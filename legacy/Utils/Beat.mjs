'use strict';
import {Defined, IsNum, IsNumI} from "./Defined.mjs";
import {_PrintStack} from "./PrintStack.mjs";
import {DateEx} from "./DateEx.mjs";

class Beat {
    constructor(date, noisyTolMax = 10, dtMin = 500) {
        if(!Defined(date, DateEx) || !IsNumI(noisyTolMax) || !IsNum(dtMin)) _PrintStack();
        this._date = date;
        this._noisyTolMax = noisyTolMax;
        this._dtMin = dtMin;
        this._noisyTol = 0;
        this._lastTime = 0;
    }
    Beat() {
        const now = this._date.Now().GetTS();
        const dt = now - this._lastTime;
        if(dt >= this._dtMin) {this._noisyTol = 0; this._lastTime = now; return true;}
        return (++ this._noisyTol <= this._noisyTolMax);
    }
}

export {Beat};
export default Beat;