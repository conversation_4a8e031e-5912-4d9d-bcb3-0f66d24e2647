/**
 * Wraps an async function so that multiple concurrent invocations
 * share a single underlying call, and all callers
 * receive the same result when it resolves or rejects.
 *
 * @template T
 * @template Args
 * @param {(...args: Args) => Promise<T>} fn - The async function to merge calls for.
 * @returns {(...args: Args) => Promise<T>} A function that returns a promise resolving to the shared result.
 */
export const MergeAsyncCalls = (fn) => {
    /** @type {boolean} */
    let inFlight = false;
    /** @type {Array<(value: unknown) => void>} */
    let resolveQueue = [];
    /** @type {Array<(reason: unknown) => void>} */
    let rejectQueue = [];

    return function (...args) {
        return new Promise((resolve, reject) => {
            // Queue this call's resolvers
            resolveQueue.push(resolve);
            rejectQueue.push(reject);

            // If not already executing, start the underlying call
            if (!inFlight) {
                inFlight = true;
                fn(...args)
                    .then((result) => {
                        // Resolve all queued promises
                        resolveQueue.forEach((res) => res(result));
                    })
                    .catch((error) => {
                        // Reject all queued promises
                        rejectQueue.forEach((rej) => rej(error));
                    })
                    .finally(() => {
                        // Reset for next batch
                        inFlight = false;
                        resolveQueue = [];
                        rejectQueue = [];
                    });
            }
        });
    };
};
