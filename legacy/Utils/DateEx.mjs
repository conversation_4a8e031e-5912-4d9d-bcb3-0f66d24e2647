'use strict';
import {IsBool, <PERSON>Num, IsString} from "./Defined.mjs";
import {_PrintStack} from "./PrintStack.mjs";

class DateEx extends Date {
    Format(fmt) {
        if(!IsString(fmt)) _PrintStack();
        const o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        const y = /(y+)/.exec(fmt);
        if(y) fmt = fmt.replace(y[1], String(this.getFullYear()).substring(4 - y[1].length));
        let k, rk;
        for(k in o) {
            rk = new RegExp("(" + k + ")").exec(fmt);
            if(rk) fmt = fmt.replace(rk[1], (rk[1].length == 1) ? String(o[k]) : (("00" + o[k]).substring(String(o[k]).length)));
        }
        return fmt;
    }
    FromStr(str) {
        if(!IsString(str)) _PrintStack();
        let c;
        const dateTime = str.split(' ');
        if(dateTime[0]) {
            c = dateTime[0].split('-');
            this.setFullYear(Number(c[0]));
            this.setMonth(Number(c[1]) - 1);
            this.setDate(Number(c[2]));
        }
        else {
            this.setFullYear(1970);
            this.setMonth(0);
            this.setDate(1);
        }
        if(dateTime[1]) {
            c = dateTime[1].split(':');
            this.setHours(Number(c[0]));
            this.setMinutes(Number(c[1]));
            this.setSeconds(Number(c[2]));
        }
        else {
            this.setHours(0);
            this.setMinutes(0);
            this.setSeconds(0);
        }
        if(isNaN(this.getFullYear())) this.setTime(0);
        return this;
    }
    GetTSSec() {
        const s = this.GetTS().toString();
        return parseInt(s.substring(0, s.length - 3));
    }
    SetTSSec(t) {
        if(!IsNum(t)) _PrintStack();
        this.SetTS(Number(String(t) + "000"));
        return this;
    }
    GetTS() {
        return this.valueOf();
    }
    SetTS(t) {
        if(!IsNum(t)) _PrintStack();
        this.setTime(t);
        return this;
    }
    Now() {
        this.setTime(Date.now());
        return this;
    }
    get utc() {return this._utc;}
    set utc(v) {
        if(!IsBool(v)) _PrintStack();
        this._utc = v;
        const p = Date.prototype;
        if(this._utc) {
            this.getFullYear = p.getUTCFullYear;
            this.getMonth = p.getUTCMonth;
            this.getDate = p.getUTCDate;
            this.getHours = p.getUTCHours;
            this.getMinutes = p.getUTCMinutes;
            this.getSeconds = p.getUTCSeconds;
            this.getMilliseconds = p.getUTCMilliseconds;
        }
        else {
            this.getFullYear = p.getFullYear;
            this.getMonth = p.getMonth;
            this.getDate = p.getDate;
            this.getHours = p.getHours;
            this.getMinutes = p.getMinutes;
            this.getSeconds = p.getSeconds;
            this.getMilliseconds = p.getMilliseconds;
        }
    }
}

export {DateEx};
export default DateEx;