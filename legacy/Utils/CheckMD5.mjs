'use strict';
import {existsSync, statSync, createReadStream} from "node:fs";
import {createHash} from "node:crypto";

const CheckMD5 = (path) => {
    return new Promise((resolve, reject) => {
        if(!existsSync(path) || !statSync(path).isFile()) {reject(new Error("文件不存在")); return;}
        const stream = createReadStream(path);
        const hash = createHash("md5");

        stream.on("data", (chunk) => hash.update(chunk));
        stream.on("end", () => resolve(hash.digest("hex")));
        stream.on("error", (err) => reject(err));
    });
};

// CheckMD5("../tmp/image.tar").then((v) => console.log(v)).catch((e) => console.error(e));

export {CheckMD5};
export default CheckMD5;