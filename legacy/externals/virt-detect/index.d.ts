/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

export interface VirtualizationInfo {
  arch: string
  os: string
  cpuSupported: boolean
  cpuFeatureName: string
  osReportedEnabled: boolean
  osCheckDetails: string
  overallStatusMessage: string
}
export declare function getVirtualization(): VirtualizationInfo
export interface SystemEncoding {
  ansiCode: number
  oemCode: number
  ansiEncoding: string
  oemEncoding: string
}
export declare function getSystemEncoding(): SystemEncoding
export declare function getVersion(): string
export interface FeatureStatus {
  enabled: boolean
  details: Array<string>
}
export declare function isHypervEnabled(): FeatureStatus
export declare function isWslEnabled(): FeatureStatus
export interface MachineIdResult {
  machineId?: string
  error?: string
  factors: Array<string>
}
export const enum MachineIdFactor {
  Baseboard = 0,
  Processor = 1,
  DiskDrivers = 2,
  VideoControllers = 3
}
export declare function getMachineId(factors: Array<MachineIdFactor>): MachineIdResult
