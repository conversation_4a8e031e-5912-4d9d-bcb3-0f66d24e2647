/* tslint:disable */
/* eslint-disable */
/* prettier-ignore */

/* auto-generated by NAPI-RS */

// 跨平台兼容性处理
let nativeBinding;

if (process.platform === 'win32') {
    try {
        // 使用 require 而不是 dynamic import 来避免 top-level await
        nativeBinding = require('./virt-detect.win32-x64-msvc.node');
    } catch (error) {
        console.warn('Failed to load Windows native module:', error.message);
        nativeBinding = null;
    }
} else {
    console.warn(`Platform ${process.platform} is not supported for virt-detect module. Using mock implementation.`);
    nativeBinding = null;
}

// Mock implementations for non-Windows platforms
const mockImplementations = {
    getVirtualization: () => ({
        arch: process.arch,
        os: process.platform,
        cpuSupported: false,
        cpuFeatureName: 'Not supported on this platform',
        osReportedEnabled: false,
        osCheckDetails: 'Not supported on this platform',
        overallStatusMessage: 'Not supported on this platform'
    }),
    getSystemEncoding: () => ({
        ansiCode: 65001,
        oemCode: 65001,
        ansiEncoding: 'utf-8',
        oemEncoding: 'utf-8'
    }),
    getVersion: () => '1.0.0-mock',
    isHypervEnabled: () => ({
        enabled: false,
        details: ['Not supported on this platform']
    }),
    isWslEnabled: () => ({
        enabled: false,
        details: ['Not supported on this platform']
    }),
    getMachineId: (factors) => ({
        machineId: 'mock-machine-id-' + process.platform,
        error: null,
        factors: ['mock']
    }),
    MachineIdFactor: {
        Baseboard: 0,
        Processor: 1,
        DiskDrivers: 2,
        VideoControllers: 3
    }
};

export const {
    getVirtualization,
    getSystemEncoding,
    getVersion,
    isHypervEnabled,
    isWslEnabled,
    getMachineId,
    MachineIdFactor,
} = nativeBinding || mockImplementations;

export default nativeBinding || mockImplementations;
