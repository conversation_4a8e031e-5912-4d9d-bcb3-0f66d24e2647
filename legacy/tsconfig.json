{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.js", "src/**/*.tsx", "src/**/*.vue", "./auto-imports.d.ts"], "compilerOptions": {"baseUrl": ".", "lib": ["es6", "dom"], "types": ["vite/client"], "paths": {"@/*": ["src/renderer/src/*"], "@assets/*": ["src/renderer/src/assets/*"], "@components/*": ["src/renderer/src/components/*"], "@mixins/*": ["src/renderer/src/mixins/*"], "@hooks/*": ["src/renderer/src/hooks/*"]}, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noEmit": true, "allowImportingTsExtensions": true}}