Var APPDATA_PATH

!macro customInstall
  ; ——— 安装时：创建开机自启快捷方式 ———
  DetailPrint "Creating startup shortcut for ${PRODUCT_NAME}"
  ReadEnvStr $APPDATA_PATH "APPDATA"
  StrCpy $APPDATA_PATH "$APPDATA_PATH\Microsoft\Windows\Start Menu\Programs\Startup"
  CreateShortCut "$APPDATA_PATH\${PRODUCT_NAME}.lnk" "$INSTDIR\${PRODUCT_NAME}.exe" "" "$INSTDIR\${PRODUCT_NAME}.exe" 0
!macroend


!macro customUnInstall
  ${ifNot} ${isUpdated}
    ; ——— 卸载时：先卸载 WSL 分发，再删数据目录，再删启动项 ———
    DetailPrint "Unregistering WSL distro: EchoWaveUbuntu"
    ; 用系统目录里的 wsl.exe，避免环境变量问题
    ExecWait '"$SYSDIR\wsl.exe" --unregister EchoWaveUbuntu' $0
    ; 删除用户数据（递归）
    DetailPrint "Removing data folder: %APPDATA%\echowave_client"
    RMDir /r "$APPDATA\echowave_client\EchoWaveUbuntu"
    RMDir /r "$APPDATA\echowave_client"
    ; 删除“启动”文件夹里的快捷方式
    DetailPrint "Deleting startup shortcut"
    ReadEnvStr $APPDATA_PATH "APPDATA"
    StrCpy $APPDATA_PATH "$APPDATA_PATH\Microsoft\Windows\Start Menu\Programs\Startup"
    Delete "$APPDATA_PATH\${PRODUCT_NAME}.lnk"
  ${endIf}
!macroend
