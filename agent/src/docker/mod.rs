use protocol::events::{<PERSON><PERSON><PERSON><PERSON>, ExposeSecret};
use regex::Regex;
use std::{process::Stdio, sync::LazyLock};
use tokio::{io::AsyncWriteExt, process::Command};

// Docker 日志解析正则表达式
// 匹配新格式: 2025-07-25T12:52:09.820+0800 [ERROR] client.driver_mgr.docker: message
static DOCKER_NOMAD_FORMAT_REGEX: LazyLock<Regex> = LazyLock::new(|| {
    Regex::new(r"^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+\d{4})\s+\[(\w+)\]\s+([\w.]+):\s+(.+)$").unwrap()
});

// 原有格式兼容：level=、time=、module= 格式
static DOCKER_LOG_LEVEL_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"(?i)level=(\w+)").unwrap());

static DOCKER_TIMESTAMP_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"time=(\S+)").unwrap());

static DOCKER_MODULE_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"module=(\w+)").unwrap());

pub const SOCK_PATH: &str = "/var/run/docker.sock";

pub async fn set_hosts() -> anyhow::Result<()> {
    let hosts = include_str!("./hosts");
    if let Err(err) = tokio::fs::write("/etc/hosts", hosts).await {
        tracing::error!("设置 Hosts 失败，原因: {err:?}");
        anyhow::bail!("设置 Hosts 失败")
    }
    Ok(())
}

// sudo docker login https://harbor.echowave.cn:8443 --username admin --password echowave@123
pub async fn login(auth: &DockerAuth) -> anyhow::Result<()> {
    tracing::info!("开始进行 Docker 登录: {}", auth.registry_server);
    set_hosts().await?;
    let mut result = Command::new("docker")
        .arg("login")
        .arg(&auth.registry_server)
        .arg("--username")
        .arg(&auth.username)
        .arg("--password-stdin")
        .stdin(Stdio::piped())
        .spawn()
        .map_err(|err| {
            tracing::error!(
                program = "docker",
                args = "login",
                "无法执行 docker 命令，原因: {err:?}"
            );
            err
        })?;
    if let Some(stdin) = result.stdin.as_mut() {
        let _ = stdin
            .write_all(auth.password.expose_secret().as_bytes())
            .await;
        let _ = stdin.write_all(b"\n").await;
    } else {
        tracing::error!(program = "docker", "无法打开 stdin 进行写入密码");
        anyhow::bail!("Docker 登录失败")
    }
    let output = result.wait_with_output().await.map_err(|err| {
        tracing::error!("docker 登录失败, 原因: {err:?}");
        err
    })?;
    if output.status.success() {
        tracing::info!("Docker 登录成功");
        Ok(())
    } else {
        tracing::error!(
            "无法启动 docker 登录, 原因: {}",
            String::from_utf8(output.stderr)?
        );
        anyhow::bail!("Failed launch docker login")
    }
}

/// Docker 专用日志解析器
pub struct DockerLogParser;

impl DockerLogParser {
    /// 解析 Docker 日志级别
    /// 支持新格式: 2025-07-25T12:52:09.820+0800 [ERROR] client.driver_mgr.docker: message
    /// 兼容旧格式: time="2024-06-30T10:30:45.123456789Z" level=info msg="Docker daemon started"
    pub fn parse_log_level(message: &str) -> String {
        // 首先尝试新格式的 [LEVEL] 模式
        if let Some(captures) = DOCKER_NOMAD_FORMAT_REGEX.captures(message) {
            if let Some(level) = captures.get(2) {
                return level.as_str().to_uppercase();
            }
        }
        
        // 然后尝试从 level= 中提取（旧格式）
        if let Some(captures) = DOCKER_LOG_LEVEL_REGEX.captures(message) {
            if let Some(level) = captures.get(1) {
                return level.as_str().to_uppercase();
            }
        }

        // 如果都没有匹配，根据关键字推断
        let message_lower = message.to_lowercase();
        if message_lower.contains("error") || message_lower.contains("fatal") {
            "ERROR".to_string()
        } else if message_lower.contains("warn") {
            "WARN".to_string()
        } else if message_lower.contains("debug") {
            "DEBUG".to_string()
        } else {
            "INFO".to_string()
        }
    }

    /// 解析 Docker 日志时间戳
    /// 支持新格式: 2025-07-25T12:52:09.820+0800
    /// 兼容旧格式: time="2024-06-30T10:30:45.123456789Z"
    pub fn parse_timestamp(message: &str) -> Option<String> {
        // 首先尝试新格式的时间戳
        if let Some(captures) = DOCKER_NOMAD_FORMAT_REGEX.captures(message) {
            if let Some(timestamp) = captures.get(1) {
                return Some(timestamp.as_str().to_string());
            }
        }
        
        // 然后尝试旧格式的 time= 模式
        DOCKER_TIMESTAMP_REGEX
            .captures(message)
            .and_then(|cap| cap.get(1))
            .map(|m| m.as_str().to_string())
    }

    /// 解析 Docker 模块/组件
    /// 支持新格式: client.driver_mgr.docker
    /// 兼容旧格式: module=docker
    pub fn parse_module(message: &str) -> Option<String> {
        // 首先尝试新格式的模块名
        if let Some(captures) = DOCKER_NOMAD_FORMAT_REGEX.captures(message) {
            if let Some(module) = captures.get(3) {
                return Some(module.as_str().to_string());
            }
        }
        
        // 然后尝试旧格式的 module= 模式
        DOCKER_MODULE_REGEX
            .captures(message)
            .and_then(|cap| cap.get(1))
            .map(|m| m.as_str().to_string())
    }

    /// 提取清洁的消息内容，去除时间戳和级别前缀
    pub fn extract_clean_message(message: &str) -> String {
        // 如果匹配新格式，返回纯消息内容
        if let Some(captures) = DOCKER_NOMAD_FORMAT_REGEX.captures(message) {
            if let Some(clean_msg) = captures.get(4) {
                return clean_msg.as_str().to_string();
            }
        }
        
        // 否则返回原始消息
        message.to_string()
    }

    /// 完整解析 Docker 日志
    pub fn parse_log(message: &str) -> DockerLogInfo {
        DockerLogInfo {
            level: Self::parse_log_level(message),
            timestamp: Self::parse_timestamp(message),
            module: Self::parse_module(message),
            raw_message: Self::extract_clean_message(message),
        }
    }
}

#[derive(Debug, Clone)]
pub struct DockerLogInfo {
    pub level: String,
    pub timestamp: Option<String>,
    pub module: Option<String>,
    pub raw_message: String,
}
#[cfg(test)]
mod tests {
    use super::DockerLogParser;

    #[test]
    fn test_new_format_parsing() {
        // 测试新格式日志解析
        let log_line = "2025-07-25T12:52:09.820+0800 [ERROR] client.driver_mgr.docker: failed pulling container: driver=docker image_ref=crpi-50ddbdiwwyr3xyke.cn-chengdu.personal.cr.aliyuncs.com/zzm-rep/npc:0.0.1 error=\"Error response from daemon: pull access denied for crpi-50ddbdiwwyr3xyke.cn-chengdu.personal.cr.aliyuncs.com/zzm-rep/npc, repository does not exist or may require 'docker login': denied: requested access to the resource is denied\" | { forwarded:true original_timestamp:1753419129821 }";
        
        let parsed = DockerLogParser::parse_log(log_line);
        
        assert_eq!(parsed.level, "ERROR");
        assert_eq!(parsed.timestamp, Some("2025-07-25T12:52:09.820+0800".to_string()));
        assert_eq!(parsed.module, Some("client.driver_mgr.docker".to_string()));
        assert_eq!(parsed.raw_message, "failed pulling container: driver=docker image_ref=crpi-50ddbdiwwyr3xyke.cn-chengdu.personal.cr.aliyuncs.com/zzm-rep/npc:0.0.1 error=\"Error response from daemon: pull access denied for crpi-50ddbdiwwyr3xyke.cn-chengdu.personal.cr.aliyuncs.com/zzm-rep/npc, repository does not exist or may require 'docker login': denied: requested access to the resource is denied\" | { forwarded:true original_timestamp:1753419129821 }");
    }

    #[test]
    fn test_old_format_parsing() {
        // 测试旧格式日志解析
        let log_line = "time=\"2024-06-30T10:30:45.123456789Z\" level=info msg=\"Docker daemon started\"";
        
        let parsed = DockerLogParser::parse_log(log_line);
        
        assert_eq!(parsed.level, "INFO");
        assert_eq!(parsed.timestamp, Some("\"2024-06-30T10:30:45.123456789Z\"".to_string()));
        assert_eq!(parsed.raw_message, log_line);
    }

    #[test]
    fn test_clean_message_extraction() {
        let log_line = "2025-07-25T12:52:09.820+0800 [INFO] docker: container started successfully";
        let clean_message = DockerLogParser::extract_clean_message(log_line);
        
        assert_eq!(clean_message, "container started successfully");
    }
}