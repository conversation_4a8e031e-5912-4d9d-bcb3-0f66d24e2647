//! Agent 文件传输管理器
//!
//! 负责处理单个文件传输流程，包括接收文件分块、验证和写入临时文件。

use protocol::events::transfer::ChunkData;
use sha2::{Digest, Sha256};
use std::{io::Write, path::PathBuf};
use tempfile::NamedTempFile;
use tokio::fs;
use uuid::Uuid;

/// 传输管理器的状态机
#[derive(Debug, PartialEq)]
pub enum TransferStatus {
    /// 正在接收文件分块
    Receiving,
    /// 文件接收完成，正在验证
    Verifying,
    /// 验证成功，文件已就绪
    Completed,
    /// 传输失败
    Failed,
}

/// 管理一次完整的文件传输流程
pub struct TransferManager {
    /// 唯一的传输ID
    pub transfer_id: u32,
    /// 跟踪此传输流程的 trace_id
    pub trace_id: Uuid,
    /// 当前状态
    status: TransferStatus,
    /// 临时文件，用于写入接收到的分块
    temp_file: NamedTempFile,
    /// 临时文件的路径
    pub temp_path: PathBuf,
    /// 预期的文件大小
    expected_size: u64,
    /// 预期的文件 SHA256 哈希
    expected_hash: String,
    /// 分块总数
    total_chunks: u32,
    /// 每个分块的大小
    chunk_size: u32,
    /// 用于跟踪已接收分块的位图
    received_chunks: Vec<bool>,
    /// 已接收的分块数量
    received_count: u32,
}

impl TransferManager {
    /// 创建一个新的 TransferManager 实例
    pub fn new(
        transfer_id: u32,
        trace_id: Uuid,
        file_size: u64,
        file_hash: String,
        total_chunks: u32,
        chunk_size: u32,
    ) -> anyhow::Result<Self> {
        let temp_file = NamedTempFile::new()?;
        let temp_path = temp_file.path().to_path_buf();

        Ok(Self {
            transfer_id,
            trace_id,
            status: TransferStatus::Receiving,
            temp_file,
            temp_path,
            expected_size: file_size,
            expected_hash: file_hash,
            total_chunks,
            chunk_size,
            received_chunks: vec![false; total_chunks as usize],
            received_count: 0,
        })
    }

    /// 获取当前状态
    pub fn status(&self) -> &TransferStatus {
        &self.status
    }

    /// 写入一个数据分块
    /// 
    /// 此方法在独立的 tokio 任务中调用，避免阻塞主通信循环
    pub fn write_chunk(&mut self, chunk: ChunkData) -> anyhow::Result<bool> {
        // 检查传输状态
        if self.status != TransferStatus::Receiving {
            anyhow::bail!(
                "传输状态错误: 期望 Receiving，实际 {:?}",
                self.status
            );
        }

        // 验证 chunk_id 范围
        if chunk.chunk_id >= self.total_chunks {
            anyhow::bail!(
                "无效的分块ID: {}，应小于 {}",
                chunk.chunk_id,
                self.total_chunks
            );
        }

        // 检查是否已经接收过这个分块
        if self.received_chunks[chunk.chunk_id as usize] {
            tracing::warn!(
                trace_id = %self.trace_id,
                transfer_id = self.transfer_id,
                chunk_id = chunk.chunk_id,
                "收到重复的分块，忽略"
            );
            return Ok(false); // 返回 false 表示没有新数据写入
        }

        // 计算这个分块在文件中的偏移位置
        let offset = (chunk.chunk_id as u64) * (self.chunk_size as u64);
        
        // 验证分块大小
        let expected_chunk_size = if chunk.chunk_id == self.total_chunks - 1 {
            // 最后一个分块可能比较小
            let remaining = self.expected_size - offset;
            remaining.min(self.chunk_size as u64) as usize
        } else {
            self.chunk_size as usize
        };

        if chunk.data.len() != expected_chunk_size {
            anyhow::bail!(
                "分块大小不匹配: 期望 {} 字节，实际 {} 字节",
                expected_chunk_size,
                chunk.data.len()
            );
        }

        // 使用 std::io::Seek 来定位写入位置
        use std::io::Seek;
        self.temp_file.seek(std::io::SeekFrom::Start(offset))?;
        
        // 写入数据
        self.temp_file.write_all(chunk.data)?;
        self.temp_file.flush()?;

        // 更新接收状态
        self.received_chunks[chunk.chunk_id as usize] = true;
        self.received_count += 1;

        tracing::debug!(
            trace_id = %self.trace_id,
            transfer_id = self.transfer_id,
            chunk_id = chunk.chunk_id,
            received_count = self.received_count,
            total_chunks = self.total_chunks,
            "分块写入成功"
        );

        // 检查是否所有分块都已接收
        Ok(self.received_count == self.total_chunks)
    }

    /// 验证文件完整性并完成传输
    /// 
    /// 此方法应该在所有分块接收完成后调用
    pub async fn verify_and_complete(&mut self) -> anyhow::Result<()> {
        // 检查状态
        if self.status != TransferStatus::Receiving {
            anyhow::bail!(
                "传输状态错误: 期望 Receiving，实际 {:?}",
                self.status
            );
        }

        // 检查是否所有分块都已接收
        if self.received_count != self.total_chunks {
            anyhow::bail!(
                "分块接收不完整: 期望 {} 个，实际接收 {} 个",
                self.total_chunks,
                self.received_count
            );
        }

        self.status = TransferStatus::Verifying;

        // 确保所有数据都已写入磁盘
        self.temp_file.flush()?;

        // 验证文件大小
        let actual_size = fs::metadata(&self.temp_path).await?.len();
        if actual_size != self.expected_size {
            self.status = TransferStatus::Failed;
            anyhow::bail!(
                "文件大小不匹配: 期望 {} 字节，实际 {} 字节",
                self.expected_size,
                actual_size
            );
        }

        // 计算文件的 SHA256 哈希
        // todo: 使用 shared crate 里面的哈希进行流式哈希计算
        let file_content = fs::read(&self.temp_path).await?;
        let mut hasher = Sha256::new();
        hasher.update(&file_content);
        let actual_hash = format!("{:x}", hasher.finalize());

        // 验证哈希值
        if actual_hash != self.expected_hash {
            self.status = TransferStatus::Failed;
            anyhow::bail!(
                "文件哈希值不匹配: 期望 {}，实际 {}",
                self.expected_hash,
                actual_hash
            );
        }

        self.status = TransferStatus::Completed;
        
        tracing::info!(
            trace_id = %self.trace_id,
            transfer_id = self.transfer_id,
            file_size = self.expected_size,
            "文件传输验证成功"
        );

        Ok(())
    }

    /// 获取缺失的分块ID列表
    /// 
    /// 用于请求重传
    pub fn get_missing_chunks(&self) -> Vec<u32> {
        self.received_chunks
            .iter()
            .enumerate()
            .filter_map(|(index, &received)| {
                if !received {
                    Some(index as u32)
                } else {
                    None
                }
            })
            .collect()
    }

    /// 是否已完成接收
    pub fn is_completed(&self) -> bool {
        self.status == TransferStatus::Completed
    }

    /// 是否传输失败
    pub fn is_failed(&self) -> bool {
        self.status == TransferStatus::Failed
    }

    /// 获取临时文件路径的克隆
    pub fn temp_path(&self) -> PathBuf {
        self.temp_path.clone()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use protocol::events::transfer::ChunkData;
    use sha2::{Digest, Sha256};
    use tokio::fs;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_transfer_manager_complete_flow() {
        // 创建测试数据
        let test_data = b"Hello, World! This is a complete transfer manager test with more data to ensure we have multiple chunks.";
        let chunk_size = 32u32; // 小分块大小便于测试
        let total_chunks = ((test_data.len() as f64) / (chunk_size as f64)).ceil() as u32;
        
        // 计算预期哈希
        let mut hasher = Sha256::new();
        hasher.update(test_data);
        let expected_hash = format!("{:x}", hasher.finalize());
        
        let transfer_id = 123;
        let trace_id = Uuid::new_v4();
        
        // 创建 TransferManager
        let mut manager = TransferManager::new(
            transfer_id,
            trace_id,
            test_data.len() as u64,
            expected_hash,
            total_chunks,
            chunk_size,
        ).unwrap();
        
        assert_eq!(manager.transfer_id, transfer_id);
        assert_eq!(manager.trace_id, trace_id);
        assert_eq!(manager.expected_size, test_data.len() as u64);
        assert_eq!(manager.total_chunks, total_chunks);
        assert!(!manager.is_completed());
        assert!(!manager.is_failed());
        
        // 模拟分块发送
        for chunk_id in 0..total_chunks {
            let start_offset = (chunk_id as usize) * (chunk_size as usize);
            let end_offset = std::cmp::min(
                start_offset + (chunk_size as usize),
                test_data.len(),
            );
            
            let chunk_data_slice = &test_data[start_offset..end_offset];
            let chunk = ChunkData {
                transfer_id,
                chunk_id,
                data: chunk_data_slice,
            };
            
            let all_received = manager.write_chunk(chunk).unwrap();
            
            if chunk_id == total_chunks - 1 {
                assert!(all_received, "最后一个分块应该触发完成状态");
            } else {
                assert!(!all_received, "中间分块不应该触发完成状态");
            }
        }
        
        // 验证文件完整性
        manager.verify_and_complete().await.unwrap();
        assert!(manager.is_completed());
        assert!(!manager.is_failed());
        
        // 验证文件内容
        let written_data = fs::read(&manager.temp_path).await.unwrap();
        assert_eq!(written_data, test_data);
    }

    #[tokio::test]
    async fn test_transfer_manager_duplicate_chunks() {
        let test_data = b"Duplicate chunk test data";
        let chunk_size = 10u32;
        let total_chunks = ((test_data.len() as f64) / (chunk_size as f64)).ceil() as u32;
        
        let mut hasher = Sha256::new();
        hasher.update(test_data);
        let expected_hash = format!("{:x}", hasher.finalize());
        
        let mut manager = TransferManager::new(
            1,
            Uuid::new_v4(),
            test_data.len() as u64,
            expected_hash,
            total_chunks,
            chunk_size,
        ).unwrap();
        
        // 发送第一个分块
        let chunk = ChunkData {
            transfer_id: 1,
            chunk_id: 0,
            data: &test_data[0..10],
        };
        
        let result1 = manager.write_chunk(chunk.clone()).unwrap();
        assert!(!result1, "第一次发送应该返回false（未完成）");
        
        // 重复发送同一个分块
        let result2 = manager.write_chunk(chunk).unwrap();
        assert!(!result2, "重复分块应该被忽略，返回false");
        
        // 检查缺失分块列表不包含已接收的分块
        let missing = manager.get_missing_chunks();
        assert!(!missing.contains(&0), "已接收的分块不应该在缺失列表中");
        assert_eq!(missing.len(), total_chunks as usize - 1);
    }

    #[tokio::test]
    async fn test_transfer_manager_invalid_chunk_id() {
        let test_data = b"Invalid chunk ID test";
        let chunk_size = 10u32;
        let total_chunks = 3u32;
        
        let mut hasher = Sha256::new();
        hasher.update(test_data);
        let expected_hash = format!("{:x}", hasher.finalize());
        
        let mut manager = TransferManager::new(
            1,
            Uuid::new_v4(),
            test_data.len() as u64,
            expected_hash,
            total_chunks,
            chunk_size,
        ).unwrap();
        
        // 尝试发送超出范围的分块ID
        let invalid_chunk = ChunkData {
            transfer_id: 1,
            chunk_id: total_chunks, // 超出范围
            data: b"invalid",
        };
        
        let result = manager.write_chunk(invalid_chunk);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("无效的分块ID"));
    }

    #[tokio::test]
    async fn test_transfer_manager_hash_verification_failure() {
        let test_data = b"Hash verification test data";
        let chunk_size = 20u32;
        let total_chunks = 2u32;
        
        // 故意提供错误的哈希值
        let wrong_hash = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef".to_string();
        
        let mut manager = TransferManager::new(
            1,
            Uuid::new_v4(),
            test_data.len() as u64,
            wrong_hash,
            total_chunks,
            chunk_size,
        ).unwrap();
        
        // 发送所有分块
        for chunk_id in 0..total_chunks {
            let start = (chunk_id as usize) * (chunk_size as usize);
            let end = std::cmp::min(start + (chunk_size as usize), test_data.len());
            
            let chunk = ChunkData {
                transfer_id: 1,
                chunk_id,
                data: &test_data[start..end],
            };
            
            manager.write_chunk(chunk).unwrap();
        }
        
        // 验证应该失败
        let result = manager.verify_and_complete().await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("文件哈希值不匹配"));
        assert!(manager.is_failed());
    }

    #[tokio::test]
    async fn test_transfer_manager_get_missing_chunks() {
        let test_data = b"Missing chunks test data with more content";
        let chunk_size = 10u32;
        let total_chunks = 5u32;
        
        let mut hasher = Sha256::new();
        hasher.update(test_data);
        let expected_hash = format!("{:x}", hasher.finalize());
        
        let mut manager = TransferManager::new(
            1,
            Uuid::new_v4(),
            test_data.len() as u64,
            expected_hash,
            total_chunks,
            chunk_size,
        ).unwrap();
        
        // 初始状态，所有分块都缺失
        let missing = manager.get_missing_chunks();
        assert_eq!(missing.len(), total_chunks as usize);
        assert_eq!(missing, vec![0, 1, 2, 3, 4]);
        
        // 发送分块 0 和 2
        let chunk_0 = ChunkData {
            transfer_id: 1,
            chunk_id: 0,
            data: &test_data[0..10],
        };
        manager.write_chunk(chunk_0).unwrap();
        
        let chunk_2 = ChunkData {
            transfer_id: 1,
            chunk_id: 2,
            data: &test_data[20..30],
        };
        manager.write_chunk(chunk_2).unwrap();
        
        // 检查缺失分块列表
        let missing = manager.get_missing_chunks();
        assert_eq!(missing.len(), 3);
        assert_eq!(missing, vec![1, 3, 4]);
    }
}
