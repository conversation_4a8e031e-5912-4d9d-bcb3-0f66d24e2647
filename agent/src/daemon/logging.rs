use tokio::io::{AsyncBufReadExt, Async<PERSON><PERSON>, Buf<PERSON>eader};
use tracing::Level;

/// Parsed log information from daemon output
#[derive(Debug, <PERSON>lone)]
pub struct ParsedLog {
    pub level: String,
    pub timestamp: Option<String>,
    pub module: Option<String>,
    pub raw_message: String,
}

/// Generic log parser trait that different daemons can implement
pub trait LogParser {
    fn parse_log(line: &str) -> ParsedLog;
}

/// Daemon type enum for compile-time target resolution
#[derive(Debug, <PERSON>lone, Copy)]
pub enum DaemonType {
    Tailscaled,
    Dockerd,
    Nomad,
    Unknown,
}

impl DaemonType {
    pub fn from_name(name: &str) -> Self {
        match name {
            "tailscaled" => Self::Tailscaled,
            "dockerd" => Self::Dockerd,
            "nomad" => Self::Nomad,
            _ => Self::Unknown,
        }
    }
}

/// Unified daemon log forwarder using tracing's structured logging
pub struct DaemonLogForwarder;

impl DaemonLogForwarder {
    /// Spawn a task to forward daemon logs to tracing with proper structure
    pub async fn spawn_handler<R>(
        reader: R,
        daemon_type: DaemonType,
        stream_type: &'static str,
        parser: impl Fn(&str) -> ParsedLog + Send + 'static,
    ) -> tokio::task::JoinHandle<anyhow::Result<()>>
    where
        R: AsyncRead + Unpin + Send + 'static,
    {
        tokio::spawn(async move {
            let mut lines = BufReader::new(reader).lines();

            while let Some(line) = lines.next_line().await? {
                if line.trim().is_empty() {
                    continue;
                }

                let parsed = parser(&line);
                Self::emit_log_event(daemon_type, stream_type, &parsed);
            }

            let daemon_name = Self::daemon_name(daemon_type);
            tracing::debug!(
                daemon = daemon_name,
                stream = stream_type,
                "log stream ended"
            );
            Ok(())
        })
    }

    /// Emit a structured log event using tracing with compile-time targets
    fn emit_log_event(daemon_type: DaemonType, _stream_type: &str, parsed: &ParsedLog) {
        let level = Self::parse_log_level(&parsed.level);
        let module = parsed.module.as_deref().unwrap_or("");

        // Use daemon-specific emit functions to ensure compile-time constant targets
        match daemon_type {
            DaemonType::Tailscaled => {
                Self::emit_tailscaled(level, module, &parsed.raw_message);
            }
            DaemonType::Dockerd => {
                Self::emit_dockerd(level, module, &parsed.raw_message);
            }
            DaemonType::Nomad => {
                Self::emit_nomad(level, module, &parsed.raw_message);
            }
            DaemonType::Unknown => {
                Self::emit_unknown(level, module, &parsed.raw_message);
            }
        }
    }

    /// Emit event for specific daemon type with compile-time target
    fn emit_tailscaled(level: Level, module: &str, message: &str) {
        // 构建清洁的日志消息格式
        let formatted_message = if !module.is_empty() {
            format!("{}: {}", module, message)
        } else {
            message.to_string()
        };

        match level {
            Level::ERROR => {
                tracing::error!(target: "tailscaled", "{}", formatted_message)
            }
            Level::WARN => {
                tracing::warn!(target: "tailscaled", "{}", formatted_message)
            }
            Level::INFO => {
                tracing::info!(target: "tailscaled", "{}", formatted_message)
            }
            Level::DEBUG => {
                tracing::debug!(target: "tailscaled", "{}", formatted_message)
            }
            Level::TRACE => {
                tracing::trace!(target: "tailscaled", "{}", formatted_message)
            }
        }
    }

    fn emit_dockerd(level: Level, module: &str, message: &str) {
        // 构建清洁的日志消息格式
        let formatted_message = if !module.is_empty() {
            format!("{}: {}", module, message)
        } else {
            message.to_string()
        };

        match level {
            Level::ERROR => {
                tracing::error!(target: "dockerd", "{}", formatted_message)
            }
            Level::WARN => {
                tracing::warn!(target: "dockerd", "{}", formatted_message)
            }
            Level::INFO => {
                tracing::info!(target: "dockerd", "{}", formatted_message)
            }
            Level::DEBUG => {
                tracing::debug!(target: "dockerd", "{}", formatted_message)
            }
            Level::TRACE => {
                tracing::trace!(target: "dockerd", "{}", formatted_message)
            }
        }
    }
    fn emit_nomad(level: Level, module: &str, message: &str) {
        // 构建清洁的日志消息格式
        let formatted_message = if !module.is_empty() {
            format!("{}: {}", module, message)
        } else {
            message.to_string()
        };

        match level {
            Level::ERROR => {
                tracing::error!(target: "nomad", "{}", formatted_message)
            }
            Level::WARN => {
                tracing::warn!(target: "nomad", "{}", formatted_message)
            }
            Level::INFO => {
                tracing::info!(target: "nomad", "{}", formatted_message)
            }
            Level::DEBUG => {
                tracing::debug!(target: "nomad", "{}", formatted_message)
            }
            Level::TRACE => {
                tracing::trace!(target: "nomad", "{}", formatted_message)
            }
        }
    }

    fn emit_unknown(level: Level, module: &str, message: &str) {
        // 构建清洁的日志消息格式
        let formatted_message = if !module.is_empty() {
            format!("{}: {}", module, message)
        } else {
            message.to_string()
        };

        match level {
            Level::ERROR => {
                tracing::error!(target: "unknown", "{}", formatted_message)
            }
            Level::WARN => {
                tracing::warn!(target: "unknown", "{}", formatted_message)
            }
            Level::INFO => {
                tracing::info!(target: "unknown", "{}", formatted_message)
            }
            Level::DEBUG => {
                tracing::debug!(target: "unknown", "{}", formatted_message)
            }
            Level::TRACE => {
                tracing::trace!(target: "unknown", "{}", formatted_message)
            }
        }
    }

    /// Parse log level from string, with special handling for stderr streams
    fn parse_log_level(level_str: &str) -> Level {
        let base_level = match level_str.to_uppercase().as_str() {
            "ERROR" | "FATAL" => Level::ERROR,
            "WARN" | "WARNING" => Level::WARN,
            "DEBUG" => Level::DEBUG,
            "TRACE" => Level::TRACE,
            _ => Level::INFO,
        };

        base_level
    }

    /// Get daemon name string
    fn daemon_name(daemon_type: DaemonType) -> &'static str {
        match daemon_type {
            DaemonType::Tailscaled => "tailscaled",
            DaemonType::Dockerd => "dockerd",
            DaemonType::Nomad => "nomad",
            DaemonType::Unknown => "unknown",
        }
    }
}
