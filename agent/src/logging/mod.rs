use crate::communication::stdio::MessageSender;
use std::collections::VecDeque;
use std::fs::OpenOptions;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing_subscriber::{
    fmt::{self, time::LocalTime},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};

const MAX_BUFFERED_LOGS: usize = 5000;

pub fn registry() {
    #[cfg(debug_assertions)]
    unsafe {
        std::env::set_var("RUST_LOG", "TRACE")
    };
    tracing_subscriber::registry()
        .with(fmt::layer().with_timer(LocalTime::rfc_3339()))
        .with(EnvFilter::from_default_env())
        .init();
}

pub fn registry_with_stdio_buffering() {
    #[cfg(debug_assertions)]
    unsafe {
        std::env::set_var("RUST_LOG", "TRACE")
    };

    let stdio_layer = StdioLayer::new_with_buffering();
    let file = OpenOptions::new()
        .write(true)
        .truncate(true)
        .open("/opt/agent/log-debug.log")
        .unwrap();
    tracing_subscriber::registry()
        .with(stdio_layer)
        .with(
            fmt::layer()
                .with_file(true)
                .with_line_number(true)
                .with_writer(file),
        )
        .with(EnvFilter::from_default_env())
        .init();
}

pub async fn set_stdio_sender(message_sender: Arc<MessageSender>) -> Option<StdioSenderGuard> {
    if let Some(layer) = STDIO_LAYER.get() {
        layer.set_sender(message_sender).await;
        Some(StdioSenderGuard {})
    } else {
        None
    }
}

pub struct StdioSenderGuard {}

impl Drop for StdioSenderGuard {
    fn drop(&mut self) {
        tokio::spawn(async move {
            if let Some(layer) = STDIO_LAYER.get() {
                layer.clear_sender().await;
            }
        });
    }
}

static STDIO_LAYER: std::sync::OnceLock<StdioLayer> = std::sync::OnceLock::new();

use std::fmt::Write as FmtWrite;
use tracing::{Event, Subscriber};
use tracing_subscriber::layer::Context;
use tracing_subscriber::registry::LookupSpan;

pub struct StdioLayer {
    message_sender: Arc<Mutex<Option<Arc<MessageSender>>>>,
    log_buffer: Arc<Mutex<VecDeque<protocol::events::LogEntry>>>,
    is_connected: Arc<AtomicBool>,
}

impl StdioLayer {
    pub fn new_with_buffering() -> Self {
        let layer = Self {
            message_sender: Arc::new(Mutex::new(None)),
            log_buffer: Arc::new(Mutex::new(VecDeque::with_capacity(MAX_BUFFERED_LOGS))),
            is_connected: Arc::new(AtomicBool::new(false)),
        };

        // 将layer存储到全局变量中
        STDIO_LAYER.set(layer.clone()).ok();

        layer
    }

    pub async fn set_sender(&self, message_sender: Arc<MessageSender>) {
        let mut sender_guard = self.message_sender.lock().await;
        *sender_guard = Some(message_sender.clone());

        // 标记为已连接
        self.is_connected.store(true, Ordering::Relaxed);

        // 发送缓冲的日志
        let mut buffer_guard = self.log_buffer.lock().await;
        let log_entries = buffer_guard.drain(..).collect::<Vec<_>>();
        let event = protocol::events::AgentEvent::LogBatchForward(log_entries);

        if let Err(e) = message_sender.send_event(0, false, &event).await {
            eprintln!("Failed to send buffered log: {}", e);
        }
    }

    pub async fn clear_sender(&self) {
        let mut sender_guard = self.message_sender.lock().await;
        *sender_guard = None;

        // 标记为未连接
        self.is_connected.store(false, Ordering::Relaxed);
    }

    fn add_to_buffer(&self, log_entry: protocol::events::LogEntry) {
        let buffer = self.log_buffer.clone();
        tokio::spawn(async move {
            let mut buffer_guard = buffer.lock().await;

            // 如果缓冲区已满，移除最旧的日志
            if buffer_guard.len() >= MAX_BUFFERED_LOGS {
                buffer_guard.pop_front();
            }

            buffer_guard.push_back(log_entry);
        });
    }
}

impl Clone for StdioLayer {
    fn clone(&self) -> Self {
        Self {
            message_sender: self.message_sender.clone(),
            log_buffer: self.log_buffer.clone(),
            is_connected: self.is_connected.clone(),
        }
    }
}

impl<S> Layer<S> for StdioLayer
where
    S: Subscriber + for<'a> LookupSpan<'a>,
{
    fn on_event(&self, event: &Event<'_>, ctx: Context<'_, S>) {
        let mut message = String::new();
        let target = event.metadata().target();

        // 检测是否是守护进程日志
        let is_daemon_log = target.starts_with("daemon::");

        // 构建日志消息
        let mut visitor = if is_daemon_log {
            MessageVisitor::new_daemon(&mut message)
        } else {
            MessageVisitor::new_regular(&mut message)
        };
        event.record(&mut visitor);

        // 获取日志级别
        let level = match *event.metadata().level() {
            tracing::Level::ERROR => protocol::events::LogLevel::Error,
            tracing::Level::WARN => protocol::events::LogLevel::Warn,
            tracing::Level::INFO => protocol::events::LogLevel::Info,
            tracing::Level::DEBUG => protocol::events::LogLevel::Debug,
            tracing::Level::TRACE => protocol::events::LogLevel::Trace,
        };

        // 获取trace_id (只从span context中获取，不生成新的)
        let trace_id = ctx
            .lookup_current()
            .and_then(|span| span.extensions().get::<uuid::Uuid>().copied());

        // 对于守护进程日志，优化模块路径和源信息
        let (module_path, source) = if is_daemon_log {
            // 使用守护进程的模块信息，如果存在的话
            let daemon_module = visitor.get_daemon_module();
            if !daemon_module.is_empty() {
                (
                    daemon_module,
                    format!("daemon/{}", visitor.get_daemon_name()),
                )
            } else {
                (target.to_string(), "daemon".to_string())
            }
        } else {
            (
                event
                    .metadata()
                    .module_path()
                    .unwrap_or("unknown")
                    .to_string(),
                "agent".to_string(),
            )
        };

        // 创建LogEntry
        let log_entry = protocol::events::LogEntry {
            source,
            level,
            message,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            trace_id,
            module: module_path,
            target: Some(target.to_string()),
            file: event.metadata().file().map(|s| s.to_string()),
            line: event.metadata().line(),
        };

        // 使用原子操作快速检查连接状态
        if self.is_connected.load(Ordering::Relaxed) {
            // 已连接，尝试直接发送
            let sender_ref = self.message_sender.clone();
            tokio::spawn(async move {
                if let Ok(sender_guard) = sender_ref.try_lock() {
                    if let Some(sender) = sender_guard.as_ref() {
                        let sender_clone = sender.clone();
                        drop(sender_guard); // 立即释放锁

                        let event = protocol::events::AgentEvent::LogForward(log_entry);
                        if let Err(e) = sender_clone.send_event(0, false, &event).await {
                            eprintln!("Failed to send log through stdio: {}", e);
                        }
                    }
                }
            });
        } else {
            // 未连接，直接缓存
            self.add_to_buffer(log_entry);
        }
    }
}

struct MessageVisitor<'a> {
    message: &'a mut String,
    is_daemon: bool,
    daemon_name: String,
    daemon_stream: String,
    daemon_module: String,
    daemon_timestamp: String,
}

impl<'a> MessageVisitor<'a> {
    fn new_regular(message: &'a mut String) -> Self {
        Self {
            message,
            is_daemon: false,
            daemon_name: String::new(),
            daemon_stream: String::new(),
            daemon_module: String::new(),
            daemon_timestamp: String::new(),
        }
    }

    fn new_daemon(message: &'a mut String) -> Self {
        Self {
            message,
            is_daemon: true,
            daemon_name: String::new(),
            daemon_stream: String::new(),
            daemon_module: String::new(),
            daemon_timestamp: String::new(),
        }
    }

    fn get_daemon_name(&self) -> &str {
        &self.daemon_name
    }

    fn get_daemon_module(&self) -> String {
        if !self.daemon_module.is_empty() {
            format!("{}::{}", self.daemon_name, self.daemon_module)
        } else {
            self.daemon_name.clone()
        }
    }
}

impl<'a> tracing::field::Visit for MessageVisitor<'a> {
    fn record_debug(&mut self, field: &tracing::field::Field, value: &dyn std::fmt::Debug) {
        let field_name = field.name();

        if self.is_daemon {
            // 特殊处理守护进程日志的结构化字段
            match field_name {
                "message" => {
                    let msg = format!("{:?}", value);
                    // 移除外层的引号
                    if msg.starts_with('"') && msg.ends_with('"') && msg.len() > 2 {
                        self.message.push_str(&msg[1..msg.len() - 1]);
                    } else {
                        self.message.push_str(&msg);
                    }
                }
                "daemon" => {
                    self.daemon_name = format!("{:?}", value).trim_matches('"').to_string();
                }
                "stream" => {
                    self.daemon_stream = format!("{:?}", value).trim_matches('"').to_string();
                }
                "module" => {
                    self.daemon_module = format!("{:?}", value).trim_matches('"').to_string();
                }
                "timestamp" => {
                    self.daemon_timestamp = format!("{:?}", value).trim_matches('"').to_string();
                }
                _ => {
                    // 其他字段作为附加信息
                    if !self.message.is_empty() {
                        self.message.push(' ');
                    }
                    write!(self.message, "{}={:?}", field_name, value).ok();
                }
            }
        } else {
            // 普通日志的处理逻辑
            if field_name == "message" {
                let msg = format!("{:?}", value);
                // 移除外层的引号
                if msg.starts_with('"') && msg.ends_with('"') && msg.len() > 2 {
                    self.message.push_str(&msg[1..msg.len() - 1]);
                } else {
                    self.message.push_str(&msg);
                }
            } else {
                if !self.message.is_empty() {
                    self.message.push(' ');
                }
                write!(self.message, "{}={:?}", field_name, value).ok();
            }
        }
    }
}
