//! Agent 事件处理模块
//!
//! 整合了通信协议处理和业务逻辑处理，提供统一的事件处理接口

use chrono::Utc;
use protocol::events::transfer::ChunkData;
use protocol::events::{
    AgentEvent, AgentResponse, DockerAuth, LatencyInfo, NodeInfo, ResourceInfo, TailscaleAuth,
    TaskInfo, TransferEvent,
};
use tokio::process::Command;
use uuid::Uuid;

use crate::communication::stdio::MessageSender;
use crate::nomad::AgentInfoObj;
use crate::transfer_manager::TransferManager;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;

/// Agent 事件处理器
pub struct AgentHandler {
    /// 管理所有正在进行的传输任务, key 为 transfer_id
    transfer_managers: Arc<Mutex<HashMap<u32, TransferManager>>>,
    /// 消息发送器，用于发送事件到 Domain
    message_sender: Option<Arc<MessageSender>>,
}

impl AgentHandler {
    pub fn new() -> Self {
        Self {
            transfer_managers: Arc::new(Mutex::new(HashMap::new())),
            message_sender: None,
        }
    }

    /// 设置消息发送器，用于向 Domain 发送事件
    pub fn set_message_sender(&mut self, sender: Arc<MessageSender>) {
        self.message_sender = Some(sender);
    }

    /// 处理来自 Core 的事件
    pub async fn handle(&self, event: AgentEvent) -> Result<AgentResponse, HandlerError> {
        tracing::debug!("处理Agent事件: {:?}", event);

        match event {
            AgentEvent::Shutdown { trace_id } => self.handle_shutdown(trace_id).await,
            AgentEvent::Stop { trace_id } => self.handle_stop_daemons(trace_id).await,
            AgentEvent::Start {
                trace_id,
                tailscale_ip,
                docker_auth,
            } => {
                self.handle_start_daemons(trace_id, tailscale_ip, docker_auth)
                    .await
            }
            AgentEvent::WaitForTailscaleReady {
                trace_id,
                tailscale_auth,
            } => {
                self.handle_wait_for_tailscale_ready(trace_id, tailscale_auth)
                    .await
            }
            AgentEvent::GetNodeStatus { trace_id } => self.handle_get_node_status(trace_id).await,
            AgentEvent::GetNetworkLatency { trace_id } => {
                self.handle_get_network_latency(trace_id).await
            }
            AgentEvent::GetHealthStatus { trace_id } => {
                self.handle_get_health_status(trace_id).await
            }
            AgentEvent::Transfer(transfer_event) => {
                self.handle_transfer_event(transfer_event).await
            }
            AgentEvent::StartSelfUpdate {
                trace_id,
                transfer_id,
                version,
            } => {
                self.handle_start_self_update(trace_id, transfer_id, version)
                    .await
            }
            _ => {
                // 非请求事件
                Err(HandlerError::UnsupportedEvent(event.event_name()))
            }
        }
    }

    /// 处理传输相关事件
    async fn handle_transfer_event(
        &self,
        event: TransferEvent,
    ) -> Result<AgentResponse, HandlerError> {
        match event {
            TransferEvent::StartTransfer {
                trace_id,
                transfer_id,
                file_size,
                file_hash,
                total_chunks,
                chunk_size,
            } => {
                let mut managers = self.transfer_managers.lock().await;

                // 检查是否已存在同ID的传输
                if managers.contains_key(&transfer_id) {
                    return Err(HandlerError::Internal(format!(
                        "传输任务 {} 已存在",
                        transfer_id
                    )));
                }

                // 创建新的 TransferManager
                let manager = TransferManager::new(
                    transfer_id,
                    trace_id,
                    file_size,
                    file_hash,
                    total_chunks,
                    chunk_size,
                )
                .map_err(|e| HandlerError::Internal(e.to_string()))?;

                tracing::info!("开始新的文件传输: transfer_id={}", transfer_id);
                managers.insert(transfer_id, manager);

                // StartTransfer 事件不需要立即响应，等待后续操作
                // 这里返回一个临时的成功响应，表示已接受任务
                Ok(AgentResponse::Success { trace_id })
            }
            // 其他 TransferEvent 在此处理
            _ => Err(HandlerError::UnsupportedEvent("该传输事件类型")),
        }
    }

    #[tracing::instrument(skip(self, tailscale_auth))]
    async fn handle_wait_for_tailscale_ready(
        &self,
        trace_id: Uuid,
        tailscale_auth: Option<TailscaleAuth>,
    ) -> Result<AgentResponse, HandlerError> {
        tracing::info!("处理等待 Tailscale 就绪事件");

        let Some(auth) = tailscale_auth else {
            tracing::error!("未提供 Tailscale 认证信息，无法继续");
            return Ok(AgentResponse::TailscaleReady {
                trace_id,
                is_ready: false,
                ip: None,
            });
        };

        match crate::tailscale::wait_for_ready(&auth).await {
            Ok(ip) => Ok(AgentResponse::TailscaleReady {
                trace_id,
                is_ready: true,
                ip: Some(ip),
            }),
            Err(e) => {
                tracing::error!("等待 Tailscale 就绪时出错: {}", e);
                // 即使失败，也返回一个响应，让调用方知道结果
                Ok(AgentResponse::TailscaleReady {
                    trace_id,
                    is_ready: false,
                    ip: None,
                })
            }
        }
    }

    #[tracing::instrument(skip(self))]
    async fn handle_shutdown(&self, trace_id: Uuid) -> Result<AgentResponse, HandlerError> {
        tracing::info!("执行Agent关闭操作");

        crate::daemon::terminate_daemons()
            .await
            .map_err(|e| HandlerError::Internal(e.to_string()))?;

        Ok(AgentResponse::Success { trace_id })
    }

    #[tracing::instrument(skip(self))]
    async fn handle_stop_daemons(&self, trace_id: Uuid) -> Result<AgentResponse, HandlerError> {
        tracing::info!("停止守护进程");

        crate::daemon::stop_docker_and_nomad()
            .await
            .map_err(|e| HandlerError::Internal(e.to_string()))?;

        Ok(AgentResponse::Success { trace_id })
    }

    #[tracing::instrument(skip(self, docker_auth))]
    async fn handle_start_daemons(
        &self,
        trace_id: Uuid,
        tailscale_ip: String,
        docker_auth: Option<DockerAuth>,
    ) -> Result<AgentResponse, HandlerError> {
        tracing::info!("启动守护进程");

        crate::daemon::start_docker_and_nomad(&tailscale_ip, docker_auth.as_ref())
            .await
            .map_err(|e| HandlerError::Internal(e.to_string()))?;

        Ok(AgentResponse::Success { trace_id })
    }

    #[tracing::instrument(skip(self))]
    async fn handle_get_node_status(&self, trace_id: Uuid) -> Result<AgentResponse, HandlerError> {
        tracing::debug!("获取节点状态");

        tracing::debug!("获取节点信息");
        let (node_id, ip) = self.get_node_info().await.map_err(|e| {
            tracing::warn!("获取节点信息失败: {}", e);
            e
        })?;
        tracing::debug!("获取节点信息成功: node_id={}, ip={}", node_id, ip);

        // 获取主机名
        tracing::debug!("获取主机名");
        let hostname = self.get_hostname().await.unwrap_or_else(|e| {
            tracing::warn!("获取主机名失败: {}, 使用默认值", e);
            "localhost".to_string()
        });
        tracing::debug!("获取主机名成功: hostname={}", hostname);

        // 获取操作系统信息
        tracing::debug!("获取操作系统信息");
        let os_info = self.get_os_info().await.unwrap_or_else(|e| {
            tracing::warn!("获取操作系统信息失败: {}, 使用默认值", e);
            "unknown".to_string()
        });
        tracing::debug!("获取操作系统信息成功: os_info={}", os_info);

        // 获取 Nomad 任务列表
        tracing::debug!("获取 Nomad 任务列表");
        let tasks = self.get_nomad_tasks().await.unwrap_or_else(|e| {
            tracing::warn!("获取 Nomad 任务失败: {}, 返回空列表", e);
            vec![]
        });

        // 判断节点是否空闲 (没有运行中的任务)
        let idle = tasks.is_empty();
        tracing::debug!("节点是否空闲: idle={}, 任务数={}", idle, tasks.len());

        Ok(AgentResponse::NodeStatus {
            trace_id,
            idle,
            tasks,
            node_info: NodeInfo {
                node_id,
                ip_address: ip,
                hostname,
                os_info,
            },
        })
    }

    /// 获取节点信息
    async fn get_node_info(&self) -> Result<(String, String), HandlerError> {
        // 获取本地 IP 地址，复用 tailscale 模块的函数
        let ip = crate::tailscale::wait_for_ipv4().await.map_err(|e| {
            tracing::warn!("获取 Tailscale IP 失败: {}", e);
            anyhow::anyhow!(e)
        })?;
        // 获取 Nomad 节点 ID
        let node_id = self
            .get_nomad_node_id(&ip)
            .await
            .map_err(|e| HandlerError::Internal(format!("获取 Nomad 节点 ID 失败: {}", e)))?;

        Ok((node_id, ip))
    }

    async fn get_hostname(&self) -> Result<String, HandlerError> {
        // 尝试多种方法获取主机名
        // 1. 优先使用 gethostname 系统调用
        if let Ok(hostname) = self.get_hostname_syscall() {
            if !hostname.is_empty() {
                return Ok(hostname);
            }
        }

        // 2. 尝试使用 hostname 命令
        if let Ok(hostname) = self.get_hostname_command().await {
            if !hostname.is_empty() {
                return Ok(hostname);
            }
        }

        // 3. 尝试从环境变量获取
        if let Ok(hostname) = std::env::var("HOSTNAME") {
            if !hostname.is_empty() {
                return Ok(hostname);
            }
        }

        // 4. 最后的后备方案
        tracing::warn!("所有获取主机名的方法都失败，使用默认主机名");
        Ok("localhost".to_string())
    }

    /// 使用系统调用获取主机名
    fn get_hostname_syscall(&self) -> Result<String, std::io::Error> {
        unsafe {
            let mut buf = [0u8; 256];
            let result = libc::gethostname(buf.as_mut_ptr() as *mut libc::c_char, buf.len());

            if result == 0 {
                let len = buf.iter().position(|&x| x == 0).unwrap_or(buf.len());
                let hostname = std::str::from_utf8(&buf[..len])
                    .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;
                Ok(hostname.to_string())
            } else {
                Err(std::io::Error::last_os_error())
            }
        }
    }

    /// 使用命令获取主机名
    async fn get_hostname_command(&self) -> Result<String, std::io::Error> {
        let output = Command::new("hostname").output().await?;

        if output.status.success() {
            let hostname = String::from_utf8_lossy(&output.stdout).trim().to_string();
            Ok(hostname)
        } else {
            Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                "hostname command failed",
            ))
        }
    }

    async fn get_os_info(&self) -> Result<String, HandlerError> {
        // 尝试多种方法获取操作系统信息

        // 1. 优先读取 /etc/os-release (标准Linux方法)
        if let Ok(os_info) = self.get_os_info_from_release().await {
            return Ok(os_info);
        }

        // 2. 尝试使用 uname 命令获取基本信息
        if let Ok(os_info) = self.get_os_info_from_uname().await {
            return Ok(os_info);
        }

        // 3. 最后的后备方案 - 基本系统信息
        let platform = std::env::consts::OS;
        let arch = std::env::consts::ARCH;
        let os_info = format!("{} {}", platform, arch);

        tracing::warn!("使用基本系统信息作为OS信息: {}", os_info);
        Ok(os_info)
    }

    /// 从 /etc/os-release 读取操作系统信息
    async fn get_os_info_from_release(&self) -> Result<String, std::io::Error> {
        use tokio::fs;

        let content = fs::read_to_string("/etc/os-release").await?;

        let mut name = None;
        let mut version = None;

        for line in content.lines() {
            if let Some(value) = line.strip_prefix("PRETTY_NAME=") {
                name = Some(value.trim_matches('"').to_string());
            } else if let Some(value) = line.strip_prefix("VERSION=") {
                version = Some(value.trim_matches('"').to_string());
            }
        }

        match (name, version) {
            (Some(name), Some(version)) => Ok(format!("{} {}", name, version)),
            (Some(name), None) => Ok(name),
            _ => Err(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                "Could not parse OS release info",
            )),
        }
    }

    /// 使用 uname 命令获取系统信息
    async fn get_os_info_from_uname(&self) -> Result<String, std::io::Error> {
        let output = Command::new("uname")
            .args(&["-srm"]) // 系统名、发行版、机器类型
            .output()
            .await?;

        if output.status.success() {
            let info = String::from_utf8_lossy(&output.stdout).trim().to_string();
            Ok(info)
        } else {
            Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                "uname command failed",
            ))
        }
    }

    async fn get_nomad_tasks(&self) -> anyhow::Result<Vec<TaskInfo>> {
        // 获取当前节点上运行的所有任务分配
        let output = Command::new("/opt/nomad/nomad")
            .args(&["alloc", "status", "-json"])
            .output()
            .await?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            tracing::warn!("Nomad alloc status 命令失败: {}", stderr);

            // 如果没有运行的任务，nomad 可能返回非零退出码，这是正常的
            return Ok(vec![]);
        }

        let output_str = String::from_utf8(output.stdout)?;

        // 如果输出为空或者是空数组，返回空的任务列表
        if output_str.trim().is_empty() || output_str.trim() == "[]" {
            return Ok(vec![]);
        }

        // 解析 Nomad 分配信息并转换为 TaskInfo
        let allocations: Vec<serde_json::Value> = serde_json::from_str(&output_str)?;

        let mut tasks = Vec::new();
        for alloc in allocations {
            // 只包含正在运行的任务
            if let Some(client_status) = alloc["ClientStatus"].as_str() {
                if client_status == "running" {
                    let task_info = TaskInfo {
                        task_id: alloc["ID"].as_str().unwrap_or("unknown").to_string(),
                        status: client_status.to_string(),
                        allocated_resources: ResourceInfo {
                            cpu: alloc["Resources"]["CPU"].as_f64().unwrap_or(0.0),
                            memory: alloc["Resources"]["MemoryMB"].as_u64().unwrap_or(0),
                            disk: alloc["Resources"]["DiskMB"].as_u64().unwrap_or(0),
                        },
                    };
                    tasks.push(task_info);
                }
            }
        }

        Ok(tasks)
    }

    /// 获取 Nomad 节点 ID
    async fn get_nomad_node_id(&self, nomad_address: &str) -> anyhow::Result<String> {
        let output = Command::new("/opt/nomad/nomad")
            .args(&[
                "agent-info",
                "-json",
                &format!("--address=http://{}:4646", nomad_address),
            ])
            .output()
            .await?;

        if !output.status.success() {
            let stderr = if !output.stderr.is_empty() {
                String::from_utf8_lossy(&output.stderr).to_string()
            } else {
                String::from_utf8_lossy(&output.stdout).to_string()
            };
            tracing::error!(
                "Nomad node status 命令失败: {} ，退出码: {}",
                stderr,
                output.status.code().unwrap_or(-1)
            );
            return Err(anyhow::anyhow!("Nomad 命令执行失败"));
        }

        let output_str = String::from_utf8(output.stdout)?;
        let node_info: AgentInfoObj = serde_json::from_str(&output_str)?;

        Ok(node_info.stats.client.node_id)
    }

    #[tracing::instrument(skip(self))]
    async fn handle_get_network_latency(
        &self,
        trace_id: Uuid,
    ) -> Result<AgentResponse, HandlerError> {
        tracing::debug!("获取网络延迟信息");

        // 默认使用 EchoWave 服务器 IP 地址 (示例，需要根据实际情况调整)
        let server_ip = "**********"; // 这里应该是实际的服务器 Tailscale IP

        let latency_info = match crate::tailscale::measure_server_latency(server_ip).await {
            Ok(latency) => LatencyInfo {
                server_latency: Some(latency),
                timestamp: Utc::now().timestamp(),
                error: None,
            },
            Err(e) => {
                tracing::error!("测量网络延迟失败: {}", e);
                LatencyInfo {
                    server_latency: None,
                    timestamp: Utc::now().timestamp(),
                    error: Some(e.to_string()),
                }
            }
        };

        Ok(AgentResponse::NetworkLatency {
            trace_id,
            latency_info,
        })
    }

    #[tracing::instrument(skip(self))]
    async fn handle_get_health_status(
        &self,
        trace_id: Uuid,
    ) -> Result<AgentResponse, HandlerError> {
        tracing::debug!("获取健康状态");

        Ok(AgentResponse::HealthStatus {
            trace_id,
            healthy: true,
            version: env!("CARGO_PKG_VERSION").to_string(),
        })
    }

    /// 处理数据块写入
    ///
    /// 此方法在独立的 tokio 任务中调用，避免阻塞主通信循环
    pub async fn handle_chunk(&self, chunk: ChunkData<'_>) -> anyhow::Result<()> {
        let mut managers = self.transfer_managers.lock().await;

        let manager = managers
            .get_mut(&chunk.transfer_id)
            .ok_or_else(|| anyhow::anyhow!("未找到传输任务: {}", chunk.transfer_id))?;

        // 将数据写入管理器
        let all_received = manager.write_chunk(chunk)?;

        if all_received {
            tracing::info!(
                transfer_id = manager.transfer_id,
                trace_id = %manager.trace_id,
                "所有分块接收完成，开始验证"
            );

            // 获取必要的信息和发送器引用
            let trace_id = manager.trace_id;
            let transfer_id = manager.transfer_id;
            let message_sender = self.message_sender.clone();

            // 验证文件完整性
            let verification_result = manager.verify_and_complete().await;

            // 发送 TransferCompleted 事件告知 Domain 结果
            if let Some(sender) = message_sender {
                let transfer_completed_event =
                    AgentEvent::Transfer(TransferEvent::TransferCompleted {
                        trace_id,
                        transfer_id,
                        success: verification_result.is_ok(),
                        error_message: verification_result.as_ref().err().map(|e| e.to_string()),
                    });

                // 使用 operation_id 0 作为主动推送事件的标识
                if let Err(e) = sender.send_event(0, false, &transfer_completed_event).await {
                    tracing::error!(
                        transfer_id = transfer_id,
                        trace_id = %trace_id,
                        error = %e,
                        "发送 TransferCompleted 事件失败"
                    );
                }
            }

            // 处理验证结果
            match verification_result {
                Ok(()) => {
                    tracing::info!(
                        transfer_id = transfer_id,
                        trace_id = %trace_id,
                        "文件传输完成并验证成功"
                    );
                }
                Err(e) => {
                    tracing::error!(
                        transfer_id = transfer_id,
                        trace_id = %trace_id,
                        error = %e,
                        "文件验证失败"
                    );
                    return Err(e);
                }
            }
        }

        Ok(())
    }

    /// 处理自我更新命令
    async fn handle_start_self_update(
        &self,
        trace_id: Uuid,
        transfer_id: u32,
        version: String,
    ) -> Result<AgentResponse, HandlerError> {
        tracing::info!(
            trace_id = %trace_id,
            transfer_id = transfer_id,
            version = %version,
            "开始 Agent 自我更新"
        );

        // 检查是否有完成的传输任务
        let managers = self.transfer_managers.lock().await;
        let manager = managers
            .get(&transfer_id)
            .ok_or_else(|| HandlerError::Internal(format!("未找到传输任务: {}", transfer_id)))?;

        if !manager.is_completed() {
            return Err(HandlerError::Internal(
                "文件传输尚未完成，无法进行更新".to_string(),
            ));
        }

        let temp_path = manager.temp_path();
        drop(managers); // 释放锁

        // 获取消息发送器引用
        let message_sender = self.message_sender.clone();

        // 在独立的任务中执行更新逻辑
        let trace_id_clone = trace_id;
        let transfer_id_clone = transfer_id;
        tokio::spawn(async move {
            let success = match Self::perform_self_update(&temp_path).await {
                Ok(()) => {
                    tracing::info!(
                        trace_id = %trace_id_clone,
                        transfer_id = transfer_id_clone,
                        "自我更新成功，等待重启"
                    );
                    true
                }
                Err(e) => {
                    tracing::error!(
                        trace_id = %trace_id_clone,
                        transfer_id = transfer_id_clone,
                        error = %e,
                        "自我更新失败"
                    );
                    false
                }
            };

            // 发送 SelfUpdateReady 事件
            if let Some(sender) = message_sender {
                let update_ready_event = AgentEvent::SelfUpdateReady {
                    trace_id: trace_id_clone,
                    transfer_id: transfer_id_clone,
                    success,
                    error_message: if success {
                        None
                    } else {
                        Some("文件替换失败".to_string())
                    },
                };

                // 使用 operation_id 0 作为主动推送事件的标识
                if let Err(e) = sender.send_event(0, false, &update_ready_event).await {
                    tracing::error!(
                        trace_id = %trace_id_clone,
                        transfer_id = transfer_id_clone,
                        error = %e,
                        "发送 SelfUpdateReady 事件失败"
                    );
                }
            } else {
                tracing::warn!(
                    trace_id = %trace_id_clone,
                    transfer_id = transfer_id_clone,
                    "消息发送器未设置，无法通知更新结果"
                );
            }
        });

        Ok(AgentResponse::Success { trace_id })
    }

    /// 执行实际的更新操作
    async fn perform_self_update(new_agent_path: &std::path::Path) -> anyhow::Result<()> {
        use tokio::fs;

        // 获取当前可执行文件路径
        let current_exe = std::env::current_exe()?;
        let backup_path = current_exe.with_extension("bak");

        tracing::info!(
            current_exe = %current_exe.display(),
            new_agent = %new_agent_path.display(),
            backup = %backup_path.display(),
            "准备执行文件替换"
        );

        // 1. 备份当前的执行文件
        if backup_path.exists() {
            fs::remove_file(&backup_path)
                .await
                .map_err(|e| anyhow::anyhow!("删除旧备份失败: {}", e))?;
        }

        fs::rename(&current_exe, &backup_path)
            .await
            .map_err(|e| anyhow::anyhow!("备份当前执行文件失败: {}", e))?;

        tracing::info!("当前执行文件已备份");

        // 2. 复制新的执行文件
        fs::copy(new_agent_path, &current_exe).await.map_err(|e| {
            // 如果复制失败，尝试恢复备份
            let _ = std::fs::rename(&backup_path, &current_exe);
            anyhow::anyhow!("复制新执行文件失败: {}", e)
        })?;

        tracing::info!("新执行文件已复制");

        // 3. 设置执行权限
        #[cfg(unix)]
        {
            use std::fs::Permissions;
            use std::os::unix::fs::PermissionsExt;
            let perms = Permissions::from_mode(0o755);
            fs::set_permissions(&current_exe, perms)
                .await
                .map_err(|e| anyhow::anyhow!("设置执行权限失败: {}", e))?;

            tracing::info!("新执行文件权限已设置");
        }

        // 4. 清理临时文件
        if let Err(e) = fs::remove_file(new_agent_path).await {
            tracing::warn!(
                temp_file = %new_agent_path.display(),
                error = %e,
                "清理临时文件失败"
            );
        }

        tracing::info!("自我更新完成，等待重启");
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum HandlerError {
    #[error("不支持的事件: {0}")]
    UnsupportedEvent(&'static str),
    #[error("内部错误: {0}")]
    Internal(String),
}

impl HandlerError {
    pub fn code(&self) -> u32 {
        match self {
            HandlerError::UnsupportedEvent(_) => 1001,
            HandlerError::Internal(_) => 1000,
        }
    }
}

impl From<anyhow::Error> for HandlerError {
    fn from(e: anyhow::Error) -> Self {
        HandlerError::Internal(e.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_shutdown_handler() {
        let handler = AgentHandler::new();
        let trace_id = Uuid::new_v4();
        let event = AgentEvent::Shutdown { trace_id };

        let result = handler.handle(event).await;
        // 根据环境可能成功或失败，这里只检查不会panic
        assert!(result.is_ok() || result.is_err());
    }
}
