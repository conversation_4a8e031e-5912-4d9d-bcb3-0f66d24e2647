use regex::Regex;
use serde::Deserialize;
use std::sync::LazyLock;
use tokio::fs;

// Nomad 日志解析正则表达式
static NOMAD_LOG_LEVEL_REGEX: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\[(\w+)\]").unwrap());

static NOMAD_TIMESTAMP_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}").unwrap());

static NOMAD_MODULE_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"(\w+\.)*(\w+):").unwrap());

pub const HCL_PATH: &'static str = "/opt/nomad/nomad.hcl";

pub async fn write_hcl(host: &str, server: &str) -> anyhow::Result<()> {
    let hcl_content = include_str!("./nomad.hcl")
        .replace("{HOST}", host)
        .replace("{SERVER}", server);

    fs::write(HCL_PATH, hcl_content).await.map_err(|err| {
        tracing::error!("Failed to write nomad.hcl: {}", err);
        err
    })?;
    Ok(())
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub struct RuntimeStatusObj {
    pub version: serde_json::Value,
    pub max_procs: serde_json::Value,
    pub goroutines: serde_json::Value,
    pub cpu_count: serde_json::Value,
    #[serde(rename = "kernel.name")]
    pub kernel_name: serde_json::Value,
    pub arch: serde_json::Value,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub struct ClientStatusObj {
    // pub known_servers: String,
    // pub num_allocations: String,
    // pub last_heartbeat: String,
    // pub heartbeat_ttl: String,
    pub node_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub struct StatusObj {
    // pub runtime: RuntimeStatusObj,
    pub client: ClientStatusObj,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct VersionObj {
    pub build_date: String,
    pub revision: String,
    pub version: String,
    pub version_metadata: String,
    pub version_prerelease: String,
}

// 新增的结构体定义开始

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct AuditObj {
    pub enabled: Option<bool>,
    pub filters: Option<serde_json::Value>,
    pub sinks: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct LimitsObj {
    #[serde(rename = "HTTPMaxConnsPerClient")]
    pub http_max_conns_per_client: f64,
    #[serde(rename = "HTTPSHandshakeTimeout")]
    pub https_handshake_timeout: String,
    #[serde(rename = "RPCHandshakeTimeout")]
    pub rpc_handshake_timeout: String,
    #[serde(rename = "RPCMaxConnsPerClient")]
    pub rpc_max_conns_per_client: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct PluginConfigObj {
    pub enabled: bool,
    pub fingerprint_period: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct PluginObj {
    pub name: String,
    pub args: Option<serde_json::Value>,
    pub config: PluginConfigObj,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct TLSConfigObj {
    #[serde(rename = "EnableRPC")]
    pub enable_rpc: bool,
    pub key_file: String,
    pub key_loader: serde_json::Value,
    #[serde(rename = "RPCUpgradeMode")]
    pub rpc_upgrade_mode: bool,
    #[serde(rename = "TLSMinVersion")]
    pub tls_min_version: String,
    #[serde(rename = "VerifyHTTPSClient")]
    pub verify_https_client: bool,
    pub cert_file: String,
    #[serde(rename = "EnableHTTP")]
    pub enable_http: bool,
    #[serde(rename = "TLSCipherSuites")]
    pub tls_cipher_suites: String,
    pub verify_server_hostname: bool,
    #[serde(rename = "CAFile")]
    pub ca_file: String,
    pub checksum: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct VaultUIObj {
    #[serde(rename = "BaseUIURL")]
    pub base_ui_url: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ConsulUIObj {
    #[serde(rename = "BaseUIURL")]
    pub base_ui_url: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ContentSecurityPolicyObj {
    pub img_src: Vec<String>,
    pub script_src: Vec<String>,
    pub style_src: Vec<String>,
    pub connect_src: Vec<String>,
    pub default_src: Vec<String>,
    pub form_action: Vec<String>,
    pub frame_ancestors: Vec<String>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct UILabelObj {
    pub background_color: String,
    pub text: String,
    pub text_color: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct UIObj {
    pub vault: VaultUIObj,
    pub consul: ConsulUIObj,
    pub content_security_policy: ContentSecurityPolicyObj,
    pub enabled: bool,
    pub label: UILabelObj,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ACLObj {
    pub replication_token: String,
    pub role_ttl: f64,
    pub token_max_expiration_ttl: f64,
    pub token_min_expiration_ttl: f64,
    pub token_ttl: f64,
    pub enabled: bool,
    pub policy_ttl: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct AddressesObj {
    #[serde(rename = "HTTP")]
    pub http: String,
    #[serde(rename = "RPC")]
    pub rpc: String,
    pub serf: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct PortsObj {
    #[serde(rename = "HTTP")]
    pub http: f64,
    #[serde(rename = "RPC")]
    pub rpc: f64,
    pub serf: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
#[allow(dead_code)]
pub struct TelemetryObj {
    pub circonus_check_instance_id: String,
    pub filter_default: Option<serde_json::Value>,
    pub circonus_api_app: String,
    pub circonus_api_token: String,
    pub publish_allocation_metrics: bool,
    pub publish_node_metrics: bool,
    pub data_dog_tags: Option<serde_json::Value>,
    pub disable_dispatched_job_summary_metrics: bool,
    pub disable_rpc_rate_metrics_labels: bool,
    pub statsd_addr: String,
    pub prometheus_metrics: bool,
    pub circonus_broker_id: String,
    pub data_dog_addr: String,
    pub collection_interval: String,
    pub statsite_addr: String,
    pub use_node_name: bool,
    pub circonus_check_id: String,
    pub include_alloc_metadata_in_metrics: bool,
    pub prefix_filter: Option<serde_json::Value>,
    pub in_memory_retention_period: String,
    pub circonus_api_url: String,
    pub circonus_check_search_tag: String,
    pub circonus_check_submission_url: String,
    pub circonus_submission_interval: String,
    pub disable_hostname: bool,
    pub disable_quota_utilization_metrics: bool,
    pub circonus_broker_select_tag: String,
    pub circonus_check_force_metric_activation: String,
    pub circonus_check_tags: String,
    pub disable_allocation_hook_metrics: bool,
    pub in_memory_collection_interval: String,
    pub allowed_metadata_keys_in_metrics: Option<serde_json::Value>,
    pub circonus_check_display_name: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct AutopilotObj {
    pub enable_custom_upgrades: Option<serde_json::Value>,
    pub enable_redundancy_zones: Option<serde_json::Value>,
    pub last_contact_threshold: f64,
    pub max_trailing_logs: f64,
    pub min_quorum: f64,
    pub server_stabilization_time: f64,
    pub cleanup_dead_servers: Option<serde_json::Value>,
    pub disable_upgrade_migration: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct UsersObj {
    pub max_dynamic_user: f64,
    pub min_dynamic_user: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ArtifactObj {
    pub decompression_size_limit: String,
    pub filesystem_isolation_extra_paths: Option<serde_json::Value>,
    #[serde(rename = "GCSTimeout")]
    pub gcs_timeout: String,
    #[serde(rename = "HTTPMaxSize")]
    pub http_max_size: String,
    #[serde(rename = "S3Timeout")]
    pub s3_timeout: String,
    pub set_environment_variables: String,
    pub disable_filesystem_isolation: bool,
    pub git_timeout: String,
    #[serde(rename = "HTTPReadTimeout")]
    pub http_read_timeout: String,
    pub hg_timeout: String,
    pub decompression_file_count_limit: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ReservedObj {
    #[serde(rename = "CPU")]
    pub cpu: f64,
    pub cores: String,
    pub disk_mb: f64,
    pub memory_mb: f64,
    pub reserved_ports: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct NomadRetryObj {
    pub max_backoff: f64,
    #[serde(rename = "MaxBackoffHCL")]
    pub max_backoff_hcl: String,
    pub attempts: f64,
    pub backoff: f64,
    #[serde(rename = "BackoffHCL")]
    pub backoff_hcl: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct WaitObj {
    pub max: f64,
    #[serde(rename = "MaxHCL")]
    pub max_hcl: String,
    pub min: f64,
    #[serde(rename = "MinHCL")]
    pub min_hcl: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct VaultRetryObj {
    #[serde(rename = "MaxBackoffHCL")]
    pub max_backoff_hcl: String,
    pub attempts: f64,
    pub backoff: f64,
    #[serde(rename = "BackoffHCL")]
    pub backoff_hcl: String,
    pub max_backoff: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ConsulRetryObj {
    pub attempts: f64,
    pub backoff: f64,
    #[serde(rename = "BackoffHCL")]
    pub backoff_hcl: String,
    pub max_backoff: f64,
    #[serde(rename = "MaxBackoffHCL")]
    pub max_backoff_hcl: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct TemplateConfigObj {
    pub max_stale: f64,
    pub nomad_retry: NomadRetryObj,
    pub wait: WaitObj,
    pub block_query_wait_time: f64,
    #[serde(rename = "BlockQueryWaitTimeHCL")]
    pub block_query_wait_time_hcl: String,
    pub function_denylist: Vec<String>,
    #[serde(rename = "MaxStaleHCL")]
    pub max_stale_hcl: String,
    pub vault_retry: VaultRetryObj,
    pub wait_bounds: Option<serde_json::Value>,
    pub consul_retry: ConsulRetryObj,
    pub disable_sandbox: bool,
    pub function_blacklist: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ServerJoinObj {
    pub retry_interval: f64,
    pub retry_join: Vec<serde_json::Value>,
    pub retry_max_attempts: f64,
    pub start_join: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ClientObj {
    pub disk_free_mb: f64,
    #[serde(rename = "GCDiskUsageThreshold")]
    pub gc_disk_usage_threshold: f64,
    pub users: UsersObj,
    #[serde(rename = "CNIPath")]
    pub cni_path: String,
    pub alloc_dir: String,
    pub alloc_mounts_dir: String,
    pub artifact: ArtifactObj,
    pub bind_wildcard_default_host_network: bool,
    pub drain: Option<serde_json::Value>,
    pub max_dynamic_port: f64,
    pub network_interface: String,
    pub no_host_uuid: bool,
    pub host_volumes: Option<serde_json::Value>,
    pub client_min_port: f64,
    pub disk_total_mb: f64,
    pub enabled: bool,
    pub memory_mb: f64,
    pub node_pool: String,
    pub nomad_service_discovery: bool,
    pub reserved: ReservedObj,
    pub chroot_env: serde_json::Value,
    #[serde(rename = "GCInodeUsageThreshold")]
    pub gc_inode_usage_threshold: f64,
    #[serde(rename = "GCMaxAllocs")]
    pub gc_max_allocs: f64,
    pub host_networks: Option<serde_json::Value>,
    pub min_dynamic_port: f64,
    pub network_speed: f64,
    pub reservable_cores: String,
    pub state_dir: String,
    pub client_max_port: f64,
    pub cpu_compute: f64,
    pub cpu_disable_dmidecode: bool,
    pub disable_remote_exec: bool,
    #[serde(rename = "GCParallelDestroys")]
    pub gc_parallel_destroys: f64,
    pub node_class: String,
    pub options: serde_json::Value,
    pub template_config: TemplateConfigObj,
    pub bridge_network_subnet: String,
    pub bridge_network_subnet_ipv6: String,
    pub max_kill_timeout: String,
    pub preferred_address_family: String,
    pub servers: Vec<String>,
    pub bridge_network_hairpin_mode: bool,
    pub bridge_network_name: String,
    #[serde(rename = "CNIConfigDir")]
    pub cni_config_dir: String,
    pub cgroup_parent: String,
    #[serde(rename = "GCInterval")]
    pub gc_interval: f64,
    pub meta: serde_json::Value,
    pub server_join: ServerJoinObj,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct AdvertiseAddrsObj {
    pub serf: String,
    #[serde(rename = "HTTP")]
    pub http: String,
    #[serde(rename = "RPC")]
    pub rpc: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct SentinelObj {
    pub imports: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
#[allow(dead_code)]
pub struct PlanRejectionTrackerObj {
    pub enabled: bool,
    pub node_threshold: f64,
    pub node_window: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
#[allow(dead_code)]
pub struct SearchObj {
    pub fuzzy_enabled: bool,
    pub limit_query: f64,
    pub limit_results: f64,
    pub min_term_length: f64,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ServerObj {
    pub job_max_priority: Option<serde_json::Value>,
    pub start_join: Vec<serde_json::Value>,
    pub default_scheduler_config: Option<serde_json::Value>,
    pub enable_event_broker: bool,
    pub eval_gc_threshold: Option<String>,
    pub job_max_source_size: String,
    pub non_voting_server: bool,
    pub raft_multiplier: Option<serde_json::Value>,
    pub raft_protocol: f64,
    pub raft_snapshot_interval: Option<serde_json::Value>,
    pub event_buffer_size: f64,
    pub heartbeat_grace: f64,
    pub license_env: String,
    pub raft_bolt_config: Option<serde_json::Value>,
    pub raft_snapshot_threshold: Option<serde_json::Value>,
    pub redundancy_zone: String,
    pub rejoin_after_leave: bool,
    pub root_key_gc_threshold: String,
    pub min_heartbeat_ttl: f64,
    pub authoritative_region: String,
    #[serde(rename = "CSIPluginGCThreshold")]
    pub csi_plugin_gc_threshold: String,
    #[serde(rename = "CSIVolumeClaimGCThreshold")]
    pub csi_volume_claim_gc_threshold: String,
    pub job_default_priority: Option<serde_json::Value>,
    pub num_schedulers: Option<serde_json::Value>,
    pub raft_trailing_logs: Option<serde_json::Value>,
    pub retry_interval: f64,
    #[serde(rename = "ACLTokenGCThreshold")]
    pub acl_token_gc_threshold: String,
    pub data_dir: String,
    pub deployment_gc_threshold: String,
    pub job_gc_interval: String,
    pub job_gc_threshold: String,
    pub job_tracked_versions: f64,
    pub max_heartbeats_per_second: f64,
    pub node_gc_threshold: String,
    pub failover_heartbeat_ttl: f64,
    #[serde(rename = "OIDCIssuer")]
    pub oidc_issuer: String,
    pub plan_rejection_tracker: PlanRejectionTrackerObj,
    pub retry_join: Vec<serde_json::Value>,
    pub retry_max_attempts: f64,
    pub search: SearchObj,
    pub server_join: ServerJoinObj,
    pub batch_eval_gc_threshold: String,
    #[serde(rename = "CSIVolumeClaimGCInterval")]
    pub csi_volume_claim_gc_interval: String,
    pub license_path: String,
    pub root_key_gc_interval: String,
    pub upgrade_version: String,
    pub enabled_schedulers: Option<serde_json::Value>,
    pub bootstrap_expect: f64,
    pub deployment_query_rate_limit: f64,
    pub enabled: bool,
    pub root_key_rotation_threshold: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct VaultObj {
    pub connection_retry_intv: f64,
    pub default_identity: Option<serde_json::Value>,
    #[serde(rename = "TLSCaPath")]
    pub tls_ca_path: String,
    #[serde(rename = "TLSSkipVerify")]
    pub tls_skip_verify: Option<serde_json::Value>,
    pub token: String,
    pub role: String,
    #[serde(rename = "TLSCaFile")]
    pub tls_ca_file: String,
    #[serde(rename = "TLSKeyFile")]
    pub tls_key_file: String,
    #[serde(rename = "TLSServerName")]
    pub tls_server_name: String,
    pub addr: String,
    pub allow_unauthenticated: bool,
    pub enabled: Option<serde_json::Value>,
    pub namespace: String,
    #[serde(rename = "TLSCertFile")]
    pub tls_cert_file: String,
    pub task_token_ttl: String,
    #[serde(rename = "JWTAuthBackendPath")]
    pub jwt_auth_backend_path: String,
    pub name: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct LicenseObj {
    pub enabled: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ReportingObj {
    pub export_interval: f64,
    pub license: LicenseObj,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ConsulObj {
    #[serde(rename = "GRPCAddr")]
    pub grpc_addr: String,
    #[serde(rename = "VerifySSL")]
    pub verify_ssl: bool,
    pub allow_unauthenticated: bool,
    pub auto_advertise: bool,
    pub client_auto_join: bool,
    pub key_file: String,
    pub addr: String,
    pub auth: String,
    pub client_failures_before_warning: f64,
    #[serde(rename = "ClientHTTPCheckName")]
    pub client_http_check_name: String,
    pub name: String,
    pub server_failures_before_warning: f64,
    #[serde(rename = "ServerHTTPCheckName")]
    pub server_http_check_name: String,
    pub service_identity: Option<serde_json::Value>,
    #[serde(rename = "CAFile")]
    pub ca_file: String,
    pub checks_use_advertise: bool,
    pub client_failures_before_critical: f64,
    pub client_service_name: String,
    pub service_identity_auth_method: String,
    #[serde(rename = "ShareSSL")]
    pub share_ssl: Option<serde_json::Value>,
    pub server_serf_check_name: String,
    pub server_service_name: String,
    pub tags: Option<serde_json::Value>,
    pub timeout: f64,
    pub namespace: String,
    pub server_auto_join: bool,
    #[serde(rename = "ServerRPCCheckName")]
    pub server_rpc_check_name: String,
    pub server_failures_before_critical: f64,
    pub task_identity: Option<serde_json::Value>,
    pub token: String,
    #[serde(rename = "EnableSSL")]
    pub enable_ssl: bool,
    pub cert_file: String,
    #[serde(rename = "GRPCCAFile")]
    pub grpc_ca_file: String,
    pub task_identity_auth_method: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ConfigObj {
    pub audit: AuditObj,
    pub datacenter: String,
    pub disable_anonymous_signature: bool,
    pub limits: LimitsObj,
    pub plugins: Vec<PluginObj>,
    pub syslog_facility: String,
    #[serde(rename = "TLSConfig")]
    pub tls_config: TLSConfigObj,
    #[serde(rename = "UI")]
    pub ui: UIObj,
    #[serde(rename = "ACL")]
    pub acl: ACLObj,
    pub addresses: AddressesObj,
    pub dev_mode: bool,
    pub log_rotate_duration: String,
    pub ports: PortsObj,
    pub telemetry: TelemetryObj,
    pub log_file: String,
    pub enable_debug: bool,
    pub autopilot: AutopilotObj,
    pub client: ClientObj,
    pub disable_update_check: bool,
    pub enable_syslog: bool,
    pub leave_on_int: bool,
    pub log_include_location: bool,
    pub log_json: bool,
    pub log_level: String,
    pub log_rotate_max_files: f64,
    pub node_name: String,
    pub advertise_addrs: AdvertiseAddrsObj,
    #[serde(rename = "HTTPAPIResponseHeaders")]
    pub http_api_response_headers: serde_json::Value,
    pub leave_on_term: bool,
    pub version: VersionObj,
    pub sentinel: SentinelObj,
    pub server: ServerObj,
    pub vaults: Vec<VaultObj>,
    pub data_dir: String,
    pub files: Vec<String>,
    #[serde(rename = "KEKProviders")]
    pub kek_providers: Option<serde_json::Value>,
    pub reporting: ReportingObj,
    pub bind_addr: String,
    pub consuls: Vec<ConsulObj>,
    pub log_rotate_bytes: f64,
    pub plugin_dir: String,
    pub region: String,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub struct MemberObj {
    pub addr: String,
    pub delegate_cur: u32,
    pub delegate_max: u32,
    pub delegate_min: u32,
    pub name: String,
    pub port: u32,
    pub protocol_cur: u32,
    pub protocol_max: u32,
    pub protocol_min: u32,
    pub status: String,
    pub tags: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub struct StatsObj {
    pub client: ClientStatusObj,
    pub runtime: RuntimeStatusObj,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "snake_case")]
pub struct AgentInfoObj {
    // pub config: ConfigObj,
    // pub member: MemberObj,
    pub stats: StatsObj,
}

/// Nomad 专用日志解析器
pub struct NomadLogParser;

impl NomadLogParser {
    /// 解析 Nomad 日志级别
    /// 典型格式: 2024-06-30T10:30:45.123Z [INFO]  nomad.client: starting client
    pub fn parse_log_level(message: &str) -> String {
        // 首先尝试从 [LEVEL] 中提取
        if let Some(captures) = NOMAD_LOG_LEVEL_REGEX.captures(message) {
            if let Some(level) = captures.get(1) {
                return level.as_str().to_uppercase();
            }
        }

        // 根据内容推断
        let message_lower = message.to_lowercase();
        if message_lower.contains("error")
            || message_lower.contains("failed")
            || message_lower.contains("fatal")
        {
            "ERROR".to_string()
        } else if message_lower.contains("warn") || message_lower.contains("warning") {
            "WARN".to_string()
        } else if message_lower.contains("debug") {
            "DEBUG".to_string()
        } else if message_lower.contains("trace") {
            "TRACE".to_string()
        } else {
            "INFO".to_string()
        }
    }

    /// 解析 Nomad 日志时间戳
    pub fn parse_timestamp(message: &str) -> Option<String> {
        NOMAD_TIMESTAMP_REGEX
            .find(message)
            .map(|m| m.as_str().to_string())
    }

    /// 解析 Nomad 模块路径
    pub fn parse_module(message: &str) -> Option<String> {
        NOMAD_MODULE_REGEX
            .captures(message)
            .and_then(|cap| cap.get(2))
            .map(|m| m.as_str().to_string())
    }

    /// 完整解析 Nomad 日志
    pub fn parse_log(message: &str) -> NomadLogInfo {
        NomadLogInfo {
            level: Self::parse_log_level(message),
            timestamp: Self::parse_timestamp(message),
            module: Self::parse_module(message),
            raw_message: message.to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct NomadLogInfo {
    pub level: String,
    pub timestamp: Option<String>,
    pub module: Option<String>,
    pub raw_message: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    fn test_agent_info_obj_serde() {
        let json_str = r#"
        {
    "config": {
        "Audit": {
            "Enabled": null,
            "Filters": null,
            "Sinks": null
        },
        "Datacenter": "dc1",
        "DisableAnonymousSignature": false,
        "Limits": {
            "HTTPMaxConnsPerClient": 100.0,
            "HTTPSHandshakeTimeout": "5s",
            "RPCHandshakeTimeout": "5s",
            "RPCMaxConnsPerClient": 100.0
        },
        "Plugins": [
            {
                "Name": "nomad-device-nvidia",
                "Args": null,
                "Config": {
                    "enabled": true,
                    "fingerprint_period": "1m"
                }
            }
        ],
        "SyslogFacility": "LOCAL0",
        "TLSConfig": {
            "EnableRPC": false,
            "KeyFile": "",
            "KeyLoader": {},
            "RPCUpgradeMode": false,
            "TLSMinVersion": "",
            "VerifyHTTPSClient": false,
            "CertFile": "",
            "EnableHTTP": false,
            "TLSCipherSuites": "",
            "VerifyServerHostname": false,
            "CAFile": "",
            "Checksum": ""
        },
        "UI": {
            "Vault": {
                "BaseUIURL": ""
            },
            "Consul": {
                "BaseUIURL": ""
            },
            "ContentSecurityPolicy": {
                "ImgSrc": [
                    "'self'",
                    "data:"
                ],
                "ScriptSrc": [
                    "'self'"
                ],
                "StyleSrc": [
                    "'self'",
                    "'unsafe-inline'"
                ],
                "ConnectSrc": [
                    "*"
                ],
                "DefaultSrc": [
                    "'none'"
                ],
                "FormAction": [
                    "'none'"
                ],
                "FrameAncestors": [
                    "'none'"
                ]
            },
            "Enabled": true,
            "Label": {
                "BackgroundColor": "",
                "Text": "",
                "TextColor": ""
            }
        },
        "ACL": {
            "ReplicationToken": "",
            "RoleTTL": 30000000000.0,
            "TokenMaxExpirationTTL": 0.0,
            "TokenMinExpirationTTL": 0.0,
            "TokenTTL": 30000000000.0,
            "Enabled": false,
            "PolicyTTL": 30000000000.0
        },
        "Addresses": {
            "HTTP": "***********",
            "RPC": "***********",
            "Serf": "***********"
        },
        "DevMode": false,
        "LogRotateDuration": "",
        "Ports": {
            "HTTP": 4646.0,
            "RPC": 4647.0,
            "Serf": 4648.0
        },
        "Telemetry": {
            "CirconusCheckInstanceID": "",
            "FilterDefault": null,
            "CirconusAPIApp": "",
            "CirconusAPIToken": "",
            "PublishAllocationMetrics": false,
            "PublishNodeMetrics": false,
            "DataDogTags": null,
            "DisableDispatchedJobSummaryMetrics": false,
            "DisableRPCRateMetricsLabels": false,
            "StatsdAddr": "",
            "PrometheusMetrics": false,
            "CirconusBrokerID": "",
            "DataDogAddr": "",
            "CollectionInterval": "1s",
            "StatsiteAddr": "",
            "UseNodeName": false,
            "CirconusCheckID": "",
            "IncludeAllocMetadataInMetrics": false,
            "PrefixFilter": null,
            "InMemoryRetentionPeriod": "1m",
            "CirconusAPIURL": "",
            "CirconusCheckSearchTag": "",
            "CirconusCheckSubmissionURL": "",
            "CirconusSubmissionInterval": "",
            "DisableHostname": false,
            "DisableQuotaUtilizationMetrics": false,
            "CirconusBrokerSelectTag": "",
            "CirconusCheckForceMetricActivation": "",
            "CirconusCheckTags": "",
            "DisableAllocationHookMetrics": false,
            "InMemoryCollectionInterval": "10s",
            "AllowedMetadataKeysInMetrics": null,
            "CirconusCheckDisplayName": ""
        },
        "LogFile": "",
        "EnableDebug": false,
        "Autopilot": {
            "EnableCustomUpgrades": null,
            "EnableRedundancyZones": null,
            "LastContactThreshold": 200000000.0,
            "MaxTrailingLogs": 250.0,
            "MinQuorum": 0.0,
            "ServerStabilizationTime": 10000000000.0,
            "CleanupDeadServers": null,
            "DisableUpgradeMigration": null
        },
        "Client": {
            "DiskFreeMB": 0.0,
            "GCDiskUsageThreshold": 80.0,
            "Users": {
                "MaxDynamicUser": 89999.0,
                "MinDynamicUser": 80000.0
            },
            "CNIPath": "/opt/cni/bin",
            "AllocDir": "",
            "AllocMountsDir": "",
            "Artifact": {
                "DecompressionSizeLimit": "100GB",
                "FilesystemIsolationExtraPaths": null,
                "GCSTimeout": "30m",
                "HTTPMaxSize": "100GB",
                "S3Timeout": "30m",
                "SetEnvironmentVariables": "",
                "DisableFilesystemIsolation": false,
                "GitTimeout": "30m",
                "HTTPReadTimeout": "30m",
                "HgTimeout": "30m",
                "DecompressionFileCountLimit": 4096.0
            },
            "BindWildcardDefaultHostNetwork": true,
            "Drain": null,
            "MaxDynamicPort": 32000.0,
            "NetworkInterface": "",
            "NoHostUUID": true,
            "HostVolumes": null,
            "ClientMinPort": 14000.0,
            "DiskTotalMB": 0.0,
            "Enabled": true,
            "MemoryMB": 0.0,
            "NodePool": "default",
            "NomadServiceDiscovery": true,
            "Reserved": {
                "CPU": 0.0,
                "Cores": "",
                "DiskMB": 0.0,
                "MemoryMB": 0.0,
                "ReservedPorts": ""
            },
            "ChrootEnv": {},
            "GCInodeUsageThreshold": 70.0,
            "GCMaxAllocs": 50.0,
            "HostNetworks": null,
            "MinDynamicPort": 20000.0,
            "NetworkSpeed": 0.0,
            "ReservableCores": "",
            "StateDir": "",
            "ClientMaxPort": 14512.0,
            "CpuCompute": 0.0,
            "CpuDisableDmidecode": false,
            "DisableRemoteExec": false,
            "GCParallelDestroys": 2.0,
            "NodeClass": "",
            "Options": {},
            "TemplateConfig": {
                "MaxStale": 315360000000000000.0,
                "NomadRetry": {
                    "MaxBackoff": 60000000000.0,
                    "MaxBackoffHCL": "",
                    "Attempts": 12.0,
                    "Backoff": 250000000.0,
                    "BackoffHCL": ""
                },
                "Wait": {
                    "Max": 240000000000.0,
                    "MaxHCL": "",
                    "Min": 5000000000.0,
                    "MinHCL": ""
                },
                "BlockQueryWaitTime": 300000000000.0,
                "BlockQueryWaitTimeHCL": "",
                "FunctionDenylist": [
                    "executeTemplate",
                    "plugin",
                    "writeToFile"
                ],
                "MaxStaleHCL": "",
                "VaultRetry": {
                    "MaxBackoffHCL": "",
                    "Attempts": 12.0,
                    "Backoff": 250000000.0,
                    "BackoffHCL": "",
                    "MaxBackoff": 60000000000.0
                },
                "WaitBounds": null,
                "ConsulRetry": {
                    "Attempts": 12.0,
                    "Backoff": 250000000.0,
                    "BackoffHCL": "",
                    "MaxBackoff": 60000000000.0,
                    "MaxBackoffHCL": ""
                },
                "DisableSandbox": false,
                "FunctionBlacklist": null
            },
            "BridgeNetworkSubnet": "",
            "BridgeNetworkSubnetIPv6": "",
            "MaxKillTimeout": "30s",
            "PreferredAddressFamily": "",
            "Servers": [
                "**********:4647"
            ],
            "BridgeNetworkHairpinMode": false,
            "BridgeNetworkName": "",
            "CNIConfigDir": "/opt/cni/config",
            "CgroupParent": "",
            "GCInterval": 60000000000.0,
            "Meta": {},
            "ServerJoin": {
                "RetryInterval": 30000000000.0,
                "RetryJoin": [],
                "RetryMaxAttempts": 0.0,
                "StartJoin": null
            }
        },
        "DisableUpdateCheck": false,
        "EnableSyslog": false,
        "LeaveOnInt": false,
        "LogIncludeLocation": false,
        "LogJson": false,
        "LogLevel": "INFO",
        "LogRotateMaxFiles": 0.0,
        "NodeName": "",
        "AdvertiseAddrs": {
            "Serf": "",
            "HTTP": "***********:4646",
            "RPC": "***********:4647"
        },
        "HTTPAPIResponseHeaders": {},
        "LeaveOnTerm": false,
        "Version": {
            "BuildDate": "2025-03-11T09:07:15Z",
            "Revision": "f8695974efdbaa9f80ace06f0eec5bd4d2501035+CHANGES",
            "Version": "1.9.7",
            "VersionMetadata": "",
            "VersionPrerelease": ""
        },
        "Sentinel": {
            "Imports": null
        },
        "Server": {
            "JobMaxPriority": null,
            "StartJoin": [],
            "DefaultSchedulerConfig": null,
            "EnableEventBroker": true,
            "EvalGCThreshold": "",
            "JobMaxSourceSize": "1M",
            "NonVotingServer": false,
            "RaftMultiplier": null,
            "RaftProtocol": 3.0,
            "RaftSnapshotInterval": null,
            "EventBufferSize": 100.0,
            "HeartbeatGrace": 0.0,
            "LicenseEnv": "",
            "RaftBoltConfig": null,
            "RaftSnapshotThreshold": null,
            "RedundancyZone": "",
            "RejoinAfterLeave": false,
            "RootKeyGCThreshold": "",
            "MinHeartbeatTTL": 0.0,
            "AuthoritativeRegion": "",
            "CSIPluginGCThreshold": "",
            "CSIVolumeClaimGCThreshold": "",
            "JobDefaultPriority": null,
            "NumSchedulers": null,
            "RaftTrailingLogs": null,
            "RetryInterval": 0.0,
            "ACLTokenGCThreshold": "",
            "DataDir": "",
            "DeploymentGCThreshold": "",
            "JobGCInterval": "",
            "JobGCThreshold": "",
            "JobTrackedVersions": 6.0,
            "MaxHeartbeatsPerSecond": 0.0,
            "NodeGCThreshold": "",
            "FailoverHeartbeatTTL": 0.0,
            "OIDCIssuer": "",
            "PlanRejectionTracker": {
                "Enabled": false,
                "NodeThreshold": 100.0,
                "NodeWindow": 300000000000.0
            },
            "RetryJoin": [],
            "RetryMaxAttempts": 0.0,
            "Search": {
                "FuzzyEnabled": true,
                "LimitQuery": 20.0,
                "LimitResults": 100.0,
                "MinTermLength": 2.0
            },
            "ServerJoin": {
                "RetryInterval": 30000000000.0,
                "RetryJoin": [],
                "RetryMaxAttempts": 0.0,
                "StartJoin": null
            },
            "BatchEvalGCThreshold": "",
            "CSIVolumeClaimGCInterval": "",
            "LicensePath": "",
            "RootKeyGCInterval": "",
            "UpgradeVersion": "",
            "EnabledSchedulers": null,
            "BootstrapExpect": 0.0,
            "DeploymentQueryRateLimit": 0.0,
            "Enabled": false,
            "RootKeyRotationThreshold": ""
        },
        "Vaults": [
            {
                "ConnectionRetryIntv": 30000000000.0,
                "DefaultIdentity": null,
                "TLSCaPath": "",
                "TLSSkipVerify": null,
                "Token": "",
                "Role": "",
                "TLSCaFile": "",
                "TLSKeyFile": "",
                "TLSServerName": "",
                "Addr": "https://vault.service.consul:8200",
                "AllowUnauthenticated": true,
                "Enabled": null,
                "Namespace": "",
                "TLSCertFile": "",
                "TaskTokenTTL": "",
                "JWTAuthBackendPath": "jwt-nomad",
                "Name": "default"
            }
        ],
        "DataDir": "/opt/nomad/data",
        "Files": [
            "/opt/nomad/nomad.hcl"
        ],
        "KEKProviders": null,
        "Reporting": {
            "ExportInterval": 0.0,
            "License": {
                "Enabled": null
            }
        },
        "BindAddr": "***********",
        "Consuls": [
            {
                "GRPCAddr": "",
                "VerifySSL": true,
                "AllowUnauthenticated": true,
                "AutoAdvertise": true,
                "ClientAutoJoin": true,
                "KeyFile": "",
                "Addr": "127.0.0.1:8500",
                "Auth": "",
                "ClientFailuresBeforeWarning": 0.0,
                "ClientHTTPCheckName": "Nomad Client HTTP Check",
                "Name": "default",
                "ServerFailuresBeforeWarning": 0.0,
                "ServerHTTPCheckName": "Nomad Server HTTP Check",
                "ServiceIdentity": null,
                "CAFile": "",
                "ChecksUseAdvertise": false,
                "ClientFailuresBeforeCritical": 0.0,
                "ClientServiceName": "nomad-client",
                "ServiceIdentityAuthMethod": "nomad-workloads",
                "ShareSSL": null,
                "ServerSerfCheckName": "Nomad Server Serf Check",
                "ServerServiceName": "nomad",
                "Tags": null,
                "Timeout": 5000000000.0,
                "Namespace": "",
                "ServerAutoJoin": true,
                "ServerRPCCheckName": "Nomad Server RPC Check",
                "ServerFailuresBeforeCritical": 0.0,
                "TaskIdentity": null,
                "Token": "",
                "EnableSSL": false,
                "CertFile": "",
                "GRPCCAFile": "",
                "TaskIdentityAuthMethod": "nomad-workloads"
            }
        ],
        "LogRotateBytes": 0.0,
        "PluginDir": "/opt/nomad/data/plugins",
        "Region": "global"
    },
    "member": {
        "Addr": "",
        "DelegateCur": 0,
        "DelegateMax": 0,
        "DelegateMin": 0,
        "Name": "",
        "Port": 0,
        "ProtocolCur": 0,
        "ProtocolMax": 0,
        "ProtocolMin": 0,
        "Status": "none",
        "Tags": null
    },
    "stats": {
        "client": {
            "heartbeat_ttl": "14.195876075s",
            "node_id": "617319c7-10fd-c459-492a-dd95894d0cb5",
            "known_servers": "**********:4647",
            "num_allocations": "0",
            "last_heartbeat": "14.009528428s"
        },
        "runtime": {
            "cpu_count": "28",
            "kernel.name": "linux",
            "arch": "amd64",
            "version": "go1.24.1",
            "max_procs": "28",
            "goroutines": "65"
        }
    }
}
        "#;
        let agent_info_obj: AgentInfoObj = serde_json::from_str(json_str).unwrap();
        println!("{:?}", agent_info_obj);
    }
}
