//! Command execution utilities.

use std::{process::ExitStatus, time::Duration};
use tokio::process::{Child, Command as TokioCommand};

#[cfg(windows)]
use std::os::windows::process::CommandExt;

/// Creates a new async command with default settings for the current platform.
///
/// On Windows, this will create a command that does not open a new window.
pub fn create_command(program: &str) -> TokioCommand {
    #[cfg(windows)]
    {
        const CREATE_NO_WINDOW: u32 = 0x08000000;
        let mut command = TokioCommand::new(program);
        command.creation_flags(CREATE_NO_WINDOW);
        command
    }
    #[cfg(not(windows))]
    {
        let command = TokioCommand::new(program);
        command
    }
}

pub enum WaitForChildResult {
    Timeout,
    Exited(ExitStatus),
}

/// 等待子进程退出
///
/// ## 参数
/// - child: 子进程实例
/// - timeout: 超时
///
/// ## 返回
/// 如果为 `true` 则超时
pub async fn wait_for_child(
    child: &mut Child,
    timeout: Duration,
) -> Result<WaitForChildResult, std::io::Error> {
    tokio::select! {
        biased;
        status = child.wait() => status.map(WaitForChildResult::Exited),
        _ = tokio::time::sleep(timeout) => Ok(WaitForChildResult::Timeout),
    }
}
