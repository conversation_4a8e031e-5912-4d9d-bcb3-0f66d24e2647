use serde::{Deserialize, Serialize};
use std::path::Path;

/// 磁盘容量信息
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DiskCapacity {
    /// 总容量（字节）
    pub total_bytes: u64,
    /// 可用空间（字节）
    pub available_bytes: u64,
    /// 空闲空间（字节）
    pub free_bytes: u64,
    /// 已用空间（字节）
    pub used_bytes: u64,
    /// 使用率（百分比，0-100）
    pub usage_percentage: f64,
}

impl DiskCapacity {
    /// 创建新的磁盘容量信息实例
    pub fn new(total_bytes: u64, available_bytes: u64, free_bytes: u64) -> Self {
        let used_bytes = total_bytes.saturating_sub(free_bytes);
        let usage_percentage = if total_bytes > 0 {
            (used_bytes as f64 / total_bytes as f64) * 100.0
        } else {
            0.0
        };

        Self {
            total_bytes,
            available_bytes,
            free_bytes,
            used_bytes,
            usage_percentage,
        }
    }
}

/// 获取指定路径的磁盘容量信息
///
/// 支持 Windows、macOS、Linux 三个平台
/// - Windows: 使用 GetDiskFreeSpaceExW API
/// - macOS/Linux: 使用 statvfs 系统调用
///
/// # 参数
/// - `path`: 要查询的路径，可以是文件或目录
///
/// # 返回值
/// 返回 `DiskCapacity` 结构体，包含总容量、可用空间等信息
///
/// # 错误
/// 如果路径不存在、权限不足或系统调用失败，返回相应错误
pub fn get_disk_capacity<P: AsRef<Path>>(path: P) -> anyhow::Result<DiskCapacity> {
    #[cfg(target_os = "windows")]
    {
        get_disk_capacity_windows(path.as_ref())
    }

    #[cfg(any(target_os = "macos", target_os = "linux"))]
    {
        get_disk_capacity_unix(path.as_ref())
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        anyhow::bail!("不支持的操作系统")
    }
}

#[cfg(target_os = "windows")]
fn get_disk_capacity_windows(path: &Path) -> anyhow::Result<DiskCapacity> {
    use windows::Win32::Storage::FileSystem::GetDiskFreeSpaceExW;
    use windows::core::{HSTRING, PCWSTR};

    // 获取绝对路径
    let absolute_path = path
        .canonicalize()
        .or_else(|_| std::env::current_dir().map(|cwd| cwd.join(path)))
        .map_err(|e| anyhow::anyhow!("无法解析路径 '{}': {}", path.display(), e))?;

    // 获取路径所在的驱动器根目录
    let root_path = if let Some(root) = absolute_path.ancestors().last() {
        root.to_string_lossy().to_string()
    } else {
        absolute_path.to_string_lossy().to_string()
    };

    // 确保路径以反斜杠结尾（Windows 要求）
    let root_path = if !root_path.ends_with('\\') && root_path.len() <= 3 {
        format!("{}\\", root_path)
    } else {
        root_path
    };

    // 转换为 UTF-16
    let wide_path: Vec<u16> = root_path.encode_utf16().chain(std::iter::once(0)).collect();
    let path_ptr = PCWSTR::from_raw(wide_path.as_ptr());

    let mut available_bytes = 0u64;
    let mut total_bytes = 0u64;
    let mut free_bytes = 0u64;

    unsafe {
        GetDiskFreeSpaceExW(
            path_ptr,
            Some(&mut available_bytes),
            Some(&mut total_bytes),
            Some(&mut free_bytes),
        )
        .map_err(|e| anyhow::anyhow!("获取磁盘空间失败: {}", e))?;
    }

    Ok(DiskCapacity::new(total_bytes, available_bytes, free_bytes))
}

#[cfg(any(target_os = "macos", target_os = "linux"))]
fn get_disk_capacity_unix(path: &Path) -> anyhow::Result<DiskCapacity> {
    use std::ffi::CString;
    use std::mem;

    // 获取绝对路径
    let absolute_path = path
        .canonicalize()
        .or_else(|_| std::env::current_dir().map(|cwd| cwd.join(path)))
        .map_err(|e| anyhow::anyhow!("无法解析路径 '{}': {}", path.display(), e))?;

    // 转换为 C 字符串
    let c_path = CString::new(absolute_path.to_string_lossy().as_bytes())
        .map_err(|e| anyhow::anyhow!("路径包含无效字符: {}", e))?;

    let mut statvfs_buf: libc::statvfs = unsafe { mem::zeroed() };

    let result = unsafe { libc::statvfs(c_path.as_ptr(), &mut statvfs_buf) };

    if result != 0 {
        let errno = unsafe {
            #[cfg(target_os = "linux")]
            {
                *libc::__errno_location()
            }
            #[cfg(target_os = "macos")]
            {
                *libc::__error()
            }
        };
        return Err(anyhow::anyhow!("statvfs 调用失败，错误码: {}", errno));
    }

    // 计算容量信息
    let block_size = statvfs_buf.f_frsize as u64;
    let total_blocks = statvfs_buf.f_blocks as u64;
    let free_blocks = statvfs_buf.f_bfree as u64;
    let available_blocks = statvfs_buf.f_bavail as u64;

    let total_bytes = total_blocks * block_size;
    let free_bytes = free_blocks * block_size;
    let available_bytes = available_blocks * block_size;

    Ok(DiskCapacity::new(total_bytes, available_bytes, free_bytes))
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_get_disk_capacity_current_dir() {
        let current_dir = env::current_dir().expect("无法获取当前目录");
        let result = get_disk_capacity(&current_dir);

        assert!(
            result.is_ok(),
            "获取当前目录磁盘容量失败: {:?}",
            result.err()
        );

        let capacity: DiskCapacity = result.unwrap();
        println!("capacity: {:?}", capacity);
        assert!(capacity.total_bytes > 0, "总容量应该大于0");
        assert!(
            capacity.available_bytes <= capacity.total_bytes,
            "可用空间不应超过总容量"
        );
        assert!(
            capacity.free_bytes <= capacity.total_bytes,
            "空闲空间不应超过总容量"
        );
        assert!(
            capacity.usage_percentage >= 0.0 && capacity.usage_percentage <= 100.0,
            "使用率应该在0-100之间"
        );
    }

    #[test]
    fn test_get_disk_capacity_root() {
        #[cfg(target_os = "windows")]
        let root_path = "C:\\";

        #[cfg(any(target_os = "macos", target_os = "linux"))]
        let root_path = "/";

        let result = get_disk_capacity(root_path);

        assert!(result.is_ok(), "获取根目录磁盘容量失败: {:?}", result.err());

        let capacity = result.unwrap();
        assert!(capacity.total_bytes > 0, "总容量应该大于0");
    }

    #[test]
    fn test_disk_capacity_new() {
        let capacity = DiskCapacity::new(1000, 400, 500);

        assert_eq!(capacity.total_bytes, 1000);
        assert_eq!(capacity.available_bytes, 400);
        assert_eq!(capacity.free_bytes, 500);
        assert_eq!(capacity.used_bytes, 500); // total - free
        assert_eq!(capacity.usage_percentage, 50.0); // used / total * 100
    }

    #[test]
    fn test_disk_capacity_zero_total() {
        let capacity = DiskCapacity::new(0, 0, 0);

        assert_eq!(capacity.usage_percentage, 0.0);
    }
}
