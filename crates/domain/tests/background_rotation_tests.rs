//! 后台轮转管理器测试
//!
//! 测试新的基于后台线程的异步轮转机制

use std::sync::Arc;
use std::time::Duration;
use tempfile::TempDir;
use tokio::time::sleep;
use uuid::Uuid;

use ::domain::logging::{
    LogLevel, StructuredLogEntry, file::FileLayer, forward::LogWriter, source::LogSource,
};
use shared::lru_cache::LruCache;

/// 后台轮转测试上下文
struct BackgroundRotationTestContext {
    _temp_dir: TempDir,
    file_layer: FileLayer,
}

impl BackgroundRotationTestContext {
    fn new() -> Self {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let debug_cache = Arc::new(LruCache::new(100));
        let file_layer = FileLayer::new(temp_dir.path().to_path_buf(), debug_cache.clone());

        Self {
            _temp_dir: temp_dir,
            file_layer,
        }
    }

    fn create_small_entry(&self) -> StructuredLogEntry {
        StructuredLogEntry::new(
            LogLevel::Info,
            "test",
            "test_target",
            "Small test message for background rotation",
            Some(Uuid::new_v4()),
        )
        .with_source("core")
    }

    fn create_large_entry(&self, size_mb: usize) -> StructuredLogEntry {
        let large_message = "X".repeat(size_mb * 1024 * 1024); // Create MB-sized message
        StructuredLogEntry::new(
            LogLevel::Info,
            "test",
            "test_target",
            &large_message,
            Some(Uuid::new_v4()),
        )
        .with_source("core")
    }

    fn get_log_dir(&self) -> &std::path::Path {
        self._temp_dir.path()
    }
}

#[tokio::test]
async fn test_background_rotation_initialization() {
    let ctx = BackgroundRotationTestContext::new();

    // 写入一个小消息，验证基本功能
    let entry = ctx.create_small_entry();
    ctx.file_layer.write_log(entry).unwrap();

    // 验证性能统计
    let stats = ctx.file_layer.get_performance_stats(LogSource::Core);
    assert!(stats.is_some());

    let stats = stats.unwrap();
    assert_eq!(stats.write_count, 1);
    assert_eq!(stats.rotation_count, 0); // 还没有轮转
    assert!(stats.current_file_size > 0);
}

#[tokio::test]
async fn test_size_based_rotation_request() {
    let ctx = BackgroundRotationTestContext::new();

    // 写入一个大消息来触发大小轮转
    let large_entry = ctx.create_large_entry(11); // 11MB，超过10MB限制
    ctx.file_layer.write_log(large_entry).unwrap();

    // 等待后台轮转管理器处理轮转请求
    sleep(Duration::from_millis(100)).await;

    // 验证轮转是否被请求（注意：实际轮转可能仍在进行中）
    let stats = ctx.file_layer.get_performance_stats(LogSource::Core);
    assert!(stats.is_some());

    let stats = stats.unwrap();
    assert_eq!(stats.write_count, 1);
    // 注意：由于轮转是异步的，rotation_count可能是0（还在处理中）或1（已完成）
    println!("Rotation count after large write: {}", stats.rotation_count);
}

#[tokio::test]
async fn test_concurrent_writes_no_blocking() {
    let ctx = BackgroundRotationTestContext::new();

    // 启动多个并发写入任务
    let mut handles = Vec::new();

    for i in 0..10 {
        let file_layer = ctx.file_layer.clone();
        let handle = tokio::spawn(async move {
            for j in 0..100 {
                let entry = StructuredLogEntry::new(
                    LogLevel::Info,
                    "concurrent_test",
                    "test_target",
                    &format!("Message from task {} iteration {}", i, j),
                    Some(Uuid::new_v4()),
                )
                .with_source("core");

                if let Err(e) = file_layer.write_log(entry) {
                    eprintln!("Write error in task {}: {}", i, e);
                }
            }
        });
        handles.push(handle);
    }

    // 等待所有写入完成
    for handle in handles {
        handle.await.unwrap();
    }

    // 验证所有写入都成功
    let stats = ctx.file_layer.get_performance_stats(LogSource::Core);
    assert!(stats.is_some());

    let stats = stats.unwrap();
    assert_eq!(stats.write_count, 1000); // 10 tasks * 100 writes each
    println!(
        "Total writes: {}, rotations: {}",
        stats.write_count, stats.rotation_count
    );
}

#[tokio::test]
async fn test_multiple_sources_isolation() {
    let ctx = BackgroundRotationTestContext::new();

    // 写入不同source的消息
    let sources = [LogSource::Core, LogSource::Daemon, LogSource::Desktop];

    for source in &sources {
        for i in 0..10 {
            let entry = StructuredLogEntry::new(
                LogLevel::Info,
                "multi_source_test",
                "test_target",
                &format!("Message {} for source {:?}", i, source),
                Some(Uuid::new_v4()),
            )
            .with_source(&source.to_string());

            ctx.file_layer.write_log(entry).unwrap();
        }
    }

    // 验证每个source都有独立的统计
    for source in &sources {
        let stats = ctx.file_layer.get_performance_stats(*source);
        assert!(stats.is_some());

        let stats = stats.unwrap();
        assert_eq!(stats.write_count, 10);
        assert_eq!(stats.source, *source);
    }

    // 验证总体统计
    let all_stats = ctx.file_layer.get_all_performance_stats();
    assert_eq!(all_stats.len(), 3);
}

#[tokio::test]
async fn test_rotation_manager_handles_errors_gracefully() {
    let ctx = BackgroundRotationTestContext::new();

    // 写入正常消息
    for i in 0..5 {
        let entry = StructuredLogEntry::new(
            LogLevel::Info,
            "error_handling_test",
            "test_target",
            &format!("Normal message {}", i),
            Some(Uuid::new_v4()),
        )
        .with_source("core");

        ctx.file_layer.write_log(entry).unwrap();
    }

    // 验证写入仍然正常工作
    let stats = ctx.file_layer.get_performance_stats(LogSource::Core);
    assert!(stats.is_some());

    let stats = stats.unwrap();
    assert_eq!(stats.write_count, 5);
}

#[tokio::test]
async fn test_file_layer_drop_cleanup() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let debug_cache = Arc::new(LruCache::new(100));

    {
        let file_layer = FileLayer::new(temp_dir.path().to_path_buf(), debug_cache.clone());

        // 写入一些消息
        for i in 0..3 {
            let entry = StructuredLogEntry::new(
                LogLevel::Info,
                "drop_test",
                "test_target",
                &format!("Message before drop {}", i),
                Some(Uuid::new_v4()),
            )
            .with_source("core");

            file_layer.write_log(entry).unwrap();
        }

        // file_layer在这里被drop，应该优雅关闭后台轮转管理器
    }

    // 等待清理完成
    sleep(Duration::from_millis(200)).await;

    // 验证日志文件确实被创建
    let log_files: Vec<_> = std::fs::read_dir(temp_dir.path())
        .unwrap()
        .map(|entry| entry.unwrap().file_name().to_string_lossy().to_string())
        .filter(|name| name.contains("core-") && name.ends_with(".jsonl"))
        .collect();

    assert!(!log_files.is_empty(), "应该至少有一个日志文件被创建");

    println!("Created log files: {:?}", log_files);
}

#[cfg(test)]
mod performance_tests {
    use super::*;
    use std::time::Instant;

    #[tokio::test]
    async fn test_write_performance_improvement() {
        let ctx = BackgroundRotationTestContext::new();

        // 测量写入性能
        let start = Instant::now();
        let num_writes = 1000;

        for i in 0..num_writes {
            let entry = StructuredLogEntry::new(
                LogLevel::Info,
                "performance_test",
                "test_target",
                &format!("Performance test message {}", i),
                Some(Uuid::new_v4()),
            )
            .with_source("core");

            ctx.file_layer.write_log(entry).unwrap();
        }

        let duration = start.elapsed();
        let writes_per_second = num_writes as f64 / duration.as_secs_f64();

        println!("写入性能: {:.2} writes/second", writes_per_second);
        println!(
            "平均写入延迟: {:.2} ms",
            duration.as_millis() as f64 / num_writes as f64
        );

        // 验证所有写入都成功
        let stats = ctx.file_layer.get_performance_stats(LogSource::Core);
        assert!(stats.is_some());

        let stats = stats.unwrap();
        assert_eq!(stats.write_count, num_writes);

        // 性能期望：应该能达到至少1000 writes/second
        assert!(
            writes_per_second > 1000.0,
            "写入性能应该超过1000次/秒，实际: {:.2}",
            writes_per_second
        );
    }
}
