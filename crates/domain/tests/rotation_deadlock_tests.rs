//! 专门针对日志轮转死循环问题的深度测试和分析
//!
//! 这个测试文件专注于：
//! 1. 分析当前轮转实现中的潜在死循环风险
//! 2. 验证防护机制的有效性
//! 3. 提供后台轮转的替代方案测试
//! 4. 性能影响分析

use std::collections::HashMap;
use std::sync::{
    Arc,
    atomic::{AtomicBool, AtomicU64, Ordering},
};
use std::time::{Duration, Instant};
use tempfile::TempDir;
use uuid::Uuid;

use ::domain::logging::{
    LogLevel, StructuredLogEntry, file::FileLayer, forward::LogWriter, source::LogSource,
};
use shared::lru_cache::LruCache;

/// 轮转死循环分析测试上下文
struct RotationTestContext {
    temp_dir: TempDir,
    file_layer: FileLayer,
    debug_cache: Arc<LruCache<Uuid, Vec<StructuredLogEntry>>>,
    rotation_count: Arc<AtomicU64>,
    write_count: Arc<AtomicU64>,
}

impl RotationTestContext {
    fn new() -> Self {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let debug_cache = Arc::new(LruCache::new(100));
        let file_layer = FileLayer::new(temp_dir.path().to_path_buf(), debug_cache.clone());

        Self {
            temp_dir,
            file_layer,
            debug_cache,
            rotation_count: Arc::new(AtomicU64::new(0)),
            write_count: Arc::new(AtomicU64::new(0)),
        }
    }

    fn create_large_entry(&self, size_kb: usize) -> StructuredLogEntry {
        let large_content = "x".repeat(size_kb * 1024);
        StructuredLogEntry::new(
            LogLevel::Info,
            "rotation_test",
            "rotation_target",
            &large_content,
            Some(Uuid::new_v4()),
        )
        .with_source("core")
    }

    fn write_and_measure(&self, entry: StructuredLogEntry) -> Duration {
        let start = Instant::now();
        self.write_count.fetch_add(1, Ordering::Relaxed);
        let _ = self.file_layer.write_log(entry);
        start.elapsed()
    }
}

/// 死循环风险分析测试
mod deadlock_analysis {
    use super::*;

    #[test]
    fn test_rotation_recursion_depth_limit() {
        let ctx = RotationTestContext::new();

        // 使用大消息快速触发轮转，测试是否会产生深度递归
        let large_entry = ctx.create_large_entry(2048); // 2MB 消息

        let start_time = Instant::now();
        let max_test_duration = Duration::from_secs(10);
        let mut max_write_time = Duration::ZERO;
        let mut total_write_time = Duration::ZERO;
        let mut recursion_indicators = 0u32;

        // 连续写入，观察是否会因递归而挂起
        for i in 0..20 {
            let write_duration = ctx.write_and_measure(large_entry.clone());
            
            max_write_time = max_write_time.max(write_duration);
            total_write_time += write_duration;
            
            // 检测可能的递归指标
            if write_duration > Duration::from_millis(500) {
                recursion_indicators += 1;
                tracing::warn!("写入 {} 耗时异常: {:?}", i, write_duration);
            }

            // 如果单次写入时间过长，可能存在递归问题
            assert!(
                write_duration < Duration::from_secs(2),
                "第 {} 次写入耗时过长: {:?}，可能存在递归调用",
                i,
                write_duration
            );

            // 如果总测试时间过长，停止测试
            if start_time.elapsed() > max_test_duration {
                panic!("测试超时，可能存在死循环");
            }
            
            // 模拟实际使用中的写入间隔
            std::thread::sleep(Duration::from_millis(10));
        }

        let avg_write_time = total_write_time / 20;
        
        println!("递归深度测试完成:");
        println!("  总耗时: {:?}", start_time.elapsed());
        println!("  平均写入时间: {:?}", avg_write_time);
        println!("  最大写入时间: {:?}", max_write_time);
        println!("  异常指标数: {}", recursion_indicators);
        
        // 验证没有明显的递归问题
        assert!(recursion_indicators < 5, "异常写入次数过多，可能存在递归问题");
        assert!(avg_write_time < Duration::from_millis(100), "平均写入时间过长");
    }

    #[test]
    fn test_rotation_guard_effectiveness() {
        let ctx = RotationTestContext::new();

        // 创建一个会触发轮转的大消息
        let large_entry = ctx.create_large_entry(1024); // 1MB

        // 快速连续写入，测试 is_rotating 标记是否有效防护
        let rapid_write_count = 50;
        let mut write_times = Vec::new();

        for i in 0..rapid_write_count {
            let write_time = ctx.write_and_measure(large_entry.clone());
            write_times.push(write_time);

            // 验证写入时间没有因为轮转冲突而急剧增长
            assert!(
                write_time < Duration::from_millis(500),
                "第 {} 次写入时间异常: {:?}",
                i,
                write_time
            );
        }

        // 分析写入时间分布
        let avg_time = write_times.iter().sum::<Duration>() / write_times.len() as u32;
        let max_time = write_times.iter().max().unwrap();
        let min_time = write_times.iter().min().unwrap();

        println!("写入时间统计:");
        println!("  平均: {:?}", avg_time);
        println!("  最大: {:?}", max_time);
        println!("  最小: {:?}", min_time);

        // 验证时间分布合理
        assert!(
            max_time.as_millis() < min_time.as_millis() * 10,
            "写入时间波动过大，可能存在性能问题"
        );
    }

    #[test] 
    fn test_concurrent_rotation_safety() {
        use std::thread;
        use std::sync::Barrier;

        let ctx = RotationTestContext::new();
        let file_layer = Arc::new(ctx.file_layer);
        let write_count = Arc::new(AtomicU64::new(0));
        let error_count = Arc::new(AtomicU64::new(0));
        let rotation_count = Arc::new(AtomicU64::new(0));
        let deadlock_detection = Arc::new(AtomicBool::new(false));

        let thread_count = 8;
        let writes_per_thread = 50;
        
        // 使用屏障确保所有线程同时开始，增加竞争概率
        let barrier = Arc::new(Barrier::new(thread_count));
        let mut handles = Vec::new();

        let start_time = Instant::now();
        
        for thread_id in 0..thread_count {
            let file_layer_clone = file_layer.clone();
            let write_count_clone = write_count.clone();
            let error_count_clone = error_count.clone(); 
            let rotation_count_clone = rotation_count.clone();
            let deadlock_detection_clone = deadlock_detection.clone();
            let barrier_clone = barrier.clone();

            let handle = thread::spawn(move || {
                // 等待所有线程准备完毕
                barrier_clone.wait();
                
                let large_content = "concurrent_rotation_test_".repeat(50000); // ~800KB 消息
                let mut thread_write_times = Vec::new();
                let mut local_rotation_count = 0u64;

                for i in 0..writes_per_thread {
                    let entry = StructuredLogEntry::new(
                        LogLevel::Info,
                        "concurrent_test",
                        "concurrent_target", 
                        &format!("线程{}-消息{}-{}", thread_id, i, large_content),
                        Some(Uuid::new_v4()),
                    )
                    .with_source("core");

                    let write_start = Instant::now();
                    
                    // 死锁检测：如果写入超过5秒，标记潜在死锁
                    let deadlock_detector = thread::spawn({
                        let deadlock_flag = deadlock_detection_clone.clone();
                        move || {
                            thread::sleep(Duration::from_secs(5));
                            deadlock_flag.store(true, Ordering::Relaxed);
                        }
                    });
                    
                    match file_layer_clone.write_log(entry) {
                        Ok(_) => {
                            write_count_clone.fetch_add(1, Ordering::Relaxed);
                            // 估算是否触发了轮转（基于写入时间）
                            let write_time = write_start.elapsed();
                            if write_time > Duration::from_millis(100) {
                                local_rotation_count += 1;
                            }
                            thread_write_times.push(write_time);
                        }
                        Err(e) => {
                            error_count_clone.fetch_add(1, Ordering::Relaxed);
                            tracing::error!("线程{} 写入失败: {:?}", thread_id, e);
                        }
                    }
                    
                    // 取消死锁检测
                    let _ = deadlock_detector.join();

                    let elapsed = write_start.elapsed();
                    
                    // 验证写入不会因为并发轮转而阻塞太久
                    assert!(
                        elapsed < Duration::from_secs(3),
                        "并发写入超时，线程{}-消息{}: {:?}",
                        thread_id,
                        i,
                        elapsed
                    );
                    
                    // 检查是否有死锁迹象
                    if deadlock_detection_clone.load(Ordering::Relaxed) {
                        panic!("检测到潜在死锁，线程{}-消息{}", thread_id, i);
                    }
                    
                    // 增加一些随机延迟，模拟真实场景
                    if i % 10 == 0 {
                        thread::sleep(Duration::from_millis(1));
                    }
                }
                
                rotation_count_clone.fetch_add(local_rotation_count, Ordering::Relaxed);
                
                // 返回线程统计信息
                let avg_time = if !thread_write_times.is_empty() {
                    thread_write_times.iter().sum::<Duration>() / thread_write_times.len() as u32
                } else {
                    Duration::ZERO
                };
                let max_time = thread_write_times.iter().max().cloned().unwrap_or(Duration::ZERO);
                
                (thread_id, avg_time, max_time, local_rotation_count)
            });

            handles.push(handle);
        }

        // 等待所有线程完成并收集统计信息
        let mut thread_stats = Vec::new();
        for handle in handles {
            match handle.join() {
                Ok(stats) => thread_stats.push(stats),
                Err(e) => panic!("线程执行失败: {:?}", e),
            }
        }

        let total_duration = start_time.elapsed();
        let total_writes = write_count.load(Ordering::Relaxed);
        let total_errors = error_count.load(Ordering::Relaxed);
        let total_rotations = rotation_count.load(Ordering::Relaxed);
        let expected_writes = (thread_count * writes_per_thread) as u64;
        
        println!("并发轮转安全测试结果:");
        println!("  总测试时间: {:?}", total_duration);
        println!("  期望写入: {}", expected_writes);
        println!("  成功写入: {}", total_writes);
        println!("  失败次数: {}", total_errors);
        println!("  估计轮转次数: {}", total_rotations);
        println!("  写入速率: {:.2} msg/s", total_writes as f64 / total_duration.as_secs_f64());
        
        // 打印每个线程的统计信息
        for (thread_id, avg_time, max_time, rotations) in thread_stats {
            println!("  线程{}: 平均{:?}, 最大{:?}, 轮转{}", 
                thread_id, avg_time, max_time, rotations);
        }

        // 验证并发安全性
        assert!(
            !deadlock_detection.load(Ordering::Relaxed),
            "检测到死锁迹象"
        );
        
        // 成功率应该很高（允许少量失败）
        let success_rate = (total_writes as f64 / expected_writes as f64) * 100.0;
        assert!(
            success_rate >= 95.0,
            "成功率过低: {:.2}%，可能存在并发问题",
            success_rate
        );
        
        // 错误率应该很低
        let error_rate = (total_errors as f64 / expected_writes as f64) * 100.0;
        assert!(
            error_rate <= 5.0,
            "错误率过高: {:.2}%",
            error_rate
        );
        
        // 总测试时间不应该过长（表明没有严重的性能问题）
        assert!(
            total_duration < Duration::from_secs(30),
            "测试时间过长: {:?}，可能存在性能问题",
            total_duration
        );
    }
}

/// 轮转性能影响分析
mod rotation_performance_analysis {
    use super::*;

    #[test]
    fn test_rotation_performance_overhead() {
        let ctx = RotationTestContext::new();

        // 测试小消息写入性能（不触发轮转）
        let small_message = "small message";
        let small_entry = StructuredLogEntry::new(
            LogLevel::Info,
            "perf_test",
            "perf_target",
            small_message,
            Some(Uuid::new_v4()),
        )
        .with_source("core");

        let start = Instant::now();
        let small_message_count = 1000;

        for _ in 0..small_message_count {
            ctx.file_layer.write_log(small_entry.clone()).unwrap();
        }

        let small_message_duration = start.elapsed();
        let small_message_rate = small_message_count as f64 / small_message_duration.as_secs_f64();

        // 测试大消息写入性能（触发轮转）
        let large_entry = ctx.create_large_entry(512); // 512KB
        let start = Instant::now();
        let large_message_count = 50;

        for _ in 0..large_message_count {
            ctx.file_layer.write_log(large_entry.clone()).unwrap();
        }

        let large_message_duration = start.elapsed();
        let large_message_rate = large_message_count as f64 / large_message_duration.as_secs_f64();

        println!("性能分析结果:");
        println!("  小消息写入速率: {:.2} msg/s", small_message_rate);
        println!("  大消息写入速率: {:.2} msg/s", large_message_rate);
        println!("  性能比率: {:.2}", small_message_rate / large_message_rate);

        // 轮转开销不应该使性能下降过多
        assert!(
            large_message_rate > small_message_rate * 0.01, // 至少保持1%的性能
            "轮转导致的性能下降过大"
        );
    }

    #[test]
    fn test_rotation_check_frequency_impact() {
        let ctx = RotationTestContext::new();

        // 模拟频繁的轮转检查对性能的影响
        let message_sizes = [100, 500, 1000, 5000, 10000]; // 字节
        let mut results = HashMap::new();

        for &size in &message_sizes {
            let content = "x".repeat(size);
            let entry = StructuredLogEntry::new(
                LogLevel::Info,
                "rotation_freq_test",
                "rotation_freq_target",
                &content,
                Some(Uuid::new_v4()),
            )
            .with_source("core");

            let start = Instant::now();
            let test_count = 100;

            for _ in 0..test_count {
                ctx.file_layer.write_log(entry.clone()).unwrap();
            }

            let duration = start.elapsed();
            let rate = test_count as f64 / duration.as_secs_f64();
            results.insert(size, rate);

            println!("消息大小 {} 字节: {:.2} msg/s", size, rate);
        }

        // 分析性能随消息大小的变化趋势
        let rates: Vec<f64> = message_sizes.iter().map(|&size| results[&size]).collect();
        let max_rate = rates
            .iter()
            .max_by(|a, b| a.partial_cmp(b).unwrap())
            .unwrap();
        let min_rate = rates
            .iter()
            .min_by(|a, b| a.partial_cmp(b).unwrap())
            .unwrap();

        // 性能下降应该是渐进的，不应该有急剧的性能悬崖
        assert!(
            *min_rate > *max_rate * 0.1,
            "性能下降过于急剧，最大速率: {:.2}, 最小速率: {:.2}",
            max_rate,
            min_rate
        );
    }
}

/// 后台轮转方案的概念验证
mod background_rotation_alternative {
    use super::*;
    use std::collections::VecDeque;
    use tokio::sync::{Notify, RwLock};

    /// 模拟后台轮转管理器的概念实现
    struct BackgroundRotationManager {
        rotation_queue: Arc<RwLock<VecDeque<(LogSource, String)>>>, // (source, reason)
        should_stop: Arc<AtomicBool>,
        notify: Arc<Notify>,
        rotation_count: Arc<AtomicU64>,
    }

    impl BackgroundRotationManager {
        fn new() -> Self {
            Self {
                rotation_queue: Arc::new(RwLock::new(VecDeque::new())),
                should_stop: Arc::new(AtomicBool::new(false)),
                notify: Arc::new(Notify::new()),
                rotation_count: Arc::new(AtomicU64::new(0)),
            }
        }

        async fn start(&self) {
            let rotation_queue = self.rotation_queue.clone();
            let should_stop = self.should_stop.clone();
            let notify = self.notify.clone();
            let rotation_count = self.rotation_count.clone();

            tokio::spawn(async move {
                loop {
                    // 等待轮转通知或定时检查
                    tokio::select! {
                        _ = notify.notified() => {
                            // 立即处理轮转队列
                            Self::process_rotation_queue(&rotation_queue, &rotation_count).await;
                        }
                        _ = tokio::time::sleep(Duration::from_millis(500)) => {
                            // 定时检查所有source的轮转条件
                            Self::check_all_sources(&rotation_queue, &rotation_count).await;
                        }
                        _ = tokio::signal::ctrl_c() => {
                            break;
                        }
                    }

                    if should_stop.load(Ordering::Relaxed) {
                        break;
                    }
                }
            });
        }

        async fn process_rotation_queue(
            queue: &Arc<RwLock<VecDeque<(LogSource, String)>>>,
            rotation_count: &Arc<AtomicU64>,
        ) {
            let mut queue_guard = queue.write().await;

            while let Some((source, reason)) = queue_guard.pop_front() {
                // 模拟轮转操作
                Self::perform_rotation(source, &reason).await;
                rotation_count.fetch_add(1, Ordering::Relaxed);
            }
        }

        async fn check_all_sources(
            queue: &Arc<RwLock<VecDeque<(LogSource, String)>>>,
            _rotation_count: &Arc<AtomicU64>,
        ) {
            // 模拟检查所有source的轮转条件
            let sources = [LogSource::Core, LogSource::Daemon, LogSource::Desktop];

            for &source in &sources {
                if Self::should_rotate(source).await {
                    let mut queue_guard = queue.write().await;
                    queue_guard.push_back((source, "定时检查触发".to_string()));
                }
            }
        }

        async fn should_rotate(_source: LogSource) -> bool {
            // 模拟轮转条件检查
            // 在实际实现中，这里会检查文件大小和日期
            false // 简化版本，不实际触发轮转
        }

        async fn perform_rotation(source: LogSource, reason: &str) {
            // 模拟执行轮转操作
            println!("执行后台轮转: {} - {}", source, reason);
            tokio::time::sleep(Duration::from_millis(10)).await; // 模拟轮转耗时
        }

        async fn request_rotation(&self, source: LogSource, reason: String) {
            let mut queue_guard = self.rotation_queue.write().await;
            queue_guard.push_back((source, reason));
            drop(queue_guard);
            self.notify.notify_one();
        }

        fn stop(&self) {
            self.should_stop.store(true, Ordering::Relaxed);
            self.notify.notify_one();
        }

        fn get_rotation_count(&self) -> u64 {
            self.rotation_count.load(Ordering::Relaxed)
        }
    }

    #[tokio::test]
    async fn test_background_rotation_concept() {
        let manager = BackgroundRotationManager::new();

        // 启动后台轮转管理器
        manager.start().await;

        // 模拟写入过程中的轮转请求
        for i in 0..10 {
            manager
                .request_rotation(LogSource::Core, format!("大小轮转请求 {}", i))
                .await;

            // 模拟写入间隔
            tokio::time::sleep(Duration::from_millis(50)).await;
        }

        // 等待后台处理完成
        tokio::time::sleep(Duration::from_millis(200)).await;

        let rotation_count = manager.get_rotation_count();
        println!("后台轮转执行次数: {}", rotation_count);

        manager.stop();

        // 验证后台轮转正常工作
        assert!(rotation_count > 0, "后台轮转应该执行了一些操作");
    }

    #[tokio::test]
    async fn test_background_rotation_performance_benefit() {
        let ctx = RotationTestContext::new();
        let manager = BackgroundRotationManager::new();

        manager.start().await;

        // 测试在后台轮转模式下的写入性能
        let start_time = Instant::now();
        let write_count = 1000;
        let mut write_times = Vec::new();

        for i in 0..write_count {
            let entry = StructuredLogEntry::new(
                LogLevel::Info,
                "background_perf_test",
                "background_perf_target",
                &format!("后台轮转性能测试消息 {}", i),
                Some(Uuid::new_v4()),
            )
            .with_source("core");

            let write_start = Instant::now();
            ctx.file_layer.write_log(entry).unwrap();
            write_times.push(write_start.elapsed());

            // 模拟触发轮转条件
            if i % 100 == 0 {
                manager
                    .request_rotation(LogSource::Core, format!("模拟轮转请求 {}", i))
                    .await;
            }
        }

        let total_duration = start_time.elapsed();
        let avg_write_time = write_times.iter().sum::<Duration>() / write_times.len() as u32;
        let max_write_time = *write_times.iter().max().unwrap();

        println!("后台轮转性能测试结果:");
        println!("  总写入时间: {:?}", total_duration);
        println!("  平均写入时间: {:?}", avg_write_time);
        println!("  最大写入时间: {:?}", max_write_time);
        println!(
            "  写入速率: {:.2} msg/s",
            write_count as f64 / total_duration.as_secs_f64()
        );

        manager.stop();

        // 在后台轮转模式下，写入时间应该更稳定
        assert!(
            max_write_time < Duration::from_millis(100),
            "后台轮转模式下最大写入时间应该较短"
        );
    }
}

/// 轮转策略优化建议测试
mod rotation_optimization_suggestions {
    use super::*;

    #[test]
    fn test_batched_rotation_concept() {
        let ctx = RotationTestContext::new();

        // 测试批量轮转的概念
        // 在实际实现中，可以收集多个轮转请求，然后批量执行

        let batch_size = 5;
        let mut rotation_requests = Vec::new();

        // 模拟收集轮转请求
        for i in 0..20 {
            let large_entry = ctx.create_large_entry(1024);

            // 模拟轮转条件检查
            if i % 3 == 0 {
                rotation_requests.push((LogSource::Core, format!("批量轮转请求 {}", i)));
            }

            ctx.file_layer.write_log(large_entry).unwrap();

            // 当收集到足够的轮转请求时，批量执行
            if rotation_requests.len() >= batch_size {
                let batch = rotation_requests.drain(..).collect::<Vec<_>>();
                process_rotation_batch(batch);
            }
        }

        // 处理剩余的轮转请求
        if !rotation_requests.is_empty() {
            process_rotation_batch(rotation_requests);
        }

        println!("批量轮转概念测试完成");
    }

    fn process_rotation_batch(batch: Vec<(LogSource, String)>) {
        println!("处理批量轮转: {} 个请求", batch.len());
        for (source, reason) in batch {
            println!("  - {}: {}", source, reason);
        }
    }

    #[test]
    fn test_adaptive_rotation_threshold() {
        let ctx = RotationTestContext::new();

        // 测试自适应轮转阈值的概念
        // 根据系统负载和写入频率动态调整轮转阈值

        let base_threshold = 1024 * 1024; // 1MB 基础阈值
        let mut current_threshold = base_threshold;
        let mut write_frequency = 0.0;

        let start_time = Instant::now();
        let measurement_window = Duration::from_millis(100);
        let mut last_measurement = start_time;
        let mut writes_in_window = 0;

        for i in 0..100 {
            let entry_size = if i % 10 == 0 {
                512 * 1024 // 偶尔写入大消息
            } else {
                1024 // 大部分是小消息
            };

            let entry = ctx.create_large_entry(entry_size / 1024);
            ctx.file_layer.write_log(entry).unwrap();
            writes_in_window += 1;

            // 定期测量写入频率
            let now = Instant::now();
            if now.duration_since(last_measurement) > measurement_window {
                write_frequency = writes_in_window as f64 / measurement_window.as_secs_f64();

                // 根据写入频率调整阈值
                current_threshold = if write_frequency > 100.0 {
                    base_threshold * 2 // 高频写入时增大阈值
                } else if write_frequency < 10.0 {
                    base_threshold / 2 // 低频写入时减小阈值
                } else {
                    base_threshold // 保持默认阈值
                };

                println!(
                    "写入频率: {:.2} msg/s, 调整阈值到: {} bytes",
                    write_frequency, current_threshold
                );

                writes_in_window = 0;
                last_measurement = now;
            }
        }

        println!("自适应轮转阈值测试完成");
    }

    #[test]
    fn test_rotation_priority_queue() {
        // 测试轮转优先级队列的概念
        // 不同类型的轮转有不同的优先级

        use std::cmp::Ordering;
        use std::collections::BinaryHeap;

        #[derive(Debug, Eq, PartialEq)]
        struct RotationRequest {
            source: LogSource,
            priority: u8, // 0 = 最高优先级
            reason: String,
            timestamp: std::time::SystemTime,
        }

        impl Ord for RotationRequest {
            fn cmp(&self, other: &Self) -> Ordering {
                // 优先级高的排在前面（数字小的优先级高）
                other
                    .priority
                    .cmp(&self.priority)
                    .then_with(|| self.timestamp.cmp(&other.timestamp))
            }
        }

        impl PartialOrd for RotationRequest {
            fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
                Some(self.cmp(other))
            }
        }

        let mut rotation_queue = BinaryHeap::new();

        // 添加不同优先级的轮转请求
        rotation_queue.push(RotationRequest {
            source: LogSource::Core,
            priority: 2, // 普通优先级
            reason: "文件大小轮转".to_string(),
            timestamp: std::time::SystemTime::now(),
        });

        rotation_queue.push(RotationRequest {
            source: LogSource::Core,
            priority: 0, // 最高优先级
            reason: "关键错误轮转".to_string(),
            timestamp: std::time::SystemTime::now(),
        });

        rotation_queue.push(RotationRequest {
            source: LogSource::Daemon,
            priority: 1, // 高优先级
            reason: "日期轮转".to_string(),
            timestamp: std::time::SystemTime::now(),
        });

        // 按优先级处理轮转请求
        println!("轮转优先级队列处理顺序:");
        while let Some(request) = rotation_queue.pop() {
            println!(
                "  优先级 {}: {} - {}",
                request.priority, request.source, request.reason
            );
        }

        println!("轮转优先级队列测试完成");
    }
}

/// 监控和诊断工具测试
mod rotation_monitoring {
    use super::*;
    use ::domain::logging::file::PerformanceStats;

    #[test]
    fn test_rotation_metrics_collection() {
        let ctx = RotationTestContext::new();

        // 模拟轮转指标收集
        #[derive(Debug, Default)]
        struct RotationMetrics {
            rotation_count: u64,
            total_rotation_time: Duration,
            average_rotation_time: Duration,
            max_rotation_time: Duration,
            rotation_failures: u64,
        }

        let mut metrics = RotationMetrics::default();
        let mut rotation_times = Vec::new();

        // 模拟多次轮转并收集指标
        for i in 0..10 {
            let large_entry = ctx.create_large_entry(2048); // 2MB 消息

            let rotation_start = Instant::now();

            match ctx.file_layer.write_log(large_entry) {
                Ok(_) => {
                    let rotation_time = rotation_start.elapsed();
                    rotation_times.push(rotation_time);
                    metrics.rotation_count += 1;
                    metrics.total_rotation_time += rotation_time;

                    if rotation_time > metrics.max_rotation_time {
                        metrics.max_rotation_time = rotation_time;
                    }
                }
                Err(_) => {
                    metrics.rotation_failures += 1;
                }
            }
        }

        // 计算平均轮转时间
        if metrics.rotation_count > 0 {
            metrics.average_rotation_time =
                metrics.total_rotation_time / metrics.rotation_count as u32;
        }

        println!("轮转指标统计:");
        println!("  轮转次数: {}", metrics.rotation_count);
        println!("  总轮转时间: {:?}", metrics.total_rotation_time);
        println!("  平均轮转时间: {:?}", metrics.average_rotation_time);
        println!("  最大轮转时间: {:?}", metrics.max_rotation_time);
        println!("  轮转失败次数: {}", metrics.rotation_failures);

        // 验证指标收集的有效性
        assert!(metrics.rotation_count > 0, "应该有一些轮转发生");
        assert!(metrics.rotation_failures == 0, "不应该有轮转失败");
        assert!(
            metrics.average_rotation_time < Duration::from_secs(1),
            "平均轮转时间应该合理"
        );
    }

    #[tokio::test]
    async fn test_rotation_health_check() {
        let ctx = RotationTestContext::new();

        // 模拟轮转健康检查
        struct RotationHealthChecker {
            last_rotation_time: Option<Instant>,
            rotation_timeout: Duration,
            consecutive_failures: u32,
            max_failures: u32,
        }

        impl RotationHealthChecker {
            fn new() -> Self {
                Self {
                    last_rotation_time: None,
                    rotation_timeout: Duration::from_secs(30),
                    consecutive_failures: 0,
                    max_failures: 3,
                }
            }

            fn check_health(&mut self) -> bool {
                // 检查轮转是否超时
                if let Some(last_time) = self.last_rotation_time {
                    if last_time.elapsed() > self.rotation_timeout {
                        self.consecutive_failures += 1;
                        println!(
                            "警告: 轮转超时，连续失败次数: {}",
                            self.consecutive_failures
                        );

                        if self.consecutive_failures >= self.max_failures {
                            println!("错误: 轮转健康检查失败，需要人工干预");
                            return false;
                        }
                    }
                }

                true
            }

            fn record_successful_rotation(&mut self) {
                self.last_rotation_time = Some(Instant::now());
                self.consecutive_failures = 0;
            }
        }

        let mut health_checker = RotationHealthChecker::new();

        // 模拟轮转操作
        for i in 0..5 {
            let large_entry = ctx.create_large_entry(1024);

            match ctx.file_layer.write_log(large_entry) {
                Ok(_) => {
                    health_checker.record_successful_rotation();
                    println!("轮转 {} 成功", i);
                }
                Err(e) => {
                    println!("轮转 {} 失败: {:?}", i, e);
                }
            }

            // 执行健康检查
            let is_healthy = health_checker.check_health();
            assert!(is_healthy, "轮转健康检查应该通过");

            // 短暂延迟
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        println!("轮转健康检查测试完成");
    }

    #[test]
    fn test_performance_stats_integration() {
        let ctx = RotationTestContext::new();
        
        // 触发一些写入和轮转以生成统计数据
        for _i in 0..5 {
            let large_entry = ctx.create_large_entry(100); // 100KB 消息，减少大小以加快测试
            ctx.file_layer.write_log(large_entry).unwrap();
        }
        
        // 获取性能统计
        if let Some(stats) = ctx.file_layer.get_performance_stats(LogSource::Core) {
            println!("性能统计信息:");
            println!("  Source: {}", stats.source);
            println!("  写入次数: {}", stats.write_count);
            println!("  轮转次数: {}", stats.rotation_count);
            println!("  总轮转时间: {}ms", stats.total_rotation_time_ms);
            println!("  平均轮转时间: {}ms", stats.avg_rotation_time_ms);
            println!("  当前文件大小: {} bytes", stats.current_file_size);
            println!("  轮转频率: {:.2} 次/分钟", stats.get_rotation_frequency());
            println!("  写入速率: {:.2} 条/秒", stats.get_write_rate());
            
            // 检查性能问题
            let issues = stats.has_performance_issues();
            if !issues.is_empty() {
                println!("性能问题:");
                for issue in issues {
                    println!("  - {}", issue);
                }
            } else {
                println!("性能正常，无问题");
            }
            
            // 验证统计数据有效性
            assert!(stats.write_count > 0, "应该有写入记录");
            if stats.rotation_count > 0 {
                assert!(stats.total_rotation_time_ms > 0, "轮转时间应该被记录");
                assert!(stats.avg_rotation_time_ms > 0, "平均轮转时间应该计算正确");
            }
        } else {
            panic!("应该能获取到性能统计信息");
        }
        
        // 测试所有统计信息获取
        let all_stats = ctx.file_layer.get_all_performance_stats();
        assert!(!all_stats.is_empty(), "应该至少有一个source的统计信息");
        
        // 测试重置统计
        ctx.file_layer.reset_performance_stats(LogSource::Core);
        
        if let Some(reset_stats) = ctx.file_layer.get_performance_stats(LogSource::Core) {
            assert_eq!(reset_stats.rotation_count, 0, "轮转计数应该被重置");
            assert_eq!(reset_stats.total_rotation_time_ms, 0, "轮转时间应该被重置");
        }
        
        println!("性能统计集成测试完成");
    }
}
