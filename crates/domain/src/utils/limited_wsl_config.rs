use std::collections::HashMap;
use std::path::PathBuf;
use std::process::Command;
use anyhow::{Context, Result};
use tokio::fs;
use tracing::{info, warn, error};

const BANNER: &'static str = "# Generated By EchoWave";

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct SystemInfo {
    pub cpu_count: usize,
    pub total_memory_gb: f64,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct WslConfig {
    pub processors: String,
    pub memory: String,
    pub swap: String,
}

impl SystemInfo {
    pub fn get() -> Result<Self> {
        let cpu_count = num_cpus::get();
        
        // Get total memory in bytes, then convert to GB
        let total_memory_bytes = if cfg!(target_os = "windows") {
            // On Windows, use sysinfo
            use sysinfo::{System, SystemExt};
            let mut sys = System::new_all();
            sys.refresh_memory();
            sys.total_memory() * 1024 // sysinfo returns KB, convert to bytes
        } else {
            // Fallback for other platforms
            use sysinfo::{System, SystemExt};
            let mut sys = System::new_all();
            sys.refresh_memory();
            sys.total_memory() * 1024
        };
        
        let total_memory_gb = total_memory_bytes as f64 / (1024.0 * 1024.0 * 1024.0);
        
        Ok(SystemInfo {
            cpu_count,
            total_memory_gb,
        })
    }
}

impl WslConfig {
    pub fn from_system_info(system_info: &SystemInfo) -> Self {
        // Calculate processors: 70% of CPU count, minimum 2
        let processors = std::cmp::max(
            (system_info.cpu_count as f64 * 0.7).floor() as usize,
            2,
        ).to_string();
        
        // Calculate memory: 80% of total memory, minimum 1GB
        let memory_gb = std::cmp::max(
            (system_info.total_memory_gb * 0.8).floor() as usize,
            1,
        );
        let memory = format!("{}GB", memory_gb);
        
        let swap = "8GB".to_string();
        
        info!("WSL config: processors={} memory={} swap={}", processors, memory, swap);
        
        WslConfig {
            processors,
            memory,
            swap,
        }
    }
    
    pub fn to_file_content(&self) -> String {
        format!(
            "{}\n[wsl2]\nprocessors={}\nmemory={}\nswap={}",
            BANNER,
            self.processors,
            self.memory,
            self.swap
        )
    }
    
    pub fn to_hashmap(&self) -> HashMap<String, String> {
        let mut map = HashMap::new();
        map.insert("processors".to_string(), self.processors.clone());
        map.insert("memory".to_string(), self.memory.clone());
        map.insert("swap".to_string(), self.swap.clone());
        map
    }
}

fn get_home_dir() -> PathBuf {
    dirs::home_dir().expect("Failed to get home directory")
}

fn get_wsl_config_path() -> PathBuf {
    get_home_dir().join(".wslconfig")
}

fn get_wsl_config_backup_path() -> PathBuf {
    get_home_dir().join(".wslconfig.bak.6563686f77617665")
}

async fn backup_user_wsl_config_file() -> Result<()> {
    let src = get_wsl_config_path();
    let dst = get_wsl_config_backup_path();
    
    if !src.exists() {
        return Ok(());
    }
    
    if dst.exists() {
        return Ok(());
    }
    
    let content = fs::read_to_string(&src).await
        .context("Failed to read WSL config file")?;
    
    if content.starts_with(BANNER) {
        info!("Found legacy config file");
        return Ok(());
    }
    
    info!("User config file exists, starting backup");
    fs::rename(&src, &dst).await
        .context("Failed to backup WSL config file")?;
    
    Ok(())
}

async fn restore_user_wsl_config_file() -> Result<bool> {
    let src = get_wsl_config_backup_path();
    let dst = get_wsl_config_path();
    
    if !dst.exists() {
        error!("Unexpected error, WSL config file not found");
        return Ok(false);
    }
    
    if !src.exists() {
        info!("No user config file, keeping config file for next use");
        return Ok(false);
    }
    
    info!("Removing WSL config file");
    fs::remove_file(&dst).await
        .context("Failed to remove WSL config file")?;
    
    info!("Restoring user config file");
    fs::rename(&src, &dst).await
        .context("Failed to restore user config file")?;
    
    Ok(true)
}

async fn restart_wsl() -> Result<()> {
    info!("Restarting WSL");
    
    let output = Command::new("wsl")
        .arg("--shutdown")
        .output()
        .context("Failed to execute wsl --shutdown")?;
    
    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        anyhow::bail!("WSL shutdown failed: {}", stderr);
    }
    
    info!("WSL restart completed");
    Ok(())
}

pub async fn set_limited_wsl_config() -> Result<()> {
    let system_info = SystemInfo::get()
        .context("Failed to get system information")?;
    
    let config = WslConfig::from_system_info(&system_info);
    let content = config.to_file_content();
    let config_map = config.to_hashmap();
    
    let config_path = get_wsl_config_path();
    
    if config_path.exists() {
        info!("WSL config file exists, starting comparison");
        
        let existing_content = fs::read_to_string(&config_path).await
            .context("Failed to read existing WSL config")?;
        
        // Parse existing config and compare with desired config
        let matches: Vec<_> = existing_content
            .lines()
            .filter_map(|line| {
                let parts: Vec<&str> = line.trim().split('=').collect();
                if parts.len() == 2 {
                    let key = parts[0].trim();
                    let value = parts[1].trim();
                    if let Some(expected_value) = config_map.get(key) {
                        if expected_value == value {
                            return Some((key, value));
                        }
                    }
                }
                None
            })
            .collect();
        
        if matches.len() == config_map.len() {
            info!("Configuration successful");
            return Ok(());
        }
    }
    
    backup_user_wsl_config_file().await
        .context("Failed to backup user WSL config")?;
    
    fs::write(&config_path, content).await
        .context("Failed to write WSL config file")?;
    
    match restart_wsl().await {
        Ok(_) => {
            info!("Configuration successful");
            Ok(())
        }
        Err(e) => {
            error!("Configuration failed: {}", e);
            Err(e)
        }
    }
}

pub async fn unset_limited_wsl_config() -> Result<()> {
    match restore_user_wsl_config_file().await {
        Ok(restored) => {
            if restored {
                restart_wsl().await
                    .context("Failed to restart WSL after restoring config")?;
            }
            info!("Unset configuration successful");
            Ok(())
        }
        Err(e) => {
            error!("Unset configuration failed: {}", e);
            Err(e)
        }
    }
}
