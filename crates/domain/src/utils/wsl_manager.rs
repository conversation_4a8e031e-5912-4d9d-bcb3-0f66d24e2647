//! WSL 管理工具
//!
//! 提供完整的 WSL 分发管理功能，包括：
//! - 列出已安装的分发和状态检查
//! - 导入新的 WSL 分发
//! - 设置 WSL2 版本
//! - 删除损坏的分发
//! - 处理 UTF-16LE 编码输出
//! - 处理特殊 WSL 错误

use regex::Regex;
use serde::{Deserialize, Serialize};
use shared::bin_where;
use shared::command::create_command;
use shared::truncate::truncate_str;
use std::{process::Stdio, sync::LazyLock};
use tracing::{debug, info, warn};

/// WSL 分发状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum WslDistributionStatus {
    Running,
    Stopped,
    Installing,
    Unknown,
}

impl std::fmt::Display for WslDistributionStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            WslDistributionStatus::Running => write!(f, "Running"),
            WslDistributionStatus::Stopped => write!(f, "Stopped"),
            WslDistributionStatus::Installing => write!(f, "Installing"),
            WslDistributionStatus::Unknown => write!(f, "Unknown"),
        }
    }
}

/// WSL 分发信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WslDistribution {
    /// 分发名称
    pub name: String,
    /// 运行状态
    pub status: WslDistributionStatus,
    /// WSL 版本 (1 或 2)
    pub version: Option<u8>,
    /// 是否为默认分发
    pub is_default: bool,
}

/// WSL 管理器
pub struct WslManager;

impl WslManager {
    /// 列出所有 WSL 分发
    ///
    /// 使用 `wsl -l -v` 命令列出所有已安装的分发
    /// 处理 UTF-16LE 编码的输出并解析状态信息
    ///
    /// # 返回值
    /// 返回所有分发的列表，包括名称、状态、版本等信息
    ///
    /// # 错误处理
    /// - WSL 命令执行失败
    /// - UTF-16LE 解码失败
    /// - 输出解析失败
    pub async fn list_distributions() -> anyhow::Result<Vec<WslDistribution>> {
        info!("Listing WSL distributions");

        // 执行 wsl -l -v 命令
        let output = create_command("wsl")
            .args(["-l", "-v"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| {
                tracing::error!("WSL命令执行失败: {}", e);
                anyhow::anyhow!("WSL命令执行失败: {}", e)
            })?;

        if !output.status.success() {
            let stderr = Self::decode_wsl_output(&output.stderr)?;
            if stderr.contains("Windows Subsystem for Linux has no installed distributions") {
                // 没有安装任何分发，返回空列表
                return Ok(vec![]);
            }
            tracing::error!("WSL命令执行失败: {}", stderr);
            anyhow::bail!("WSL命令执行失败: {}", stderr);
        }

        // 处理 UTF-16LE 编码的输出
        let output_text = Self::decode_wsl_output(&output.stdout)?;
        debug!("WSL output: {}", output_text);

        // 解析输出
        Self::parse_distribution_list(&output_text)
    }

    /// 检查特定分发是否已导入
    ///
    /// # 参数
    /// - `name`: 分发名称
    ///
    /// # 返回值
    /// 如果分发已导入返回 `Some(WslDistribution)`，否则返回 `None`
    pub async fn is_distribution_imported(name: &str) -> anyhow::Result<Option<WslDistribution>> {
        let distributions = Self::list_distributions().await?;
        Ok(distributions.into_iter().find(|d| d.name == name))
    }

    /// 导入 WSL 分发
    ///
    /// 使用 `wsl --import` 命令导入 TAR 格式的分发镜像
    ///
    /// # 参数
    /// - `name`: 分发名称
    /// - `tar_path`: TAR 文件路径
    /// - `install_path`: 安装目录路径
    ///
    /// # 错误处理
    /// - TAR 文件不存在
    /// - 安装目录创建失败
    /// - WSL 导入命令失败
    pub async fn import_distribution(
        name: &str,
        tar_path: &std::path::Path,
        install_path: &std::path::Path,
    ) -> anyhow::Result<()> {
        info!(
            "Importing WSL distribution: {} from {} to {}",
            name,
            tar_path.display(),
            install_path.display()
        );

        // 检查 TAR 文件是否存在
        if !tar_path.exists() {
            anyhow::bail!("TAR文件不存在: {}", tar_path.display());
        }

        // 重要：导入前检查并删除已存在的数据卷目录（参考 legacy）
        if install_path.exists() {
            warn!("安装目录已存在，删除: {}", install_path.display());
            tokio::fs::remove_dir_all(install_path)
                .await
                .map_err(|e| anyhow::anyhow!("删除已存在安装目录失败: {}", e))?;
        }

        // 创建安装目录
        tokio::fs::create_dir_all(install_path)
            .await
            .map_err(|e| anyhow::anyhow!("创建安装目录失败: {}", e))?;

        // 执行导入命令
        let output = create_command("wsl")
            .args([
                "--import",
                name,
                &install_path.to_string_lossy(),
                &tar_path.to_string_lossy(),
            ])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("WSL导入命令执行失败: {}", e))?;

        if !output.status.success() {
            let stderr = Self::decode_wsl_output(&output.stderr)?;
            anyhow::bail!("WSL分发导入失败: {}", stderr);
        }

        info!("WSL distribution imported successfully: {}", name);
        Ok(())
    }

    /// 设置分发为 WSL2 版本
    ///
    /// 使用 `wsl --set-version` 命令设置分发版本
    ///
    /// # 参数
    /// - `name`: 分发名称
    ///
    /// # 错误处理
    /// - 分发不存在
    /// - WSL_E_VM_MODE_INVALID_STATE 等特殊错误（可忽略）
    pub async fn set_wsl2_version(name: &str) -> anyhow::Result<()> {
        info!("Setting WSL distribution {} to version 2", name);

        let output = create_command("wsl")
            .args(["--set-version", name, "2"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("WSL版本设置命令执行失败: {}", e))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);

            // 处理特殊错误：WSL_E_VM_MODE_INVALID_STATE 等可忽略错误
            if Self::is_ignorable_wsl_error(&stderr) {
                warn!("WSL版本设置遇到可忽略错误: {}", stderr);
                return Ok(());
            }

            anyhow::bail!("WSL分发版本设置失败: {}", stderr);
        }

        info!("WSL distribution {} set to version 2 successfully", name);
        Ok(())
    }

    /// 删除(注销) WSL 分发
    ///
    /// 使用 `wsl --unregister` 命令删除损坏或不需要的分发
    ///
    /// # 参数
    /// - `name`: 分发名称
    pub async fn unregister_distribution(name: &str) -> anyhow::Result<()> {
        info!("Unregistering WSL distribution: {}", name);

        let output = create_command("wsl")
            .args(["--unregister", name])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("WSL注销命令执行失败: {}", e))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            if stderr.contains("not found") || stderr.contains("does not exist") {
                // 分发不存在，认为成功
                warn!("WSL分发 {} 不存在，跳过注销", name);
                return Ok(());
            }
            anyhow::bail!("WSL分发注销失败: {}", stderr);
        }

        info!("WSL distribution {} unregistered successfully", name);
        Ok(())
    }

    /// 启动 WSL 分发
    ///
    /// # 参数
    /// - `name`: 分发名称
    #[deprecated(
        since = "0.1.0",
        note = "请勿使用，而是直接通过 wsl -d <name> -e <command> 启动"
    )]
    #[allow(dead_code)]
    pub async fn start_distribution(name: &str) -> anyhow::Result<()> {
        info!("Starting WSL distribution: {}", name);

        // 使用 wsl -d <name> echo "test" 来启动分发
        let output = create_command("wsl")
            .args(["-d", name, "echo", "test"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("WSL启动命令执行失败: {}", e))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            anyhow::bail!("WSL分发启动失败: {}", stderr);
        }

        info!("WSL distribution {} started successfully", name);
        Ok(())
    }

    /// 停止 WSL 分发
    ///
    /// # 参数
    /// - `name`: 分发名称
    pub async fn stop_distribution(name: &str) -> anyhow::Result<()> {
        info!("Stopping WSL distribution: {}", name);
        let output = create_command("wsl")
            .args(["--terminate", name])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("WSL停止命令执行失败: {}", e))?;
        if !output.status.success() {
            let stderr = Self::decode_wsl_output(&output.stderr)?;
            anyhow::bail!("WSL分发停止失败: {}", stderr);
        }
        info!("WSL distribution {} stopped successfully", name);
        Ok(())
    }

    /// 检查 WSL 分发是否正在运行
    ///
    /// # 参数
    /// - `name`: 分发名称
    ///
    /// # 返回值
    /// 如果分发正在运行返回 `true`，否则返回 `false`
    pub async fn is_distribution_running(name: &str) -> anyhow::Result<bool> {
        match Self::is_distribution_imported(name).await? {
            Some(distribution) => Ok(distribution.status == WslDistributionStatus::Running),
            None => Ok(false),
        }
    }

    /// 解码 WSL 命令的 UTF-16LE 输出
    ///
    /// WSL 命令在 Windows 上输出 UTF-16LE 编码的文本
    ///
    /// # 参数
    /// - `output_bytes`: 原始输出字节
    ///
    /// # 返回值
    /// 解码后的 UTF-8 字符串
    pub fn decode_wsl_output(output_bytes: &[u8]) -> anyhow::Result<String> {
        // 检查是否为 UTF-16LE (BOM: FF FE)
        if output_bytes.len() >= 2 && output_bytes[0] == 0xFF && output_bytes[1] == 0xFE {
            // 跳过 BOM，解码 UTF-16LE
            let utf16_bytes = &output_bytes[2..];
            let utf16_slice: &[u16] = unsafe {
                std::slice::from_raw_parts(
                    utf16_bytes.as_ptr() as *const u16,
                    utf16_bytes.len() / 2,
                )
            };
            String::from_utf16(utf16_slice).map_err(|e| {
                warn!("UTF-16LE解码失败: {}", e);
                anyhow::anyhow!("UTF-16LE解码失败: {}", e)
            })
        } else if output_bytes.len() % 2 == 0 {
            // 尝试直接解码 UTF-16LE (无 BOM)
            let utf16_slice: &[u16] = unsafe {
                std::slice::from_raw_parts(
                    output_bytes.as_ptr() as *const u16,
                    output_bytes.len() / 2,
                )
            };
            Ok(String::from_utf16(utf16_slice).unwrap_or_else(|_| {
                // 如果 UTF-16 解码失败，尝试 UTF-8
                String::from_utf8_lossy(output_bytes).to_string()
            }))
        } else {
            // 奇数字节，使用 UTF-8 解码
            Ok(String::from_utf8_lossy(output_bytes).to_string())
        }
    }

    /// 解析 WSL 分发列表输出
    ///
    /// 解析 `wsl -l -v` 命令的输出，提取分发信息
    ///
    /// # 参数
    /// - `output`: WSL 命令输出文本
    ///
    /// # 返回值
    /// 解析后的分发列表
    fn parse_distribution_list(output: &str) -> anyhow::Result<Vec<WslDistribution>> {
        let mut distributions = Vec::new();

        // 跳过标题行，处理每个分发行
        let lines: Vec<&str> = output.lines().skip(1).collect();

        for line in lines {
            let line = line.trim();
            if line.is_empty() {
                continue;
            }

            // 解析分发信息
            if let Some(distribution) = Self::parse_distribution_line(line)? {
                distributions.push(distribution);
            }
        }

        Ok(distributions)
    }

    /// 解析单行分发信息
    ///
    /// WSL 输出格式示例：
    /// ```
    ///   NAME                   STATE           VERSION
    /// * Ubuntu                 Running         2
    ///   EchoWave               Stopped         2
    ///   docker-desktop-data    Running         2
    /// ```
    ///
    /// # 参数
    /// - `line`: 单行文本
    ///
    /// # 返回值
    /// 解析后的分发信息，如果解析失败返回 `None`
    fn parse_distribution_line(line: &str) -> anyhow::Result<Option<WslDistribution>> {
        let line = line.trim();
        if line.is_empty() {
            return Ok(None);
        }

        // 检查是否为默认分发（以 * 开头）
        let (is_default, clean_line) = if line.starts_with('*') {
            (true, line[1..].trim())
        } else {
            (false, line)
        };

        // 使用正则表达式解析分发信息
        // 格式: NAME + 空格 + STATE + 空格 + VERSION
        static RE: LazyLock<Result<Regex, regex::Error>> = LazyLock::new(|| {
            Regex::new(r"^(\S+)\s+(Running|Stopped|Installing|Converting|Unknown)\s*(\d+)?")
        });
        let re = RE.as_ref().map_err(|e| {
            warn!("正则表达式编译失败: {}", e);
            anyhow::anyhow!("正则表达式编译失败: {}", e)
        })?;

        if let Some(captures) = re.captures(clean_line) {
            let name = captures.get(1).unwrap().as_str().to_string();
            let status_str = captures.get(2).unwrap().as_str();
            let version = captures.get(3).and_then(|m| m.as_str().parse::<u8>().ok());

            let status = match status_str {
                "Running" => WslDistributionStatus::Running,
                "Stopped" => WslDistributionStatus::Stopped,
                "Installing" => WslDistributionStatus::Installing,
                "Converting" => WslDistributionStatus::Installing, // Converting 也算作 Installing
                _ => WslDistributionStatus::Unknown,
            };

            Ok(Some(WslDistribution {
                name,
                status,
                version,
                is_default,
            }))
        } else {
            debug!("无法解析WSL分发行: {}", clean_line);
            Ok(None)
        }
    }

    /// 检查是否为可忽略的 WSL 错误
    ///
    /// 某些 WSL 错误是已知的且可以安全忽略的
    ///
    /// # 参数
    /// - `error_message`: 错误消息
    ///
    /// # 返回值
    /// 如果是可忽略错误返回 `true`
    fn is_ignorable_wsl_error(error_message: &str) -> bool {
        // WSL_E_VM_MODE_INVALID_STATE 错误代码
        if error_message.contains("0x80370102") {
            return true;
        }

        // 其他已知可忽略错误
        let ignorable_patterns = [
            "WSL_E_VM_MODE_INVALID_STATE",
            "The operation completed successfully", // 某些情况下错误输出中包含成功消息
        ];

        ignorable_patterns
            .iter()
            .any(|pattern| error_message.contains(pattern))
    }

    /// 检查WSL可执行文件是否存在
    pub fn check_wsl_executable() -> bool {
        match bin_where::bin_where("wsl") {
            Some(path) => {
                info!("WSL executable found at: {:?}", path);
                true
            }
            None => {
                info!("WSL executable not found");
                false
            }
        }
    }

    /// 判断是否为 WSL 引导程序提示
    ///
    /// WSL 引导程序通常会显示如下提示：
    /// - "Windows Subsystem for Linux has no installed distributions"
    /// - "适用于 Linux 的 Windows 子系统没有已安装的分发版"
    /// - 或提示用户访问 Microsoft Store 安装
    fn is_wsl_bootstrap_prompt(error_message: &str) -> bool {
        let bootstrap_indicators = [
            "no installed distributions",
            "没有已安装的分发版",
            "Microsoft Store",
            "wsl --install",
            "适用于 Linux 的 Windows 子系统",
            "Windows Subsystem for Linux",
            "install a distribution",
            "安装分发版",
        ];

        bootstrap_indicators.iter().any(|indicator| {
            error_message
                .to_lowercase()
                .contains(&indicator.to_lowercase())
        })
    }

    /// 检查 `wsl --list` 命令
    async fn check_wsl_list_command() -> anyhow::Result<String> {
        let output = create_command("wsl")
            .args(["--list", "--verbose"])
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("执行 wsl --list 失败: {}", e))?;

        if output.status.success() {
            let list_output = Self::decode_wsl_output(&output.stdout)?;
            info!("WSL 分发列表: {}", list_output);
            Ok(list_output)
        } else {
            tracing::debug!(
                "wsl --list 命令失败, stdout: {} bytes, stderr: {} bytes",
                output.stdout.len(),
                output.stderr.len()
            );
            let stderr = if !output.stderr.is_empty() {
                WslManager::decode_wsl_output(&output.stderr)
            } else {
                WslManager::decode_wsl_output(&output.stdout)
            };
            let stderr = stderr.unwrap_or("unknown error".to_string());
            let truncated_stderr = truncate_str(&stderr, 32);
            tracing::warn!("wsl --list 命令失败: {}", truncated_stderr);
            anyhow::bail!("wsl --list 命令失败: {}", stderr);
        }
    }

    /// 检查 `wsl --version` 命令
    async fn check_wsl_version_command() -> anyhow::Result<String> {
        let output = create_command("wsl")
            .args(["--version"])
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("执行 wsl --version 失败: {}", e))?;

        if output.status.success() {
            let version_output = Self::decode_wsl_output(&output.stdout)?;
            Ok(version_output)
        } else {
            // 如果为引导程序，会返回错误码 1 然后在 stdout 输出内容
            let stderr = if !output.stderr.is_empty() {
                WslManager::decode_wsl_output(&output.stderr)
            } else {
                WslManager::decode_wsl_output(&output.stdout)
            };
            let stderr = stderr.unwrap_or("unknown error".to_string());
            let truncated_stderr = truncate_str(&stderr, 32);
            tracing::warn!("wsl --version 命令失败: {}", truncated_stderr);
            anyhow::bail!("wsl --version 命令失败: {}", stderr);
        }
    }

    /// 检查 WSL 功能完整性
    ///
    /// ## 检查方法
    ///
    /// 1. 执行 `wsl --version` 检查是否为完整 WSL
    /// 2. 执行 `wsl --list` 检查分发管理功能
    /// 3. 区分引导程序（引导用户安装）vs 管理程序（完整功能）
    ///
    /// ## 返回值
    ///
    /// - `Ok(true)`: WSL 管理程序完整可用
    /// - `Ok(false)`: 只有 WSL 引导程序
    /// - `Err(_)`: WSL 完全不可用或检查失败
    pub async fn check_wsl_functionality() -> anyhow::Result<bool> {
        info!("检查 WSL 功能完整性");

        // 首先检查 wsl.exe 是否存在
        if !Self::check_wsl_executable() {
            tracing::warn!("WSL 可执行文件不存在");
            anyhow::bail!("WSL 可执行文件不存在");
        }

        // 检查 `wsl --version` 命令
        let version_result = Self::check_wsl_version_command().await;
        let list_result = Self::check_wsl_list_command().await;

        // 综合判断 WSL 功能完整性
        match (version_result.as_ref(), list_result.as_ref()) {
            (Ok(version_info), Ok(_)) => {
                // 两个命令都成功，说明是完整的 WSL 管理程序
                info!("检测到完整的 WSL 管理程序: \n{}", version_info);
                Ok(true)
            }
            (Err(_), _) | (_, Err(_)) => {
                // 任一命令失败，检查是否为引导程序提示
                let version_err_msg = version_result
                    .err()
                    .map(|e| e.to_string())
                    .unwrap_or_default();
                let list_err_msg = list_result.err().map(|e| e.to_string()).unwrap_or_default();

                if Self::is_wsl_bootstrap_prompt(&version_err_msg)
                    || Self::is_wsl_bootstrap_prompt(&list_err_msg)
                {
                    info!("检测到 WSL 引导程序");
                    Ok(false) // 是引导程序，不是完整的管理程序
                } else {
                    // 其他错误，WSL 不可用
                    anyhow::bail!(
                        "WSL 功能检查失败: version={}, list={}",
                        version_err_msg,
                        list_err_msg
                    );
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_distribution_line() {
        // 测试普通分发
        let line = "Ubuntu                 Running         2";
        let result = WslManager::parse_distribution_line(line).unwrap().unwrap();
        assert_eq!(result.name, "Ubuntu");
        assert_eq!(result.status, WslDistributionStatus::Running);
        assert_eq!(result.version, Some(2));
        assert!(!result.is_default);

        // 测试默认分发
        let line = "* Ubuntu-20.04           Stopped         2";
        let result = WslManager::parse_distribution_line(line).unwrap().unwrap();
        assert_eq!(result.name, "Ubuntu-20.04");
        assert_eq!(result.status, WslDistributionStatus::Stopped);
        assert_eq!(result.version, Some(2));
        assert!(result.is_default);

        // 测试无版本号
        let line = "docker-desktop         Running";
        let result = WslManager::parse_distribution_line(line).unwrap().unwrap();
        assert_eq!(result.name, "docker-desktop");
        assert_eq!(result.status, WslDistributionStatus::Running);
        assert_eq!(result.version, None);
        assert!(!result.is_default);
    }

    #[test]
    fn test_is_ignorable_wsl_error() {
        assert!(WslManager::is_ignorable_wsl_error("Error: 0x80370102"));
        assert!(WslManager::is_ignorable_wsl_error(
            "WSL_E_VM_MODE_INVALID_STATE occurred"
        ));
        assert!(!WslManager::is_ignorable_wsl_error("Permission denied"));
    }

    #[test]
    fn test_decode_wsl_output_utf8() {
        let utf8_bytes = "Hello World".as_bytes();
        let result = WslManager::decode_wsl_output(utf8_bytes).unwrap();
        assert_eq!(result, "Hello World");
    }

    #[tokio::test]
    async fn test_check_wsl_functionality() {
        let result = WslManager::check_wsl_functionality().await.unwrap();
        assert!(result);
    }

    #[tokio::test]
    async fn test_check_wsl_executable() {
        let result = WslManager::check_wsl_executable();
        assert!(result);
    }

    #[tokio::test]
    async fn test_check_wsl_list_command() {
        let result = WslManager::check_wsl_list_command().await.unwrap();
        println!("result: {}", result);
        assert!(!result.is_empty());
    }

    #[tokio::test]
    async fn test_check_wsl_version_command() {
        let result = WslManager::check_wsl_version_command().await.unwrap();
        println!("result: {}", result);
        assert!(!result.is_empty());
    }

    #[tokio::test]
    async fn test_check_wsl_list_command_error() {
        let result = WslManager::check_wsl_list_command().await.unwrap();
        println!("result: {}", result);
        assert!(!result.is_empty());
    }

    #[tokio::test]
    async fn test_distribution_is_running() {
        let result = WslManager::is_distribution_running("Debian").await.unwrap();
        assert!(!result);
        #[allow(deprecated)]
        WslManager::start_distribution("Debian").await.unwrap();
        let result = WslManager::is_distribution_running("Debian").await.unwrap();
        assert!(result);
        WslManager::stop_distribution("Debian").await.unwrap();
        let result = WslManager::is_distribution_running("Debian").await.unwrap();
        assert!(!result);
    }

    #[tokio::test]
    async fn test_list_distributions() {
        let result = WslManager::list_distributions().await.unwrap();
        println!("result: {:?}", result);
        assert!(!result.is_empty());
    }
}
