use super::coordinator::UpdateCoordinator;

trait AgentUpdater {
    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool>;
    async fn apply_update(&self) -> anyhow::Result<()>;
    async fn rollback(&self) -> anyhow::Result<()>;
}

impl AgentUpdater for UpdateCoordinator {
    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool> {
        // 1. 检查是否接单
        todo!()
    }

    async fn apply_update(&self) -> anyhow::Result<()> {
        todo!()
    }

    async fn rollback(&self) -> anyhow::Result<()> {
        todo!()
    }
}
