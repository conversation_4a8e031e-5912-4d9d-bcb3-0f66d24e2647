//! 更新管理服务
//!
//! 负责版本检查、更新下载和安装，集成Windows服务模块更新安装
//!
//! 职责：
//! - 检查和更新应用软件
//! - 检查和更新 WSL 镜像
//! - 检查和更新 Agent
//! - 检查和更新 WSL
//!
//! 应用更新策略：
//! - 类 Electron updater，从 CDN 获取 latest.yml 文件，解析出最新版本号，然后下载对应版本的安装包
//! - 从服务器通过 API 获取更新信息(需传递 machine_id 和 当前版本号)，用于支持灰度更新
//!
//! 外部依赖：
//! - Helper 服务，在更新应用软件时，需要依赖 Helper 服务，用于需要权限的安装更新

use futures::lock::Mutex;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::sync::atomic::AtomicU64;
use tokio::sync::watch;
use tokio::task::JoinHandle;
use tokio_util::sync::CancellationToken;

use super::{CoreService, ServiceConfig, ServiceFuture, ServiceStatus};
use crate::services::update_service::coordinator::UpdateCoordinatorState;
use crate::{
    CoreError, CoreResult,
    adapters::{agent::AgentAdapter, file::FileAdapter, helper::HelperAdapter, http::HttpAdapter},
};
use tracing::{info, warn};

mod agent;
mod client;
mod coordinator;
mod mirror;
mod wsl;

/// 更新服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateConfig {
    /// 更新检查间隔（秒）
    pub check_interval: u64,
    /// 下载超时时间（秒）
    pub download_timeout: u64,
    /// 是否启用自动更新
    pub auto_update_enabled: bool,
    /// 更新安装重试次数
    pub install_retries: u32,
}

impl Default for UpdateConfig {
    fn default() -> Self {
        Self {
            check_interval: 3600,   // 1小时
            download_timeout: 1800, // 30分钟
            auto_update_enabled: false,
            install_retries: 3,
        }
    }
}

impl ServiceConfig for UpdateConfig {
    fn validate(&self) -> CoreResult<()> {
        if self.check_interval == 0 {
            return Err(CoreError::config_error(
                "check_interval must be greater than 0",
            ));
        }
        if self.download_timeout == 0 {
            return Err(CoreError::config_error(
                "download_timeout must be greater than 0",
            ));
        }
        Ok(())
    }

    fn name(&self) -> &str {
        "update"
    }
}

/// 更新管理服务
pub struct UpdateService {
    /// 配置
    config: UpdateConfig,
    // adapters
    http_adapter: Arc<HttpAdapter>,
    file_adapter: Arc<FileAdapter>,
    helper_adapter: Arc<HelperAdapter>,
    agent_adapter: Arc<AgentAdapter>,
    // services
    device_service: Arc<super::DeviceService>,
    settings_service: Arc<super::SettingsService>,
    status: watch::Sender<ServiceStatus>,
    /// 更新状态
    coordinator_state: watch::Sender<UpdateCoordinatorState>,
    /// 主循环运行控制
    coordinator_loop: Arc<Mutex<Option<(JoinHandle<()>, CancellationToken)>>>,
    /// 当前轮询间隔（秒）
    poll_interval: Arc<AtomicU64>,
}

impl UpdateService {
    /// 创建新的更新服务
    pub fn new(
        config: UpdateConfig,
        device_service: Arc<super::DeviceService>,
        settings_service: Arc<super::SettingsService>,
        http_adapter: Arc<HttpAdapter>,
        file_adapter: Arc<FileAdapter>,
        helper_adapter: Arc<HelperAdapter>,
        agent_adapter: Arc<AgentAdapter>,
    ) -> CoreResult<Self> {
        config.validate()?;

        Ok(Self {
            config,
            http_adapter,
            file_adapter,
            helper_adapter,
            agent_adapter,
            settings_service,
            device_service,
            status: watch::Sender::new(ServiceStatus::Stopped),
            coordinator_state: watch::Sender::new(UpdateCoordinatorState::Idle),
            coordinator_loop: Arc::new(Mutex::new(None)),
            poll_interval: Arc::new(AtomicU64::new(300)), // 默认5分钟
        })
    }

    /// 获取当前协调器状态
    pub async fn get_coordinator_state(&self) -> UpdateCoordinatorState {
        self.coordinator_state.borrow().clone()
    }

    /// 启动更新协调器主循环
    pub async fn start_coordinator_loop(&self) -> anyhow::Result<()> {
        // 检查是否已经在运行
        let mut guard = self.coordinator_loop.lock().await;
        if guard.is_some() {
            info!("Update coordinator loop already running");
            return Ok(());
        }

        info!("Starting update coordinator main loop");

        let signal = CancellationToken::new();

        // 启动主循环任务
        let coordinator = coordinator::UpdateCoordinator {
            state: self.coordinator_state.clone(),
            device_service: self.device_service.clone(),
            settings_service: self.settings_service.clone(),
            file_adapter: self.file_adapter.clone(),
            http_adapter: self.http_adapter.clone(),
            signal: signal.clone(),
            poll_interval: self.poll_interval.clone(),
        };
        let handle = tokio::spawn(async move {
            if let Err(err) = coordinator.coordinator_main_loop().await {
                warn!("Update coordinator main loop error: {}", err);
            }
        });

        *guard = Some((handle, signal));

        info!("Update coordinator main loop started");
        Ok(())
    }

    /// 停止更新协调器主循环
    pub async fn stop_coordinator_loop(&self) {
        info!("Stopping update coordinator main loop");

        let mut guard = self.coordinator_loop.lock().await;
        if let Some((handle, signal)) = guard.take() {
            signal.cancel();
            if let Err(err) = handle.await {
                warn!("Update coordinator main loop error: {}", err);
            }
        } else {
            warn!("Update coordinator main loop not running");
        }

        info!("Update coordinator main loop stopped");
    }
}

impl CoreService for UpdateService {
    fn name(&self) -> &str {
        "update"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(self.health_check_impl())
    }
}

impl UpdateService {
    async fn start_impl(&self) -> CoreResult<()> {
        info!("Starting Update Service");

        // 检查配置
        self.config.validate()?;

        // 设置服务状态为运行中
        self.status.send_replace(ServiceStatus::Running);
        self.start_coordinator_loop().await?;
        Ok(())
    }
    async fn stop_impl(&self) -> CoreResult<()> {
        self.stop_coordinator_loop().await;
        self.status.send_replace(ServiceStatus::Stopped);
        Ok(())
    }

    async fn health_check_impl(&self) -> CoreResult<bool> {
        Ok(true)
    }
}
