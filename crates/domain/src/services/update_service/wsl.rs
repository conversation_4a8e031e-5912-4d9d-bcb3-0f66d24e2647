use super::coordinator::UpdateCoordinator;

trait WslUpdater {
    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool>;
    async fn apply_update(&self) -> anyhow::Result<()>;
}

impl WslUpdater for UpdateCoordinator {
    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool> {
        todo!()
    }

    async fn apply_update(&self) -> anyhow::Result<()> {
        todo!()
    }
}
