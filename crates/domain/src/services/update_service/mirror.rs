use super::coordinator::UpdateCoordinator;

trait MirrorUpdater {
    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool>;
    async fn apply_update(&self) -> anyhow::Result<()>;
    async fn rollback(&self) -> anyhow::Result<()>;
}

impl MirrorUpdater for UpdateCoordinator {
    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool> {
        todo!()
    }

    async fn apply_update(&self) -> anyhow::Result<()> {
        todo!()
    }

    async fn rollback(&self) -> anyhow::Result<()> {
        todo!()
    }
}
