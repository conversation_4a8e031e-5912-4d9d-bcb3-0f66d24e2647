use serde::{Deserialize, Serialize};
use shared::version::Version;
use tokio::sync::watch;
use tokio_util::sync::CancellationToken;
use tracing::error;
use tracing::info;
use tracing::info_span;
use tracing::warn;
use uuid::Uuid;

use crate::services::DeviceService;
use crate::services::FileAdapter;
use crate::services::HttpAdapter;
use crate::services::SettingsService;
use std::borrow::Cow;
use std::path::PathBuf;
use std::sync::Arc;
use std::sync::atomic::AtomicU64;
use std::sync::atomic::Ordering;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CurrentState {
    pub client_version: Version,
    pub mirror_version: Version,
    pub agent_version: Version,
    pub wsl_version: Version,
    pub tags: std::collections::HashMap<String, String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LastUpdateStatus {
    Success,
    Failed,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub enum LastUpdateErrorCode {
    InsufficientDiskSpace,
    DownloadHashMismatch,
    DownloadTimeout,
    DownloadFailed,
    PermissionDenied,
    WslImportFailed,
    UpdatePaused,
    ManifestFetchFailed,
    CoordinationCycleFailed,
    PreCheckFailed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct LastUpdateResult {
    /// 更新协调器生成的唯一ID
    pub correlation_id: Uuid,
    /// 更新状态
    pub status: LastUpdateStatus,
    /// 连续失败次数
    pub consecutive_failures: u32,
    /// 错误码
    pub error_code: Option<LastUpdateErrorCode>,
    /// 错误信息
    pub error_message: Option<String>,
}

// --- API Request Structures ---
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct NodeStateRequest {
    pub node_id: String,
    pub current_state: CurrentState,
    pub last_update_status: Option<LastUpdateResult>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UpdateManifest {
    pub correlation_id: Uuid,
    pub desired_state: DesiredState,
    pub rollout_control: RolloutControl,
    pub update_policy: Option<String>, // e.g., "paused_due_to_failures"
}

#[derive(Debug, Deserialize)]
pub struct UpdateManifestResponse {
    pub success: bool,
    pub data: Option<UpdateManifest>,
    pub err_code: Option<u32>,
    pub err_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DesiredState {
    pub client: Option<UpdateComponent>,
    pub mirror: Option<UpdateComponent>,
    pub agent: Option<UpdateComponent>,
    pub wsl: Option<UpdateComponent>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct UpdateComponent {
    pub version: Version,
    pub url: String,
    pub sha256: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RolloutControl {
    #[serde(rename = "type")]
    pub rollout_type: String,
    pub delay_window_seconds: u64,
    pub poll_interval: String, // e.g., "300s"
}

#[derive(Debug, Clone)]
pub enum UpdateCoordinatorState {
    /// 空闲
    Idle,
    /// 获取清单
    FetchingManifest,
    /// 等待更新
    PendingUpdate(u64), // delay in seconds
    /// 下载
    Downloading(Vec<&'static str>),
    /// 应用
    Applying,
    /// 等待重启
    RebootPending,
    /// 回滚
    RollingBack,
    /// 失败
    Failed(LastUpdateResult),
}

/// 下载进度
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DownloadProgress {
    pub downloaded_bytes: u64,
    pub total_bytes: u64,
    pub percentage: f64,
    pub speed_bytes_per_sec: u64,
    pub eta_seconds: Option<u64>,
}

#[derive(Clone)]
pub struct UpdateCoordinator {
    pub state: watch::Sender<UpdateCoordinatorState>,
    // services
    pub device_service: Arc<DeviceService>,
    pub settings_service: Arc<SettingsService>,
    // adapters
    pub file_adapter: Arc<FileAdapter>,
    pub http_adapter: Arc<HttpAdapter>,
    // other
    pub signal: CancellationToken,
    pub poll_interval: Arc<AtomicU64>,
}

impl UpdateCoordinator {
    pub async fn coordinator_main_loop(self) -> anyhow::Result<()> {
        info!("Update coordinator main loop started");
        while !self.signal.is_cancelled() {
            // 获取当前轮询间隔
            let poll_interval = self.poll_interval.load(Ordering::Relaxed);
            info!(
                "Update coordinator loop iteration, poll interval: {}s",
                poll_interval
            );
            // 执行一次协调循环
            if let Err(e) = self.execute_coordination_cycle().await {
                warn!("Update coordination cycle failed: {}", e);

                let error_status = LastUpdateResult {
                    correlation_id: Uuid::new_v4(),
                    status: LastUpdateStatus::Failed,
                    consecutive_failures: 0,
                    error_code: Some(LastUpdateErrorCode::CoordinationCycleFailed),
                    error_message: Some(e.to_string()),
                };
                self.save_last_update_status(&error_status).await;
                self.state
                    .send_replace(UpdateCoordinatorState::Failed(error_status));
            }

            // 等待下一次轮询
            tokio::time::sleep(tokio::time::Duration::from_secs(poll_interval)).await;
        }
        info!("Update coordinator main loop exited");
        Ok(())
    }
}

impl UpdateCoordinator {
    /// 执行一次协调循环    
    async fn execute_coordination_cycle(&self) -> anyhow::Result<()> {
        let current_state = self.state.borrow().clone();

        match current_state {
            UpdateCoordinatorState::Idle => {
                info!("Coordinator in Idle state, fetching manifest");
                // 获取更新清单
                let manifest = self.fetch_update_manifest().await?;
                // 更新轮询间隔
                self.poll_interval.store(
                    Self::parse_poll_interval(&manifest.rollout_control.poll_interval)?,
                    Ordering::Relaxed,
                );
                // 检查更新策略
                if !self.handle_update_policy(&manifest).await? {
                    info!("Update policy not met, skipping this cycle");
                    return Ok(());
                };
                // 检查是否需要更新
                let updates_needed = self.determine_updates_needed(&manifest)?;
                if updates_needed.is_empty() {
                    info!("No updates needed, transitioning to idle");
                    self.state.send_replace(UpdateCoordinatorState::Idle);
                } else {
                    info!("Updates needed, transitioning to download");
                }
                // 计算抖动延迟
                let delay_seconds =
                    Self::calculate_jitter_delay(manifest.rollout_control.delay_window_seconds);
                if delay_seconds > 0 {
                    info!("Entering update delay window: {} seconds", delay_seconds);
                    self.state
                        .send_replace(UpdateCoordinatorState::PendingUpdate(delay_seconds));
                } else {
                    info!(
                        "Starting download phase for {} components",
                        updates_needed.len()
                    );
                    self.state.send_replace(UpdateCoordinatorState::Downloading(
                        updates_needed.iter().map(|(name, _)| *name).collect(),
                    ));
                }
                Ok(())
            }
            UpdateCoordinatorState::FetchingManifest => {
                info!("Already fetching manifest, skipping this cycle");
                Ok(())
            }
            UpdateCoordinatorState::PendingUpdate(delay_remaining) => {
                info!(
                    "In pending update state, delay remaining: {}s",
                    delay_remaining
                );
                if delay_remaining <= 1 {
                    info!("Delay period completed, transitioning to download");
                    // TODO: 实现下载状态转换
                    self.state.send_replace(UpdateCoordinatorState::Idle);
                } else {
                    // 减少延迟时间
                    self.state
                        .send_replace(UpdateCoordinatorState::PendingUpdate(delay_remaining - 1));
                }
                Ok(())
            }
            UpdateCoordinatorState::Downloading(components) => {
                info!(
                    "Currently downloading {} components, checking status",
                    components.len()
                );
                // TODO: 检查是否下载完成
                // info!("All downloads completed, transitioning to applying state");
                // Self::set_coordinator_state(coordinator_state, UpdateCoordinatorState::Applying).await;
                // info!("Downloads still in progress, continuing to wait");
                todo!()
            }
            UpdateCoordinatorState::Applying => {
                info!("Entering applying state, performing pre-checks");
                // 执行前置检查
                match self.perform_pre_application_checks().await {
                    Ok(true) => {
                        info!("Pre-checks passed, starting component application");
                        // TODO: 实际应用组件更新（任务3.4, 3.5, 3.6）
                        self.state.send_replace(UpdateCoordinatorState::Idle);
                    }
                    Ok(false) => {
                        info!("Pre-checks not ready, waiting for next cycle");
                        // 继续等待，下次循环再检查
                    }
                    Err(e) => {
                        warn!("Pre-checks failed: {}", e);
                        let error_status = LastUpdateResult {
                            correlation_id: Uuid::new_v4(),
                            status: LastUpdateStatus::Failed,
                            consecutive_failures: 1,
                            error_code: Some(LastUpdateErrorCode::PreCheckFailed),
                            error_message: Some(format!("Pre-application checks failed: {}", e)),
                        };
                        self.save_last_update_status(&error_status).await;
                        self.state
                            .send_replace(UpdateCoordinatorState::Failed(error_status));
                    }
                }
                Ok(())
            }
            UpdateCoordinatorState::RebootPending => {
                info!("Reboot pending, skipping this cycle");
                todo!()
            }
            UpdateCoordinatorState::RollingBack => {
                info!("Currently rolling back, skipping this cycle");
                // TODO: 检查回滚进度
                todo!()
            }
            UpdateCoordinatorState::Failed(last_status) => {
                warn!("Coordinator in failed state: {:?}", last_status);

                // 实现指数退避逻辑
                let backoff_seconds =
                    Self::calculate_backoff_delay(last_status.consecutive_failures);

                info!(
                    "Applying backoff delay: {}s for {} consecutive failures",
                    backoff_seconds, last_status.consecutive_failures
                );

                // 更新轮询间隔
                self.poll_interval.store(backoff_seconds, Ordering::Relaxed);

                // 尝试恢复到空闲状态
                self.state.send_replace(UpdateCoordinatorState::Idle);
                Ok(())
            }
        }
    }
}

impl UpdateCoordinator {
    /// 收集当前状态
    pub async fn collect_current_state(&self) -> anyhow::Result<CurrentState> {
        if let Err(err) = self.device_service.wait_ready().await {
            warn!("无法等待所有设备信息就绪: {}", err);
        }
        // 获取客户端版本（从配置中获取）
        let client_version = self.device_service.get_client_version();

        // 获取WSL镜像版本（需要通过Agent适配器查询）
        let mirror_version = self.device_service.get_mirror_version();

        // 获取Agent版本
        let agent_version = self.device_service.get_agent_version();

        // 获取WSL版本
        let wsl_version = self.device_service.get_wsl_version();

        // 收集标签信息
        let tags = self.device_service.get_tags();

        Ok(CurrentState {
            client_version,
            mirror_version,
            agent_version,
            wsl_version,
            tags,
        })
    }

    /// 加载上次更新状态
    pub async fn load_last_update_status(&self) -> anyhow::Result<Option<LastUpdateResult>> {
        match self
            .file_adapter
            .read_json_by_name::<LastUpdateResult>("last-update-state.json")
            .await
        {
            Ok(status) => {
                info!("Loaded previous update status: {:?}", status);
                Ok(Some(status))
            }
            Err(e) => {
                warn!("Failed to read update status file: {}", e);
                Ok(None)
            }
        }
    }

    /// 保存更新状态
    pub async fn save_last_update_status(&self, status: &LastUpdateResult) -> anyhow::Result<()> {
        // 序列化并保存
        let content = serde_json::to_string(status).map_err(|e| {
            error!("Failed to serialize update status: {}", e);
            anyhow::anyhow!("Failed to serialize update status: {}", e)
        })?;
        self.file_adapter
            .write_json_by_name("last-update-state.json", &content)
            .await
            .map_err(|e| {
                error!("Failed to save update status: {}", e);
                anyhow::anyhow!("Failed to save update status: {}", e)
            })?;
        info!("Saved update status to: last-update-state.json");
        Ok(())
    }

    /// 构建节点状态请求
    pub async fn build_node_state_request(&self) -> anyhow::Result<NodeStateRequest> {
        let current_state = self.collect_current_state().await?;
        let last_update_status = self.load_last_update_status().await?;

        Ok(NodeStateRequest {
            node_id: self.device_service.get_machine_id(),
            current_state,
            last_update_status,
        })
    }

    /// 获取更新清单（调用 POST /api/v1/node/state 接口）
    pub async fn fetch_update_manifest(&self) -> anyhow::Result<UpdateManifest> {
        info!("Fetching update manifest from server");

        // 更新协调器状态为正在获取清单
        self.state
            .send_replace(UpdateCoordinatorState::FetchingManifest);

        // 构建请求
        let request = self.build_node_state_request().await?;

        // 发送HTTP POST请求
        let response: UpdateManifestResponse = self
            .http_adapter
            .post(
                "/api/v1/node/state",
                &serde_json::to_value(&request).map_err(|e| {
                    error!("Failed to serialize request: {}", e);
                    anyhow::anyhow!("Failed to serialize request: {}", e)
                })?,
            )
            .await
            .map_err(|e| {
                error!("Failed to fetch manifest: {}", e);
                anyhow::anyhow!("Failed to fetch manifest: {}", e)
            })?;

        if !response.success {
            let error_msg = response
                .err_message
                .unwrap_or_else(|| "Fetch manifest error".to_string());
            error!("Failed to fetch manifest: {}", error_msg);
            anyhow::bail!("Failed to fetch manifest: {}", error_msg);
        }

        let manifest = response.data.ok_or_else(|| {
            error!("No manifest found");
            anyhow::anyhow!("No manifest found")
        })?;

        info!("Received update manifest: {:?}", manifest);

        // 更新协调器状态回到空闲（后续会根据manifest内容决定下一步）
        self.state.send_replace(UpdateCoordinatorState::Idle);

        Ok(manifest)
    }

    /// 检查版本兼容性
    pub fn check_version_compatibility(&self, desired_state: &DesiredState) -> anyhow::Result<()> {
        let mut versions = Vec::new();

        // 收集所有组件的版本
        if let Some(client) = &desired_state.client {
            versions.push(("client", &client.version));
        }
        if let Some(wsl_image) = &desired_state.mirror {
            versions.push(("wsl_image", &wsl_image.version));
        }
        if let Some(agent) = &desired_state.agent {
            versions.push(("agent", &agent.version));
        }

        if versions.is_empty() {
            return Ok(());
        }

        // 提取主版本号并检查一致性
        let mut major_versions = std::collections::HashSet::new();

        for (component, version) in &versions {
            major_versions.insert(version.major);

            info!(
                "Component {} version: {} (major: {})",
                component, version, version.major
            );
        }

        if major_versions.len() > 1 {
            let error_msg = format!(
                "Version compatibility check failed: Components have different major versions: {:?}",
                versions
            );
            anyhow::bail!(
                "Version compatibility check failed: Components have different major versions: {:?}",
                versions
            );
        }

        info!(
            "Version compatibility check passed for {} components",
            versions.len()
        );
        Ok(())
    }

    /// 检查是否需要更新
    pub fn determine_updates_needed(
        &self,
        manifest: &UpdateManifest,
    ) -> anyhow::Result<Vec<(&'static str, UpdateComponent)>> {
        let mut updates_needed = Vec::new();
        let desired_state = &manifest.desired_state;

        // 检查版本兼容性
        self.check_version_compatibility(desired_state)?;

        // 检查各个组件是否需要更新
        if let Some(client_component) = &desired_state.client {
            let current_version = self.device_service.get_client_version();
            if client_component.version > current_version {
                info!(
                    "Client update needed: {} -> {}",
                    current_version, client_component.version
                );
                updates_needed.push(("client", client_component.clone()));
            }
        }

        if let Some(mirror_component) = &desired_state.mirror {
            let current_version = self.device_service.get_mirror_version();
            if mirror_component.version > current_version {
                info!(
                    "WSL update needed: {} -> {}",
                    current_version, mirror_component.version
                );
                updates_needed.push(("mirror", mirror_component.clone()));
            }
        }

        if let Some(agent_component) = &desired_state.agent {
            let current_version = self.device_service.get_agent_version();
            if agent_component.version > current_version {
                info!(
                    "Agent update needed: {} -> {}",
                    current_version, agent_component.version
                );
                updates_needed.push(("agent", agent_component.clone()));
            }
        }

        if let Some(wsl_component) = &desired_state.wsl {
            let current_version = self.device_service.get_wsl_version();
            if wsl_component.version > current_version {
                info!(
                    "WSL update needed: {} -> {}",
                    current_version, wsl_component.version
                );
                updates_needed.push(("wsl", wsl_component.clone()));
            }
        }
        if updates_needed.is_empty() {
            info!("No updates needed");
        } else {
            info!(
                "Found {} component(s) that need updating",
                updates_needed.len()
            );
        }

        Ok(updates_needed)
    }

    /// 解析轮询间隔
    fn parse_poll_interval(interval_str: &str) -> anyhow::Result<u64> {
        // 解析类似 "300s", "5m", "1h" 的时间间隔
        if interval_str.ends_with("s") {
            let seconds = interval_str
                .trim_end_matches("s")
                .parse::<u64>()
                .map_err(|e| anyhow::anyhow!("Invalid seconds format: {}", e))?;
            Ok(seconds)
        } else if interval_str.ends_with("m") {
            let minutes = interval_str
                .trim_end_matches("m")
                .parse::<u64>()
                .map_err(|e| anyhow::anyhow!("Invalid minutes format: {}", e))?;
            Ok(minutes * 60)
        } else if interval_str.ends_with("h") {
            let hours = interval_str
                .trim_end_matches("h")
                .parse::<u64>()
                .map_err(|e| anyhow::anyhow!("Invalid hours format: {}", e))?;
            Ok(hours * 3600)
        } else {
            // 默认按秒解析
            interval_str
                .parse::<u64>()
                .map_err(|e| anyhow::anyhow!("Invalid time format: {}", e))
        }
    }

    /// 处理更新策略
    async fn handle_update_policy(&self, manifest: &UpdateManifest) -> anyhow::Result<bool> {
        if let Some(policy) = &manifest.update_policy {
            match policy.as_str() {
                // 由于更新失败，进入冷却期
                "paused_due_to_failures" => {
                    warn!("Updates paused due to failures, entering cooldown period");

                    // 解析冷却时间
                    let cooldown_seconds =
                        Self::parse_poll_interval(&manifest.rollout_control.poll_interval)?;

                    info!("Entering cooldown period for {} seconds", cooldown_seconds);

                    // 设置状态为暂停
                    self.state
                        .send_replace(UpdateCoordinatorState::Failed(LastUpdateResult {
                            correlation_id: manifest.correlation_id.clone(),
                            status: LastUpdateStatus::Failed,
                            consecutive_failures: 0,
                            error_code: Some(LastUpdateErrorCode::UpdatePaused),
                            error_message: Some(
                                "Updates paused by server due to failures".to_string(),
                            ),
                        }));

                    return Ok(false); // 不继续处理更新
                }
                _ => {
                    info!("Unknown update policy: {}", policy);
                }
            }
        }

        Ok(true) // 继续处理更新
    }

    /// 计算抖动延迟
    fn calculate_jitter_delay(delay_window_seconds: u64) -> u64 {
        if delay_window_seconds == 0 {
            return 0;
        }

        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        // 使用节点ID作为种子来生成确定性的随机数
        let mut hasher = DefaultHasher::new();
        std::env::current_exe()
            .unwrap_or_default()
            .hash(&mut hasher);

        let seed = hasher.finish();

        // 简单的线性同余生成器
        let a = 1664525u64;
        let c = 1013904223u64;
        let m = 2u64.pow(32);

        let random_value = (a.wrapping_mul(seed).wrapping_add(c)) % m;
        let jitter = (random_value as f64 / m as f64) * delay_window_seconds as f64;

        jitter as u64
    }

    /// 统一更新协调器入口点
    /// 这是新的更新流程，取代传统的 check_for_updates -> download -> install 流程
    pub async fn coordinate_updates(&self, trace_id: &Uuid) -> anyhow::Result<()> {
        info!("Starting update coordination process");

        // 检查当前状态
        let current_state = self.state.borrow().clone();
        let mut consecutive_failures = 0;
        match current_state {
            UpdateCoordinatorState::FetchingManifest
            | UpdateCoordinatorState::Downloading(_)
            | UpdateCoordinatorState::Applying
            | UpdateCoordinatorState::RollingBack
            | UpdateCoordinatorState::PendingUpdate(_)
            | UpdateCoordinatorState::RebootPending => {
                info!(
                    "Update coordination already in progress, state: {:?}",
                    current_state
                );
                return Ok(());
            }
            UpdateCoordinatorState::Failed(LastUpdateResult {
                consecutive_failures: _consecutive_failures,
                ..
            }) => {
                info!("Update coordination in failed state, attempting recovery");
                consecutive_failures = _consecutive_failures + 1;
                // 在失败状态时，继续执行以尝试恢复
            }
            UpdateCoordinatorState::Idle => {
                // 空闲状态，可以继续
            }
        }

        // 第一步：获取更新清单
        let manifest = match self.fetch_update_manifest().await {
            Ok(manifest) => manifest,
            Err(e) => {
                let error_status = LastUpdateResult {
                    correlation_id: Uuid::from_u64_pair(0, 0),
                    status: LastUpdateStatus::Failed,
                    consecutive_failures,
                    error_code: Some(LastUpdateErrorCode::ManifestFetchFailed),
                    error_message: Some(format!("Failed to fetch manifest: {}", e)),
                };

                // 保存错误状态
                if let Err(save_err) = self.save_last_update_status(&error_status).await {
                    warn!("Failed to save error status: {}", save_err);
                }

                self.state
                    .send_replace(UpdateCoordinatorState::Failed(error_status));
                return Err(e);
            }
        };

        // 第二步：处理更新策略
        if !self.handle_update_policy(&manifest).await? {
            // 更新被服务端暂停
            return Ok(());
        }

        // 第三步：确定需要的更新
        let updates_needed = self.determine_updates_needed(&manifest)?;

        if updates_needed.is_empty() {
            info!("No updates needed, coordination complete");

            // 保存成功状态
            let success_status = LastUpdateResult {
                correlation_id: manifest.correlation_id.clone(),
                status: LastUpdateStatus::Success,
                consecutive_failures,
                error_code: None,
                error_message: None,
            };

            self.save_last_update_status(&success_status).await?;
            self.state.send_replace(UpdateCoordinatorState::Idle);
            return Ok(());
        }

        // 第四步：处理延迟窗口（抖动）
        let delay_seconds =
            Self::calculate_jitter_delay(manifest.rollout_control.delay_window_seconds);

        if delay_seconds > 0 {
            info!("Entering update delay window: {} seconds", delay_seconds);
            self.state
                .send_replace(UpdateCoordinatorState::PendingUpdate(delay_seconds));

            // 实际等待延迟
            tokio::time::sleep(tokio::time::Duration::from_secs(delay_seconds)).await;
        }

        // 第五步：开始下载阶段
        info!(
            "Starting download phase for {} components",
            updates_needed.len()
        );
        self.state.send_replace(UpdateCoordinatorState::Downloading(
            updates_needed.iter().map(|(name, _)| *name).collect(),
        ));

        match self
            .download_components(updates_needed, &manifest.correlation_id, trace_id)
            .await
        {
            Ok(_) => {
                info!("All components downloaded successfully, transitioning to apply phase");
                self.state.send_replace(UpdateCoordinatorState::Applying);
            }
            Err(e) => {
                let error_status = LastUpdateResult {
                    correlation_id: manifest.correlation_id.clone(),
                    status: LastUpdateStatus::Failed,
                    consecutive_failures,
                    error_code: Some(LastUpdateErrorCode::DownloadFailed),
                    error_message: Some(format!("Failed to download components: {}", e)),
                };

                // 保存错误状态
                if let Err(save_err) = self.save_last_update_status(&error_status).await {
                    warn!("Failed to save error status: {}", save_err);
                }

                self.state
                    .send_replace(UpdateCoordinatorState::Failed(error_status));
                return Err(e);
            }
        }

        // 第六步：前置检查
        info!("Entering applying state, performing pre-checks");
        match self.perform_pre_application_checks().await {
            Ok(true) => {
                info!("Pre-checks passed, starting component application");
                // TODO: 实际应用组件更新（任务3.4, 3.5, 3.6）
                self.state.send_replace(UpdateCoordinatorState::Idle);
            }
            Ok(false) => {
                info!("Pre-checks not ready, waiting for next cycle");
                // 继续等待，下次循环再检查
            }
            Err(e) => {
                warn!("Pre-checks failed: {}", e);
                let error_status = LastUpdateResult {
                    correlation_id: Uuid::new_v4(),
                    status: LastUpdateStatus::Failed,
                    consecutive_failures: 1,
                    error_code: Some(LastUpdateErrorCode::PreCheckFailed),
                    error_message: Some(format!("Pre-application checks failed: {}", e)),
                };
                self.save_last_update_status(&error_status).await;
                self.state
                    .send_replace(UpdateCoordinatorState::Failed(error_status));
            }
        }
        // 第七步：应用更新，创建回滚守卫
        // 第八步：重启目标

        Ok(())
    }

    /// 下载所有组件
    async fn download_components(
        &self,
        components: Vec<(&'static str, UpdateComponent)>,
        correlation_id: &Uuid,
        trace_id: &Uuid,
    ) -> anyhow::Result<()> {
        use shared::downloader::{ChunkedDownloader, DownloadConfig};
        use std::sync::Arc;
        use tokio_util::sync::CancellationToken;

        if components.is_empty() {
            return Ok(());
        }

        info!(
            correlation_id = %correlation_id,
            trace_id = %trace_id,
            "Starting download of {} components",
            components.len()
        );

        // 创建下载器
        let downloader =
            ChunkedDownloader::with_config(DownloadConfig::default()).map_err(|e| {
                error!("Failed to create downloader: {}", e);
                anyhow::anyhow!("Failed to create downloader: {}", e)
            })?;

        // 创建取消令牌（可以在后续需要时用于取消下载）
        let cancellation_token = CancellationToken::new();

        // 并发下载所有组件
        let mut download_tasks = Vec::new();

        for (component_name, component) in components.into_iter() {
            let download_url = component.url;

            let expected_hash = component.sha256;
            let output_path = self.get_component_download_path(
                &component_name,
                &component.version,
                &download_url,
            )?;

            let span = info_span!(
                "Downloading component {}",
                component_name,
                trace_id = ?trace_id,
                correlation_id = ?correlation_id,
                component_name = component_name,
                output_path = ?output_path.display(),
            );
            let _enter = span.enter();

            // 克隆必要的变量
            let downloader_clone = downloader.clone();
            let download_url = download_url.clone();
            let output_path = output_path.clone();
            let expected_hash = expected_hash.clone();
            let cancellation_token = cancellation_token.clone();
            let trace_id = trace_id.clone();

            // 创建进度回调
            let progress_callback =
                Arc::new(move |progress: shared::downloader::DownloadProgress| {
                    info!(
                        component = &component_name,
                        downloaded_bytes = progress.downloaded_bytes,
                        total_bytes = progress.total_bytes,
                        completed_chunks = progress.completed_chunks,
                        total_chunks = progress.total_chunks,
                        current_speed = progress.current_speed,
                        eta_seconds = progress.eta_seconds,
                        "Download progress"
                    );
                });

            // 启动下载任务
            let download_task = tokio::spawn(async move {
                downloader_clone
                    .download(
                        &download_url,
                        &output_path,
                        Some(&expected_hash),
                        progress_callback,
                        cancellation_token,
                        &trace_id,
                    )
                    .await
            });

            download_tasks.push((component_name, download_task));
        }

        // 等待所有下载完成
        let mut downloaded_files = Vec::new();
        for (component_name, task) in download_tasks {
            match task.await {
                Ok(Ok(file_hash)) => {
                    info!(
                        "Component {} downloaded successfully, hash: {}",
                        component_name, file_hash
                    );
                    downloaded_files.push((component_name, file_hash));
                }
                Ok(Err(e)) => {
                    error!("Failed to download component {}: {}", component_name, e);
                    return Err(anyhow::anyhow!(
                        "Failed to download component {}: {}",
                        component_name,
                        e
                    ));
                }
                Err(e) => {
                    error!(
                        "Download task failed for component {}: {}",
                        component_name, e
                    );
                    return Err(anyhow::anyhow!(
                        "Download task failed for component {}: {}",
                        component_name,
                        e
                    ));
                }
            }
        }

        info!(
            "All {} components downloaded successfully",
            downloaded_files.len()
        );

        Ok(())
    }
    /// 获取组件下载路径
    fn get_component_download_path(
        &self,
        component_name: &str,
        version: &Version,
        download_url: &str,
    ) -> anyhow::Result<PathBuf> {
        // 尝试从下载url中获取文件名
        let file_name = download_url
            .split("/")
            .last()
            .map(|it| Cow::Borrowed(it))
            .unwrap_or_else(|| match component_name {
                "client" => Cow::Owned(format!("echowave-client-{}.exe", version)),
                "mirror" => Cow::Owned(format!("echowave-engine-{}.tgz", version)),
                "agent" => Cow::Owned(format!("echowave-agent-{}.bin", version)),
                "wsl" => Cow::Owned(format!("wsl-{}.msi", version)),
                _ => Cow::Owned(format!("{}-{}.bin", component_name, version)),
            });

        let download_path = self
            .settings_service
            .get_cache_path()
            .join(file_name.as_ref());

        Ok(download_path)
    }

    async fn perform_pre_application_checks(&self) -> anyhow::Result<bool> {
        todo!()
    }

    /// 计算指数退避延迟
    fn calculate_backoff_delay(consecutive_failures: u32) -> u64 {
        // 指数退避：5分钟 -> 15分钟 -> 45分钟 -> 2小时 -> 6小时 -> 24小时（最大）
        let base_delay = 300u64; // 5分钟
        let max_delay = 86400u64; // 24小时

        if consecutive_failures == 0 {
            return base_delay;
        }

        let exponential_delay = base_delay * 3u64.pow(consecutive_failures.saturating_sub(1));
        exponential_delay.min(max_delay)
    }
}
