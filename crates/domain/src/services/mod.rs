//! 核心业务服务模块
//!
//! 本模块包含所有核心业务服务：
//!
//! ## 最小服务集（应用启动时）
//! - SettingsService: 设置管理服务 - 提供基础配置支持
//! - UserService: 用户管理服务 - 提供登录功能
//!
//! ## 完整服务集（用户登录后）
//! - NetworkService: 网络状态管理服务 - 网络状态检查
//! - SystemCheckService: 系统检查服务 - 系统环境检查
//! - UpdateService: 更新管理服务 - 更新管理
//! - TaskAcceptanceService: 任务接单服务 - 核心业务功能
//!
//! ## 生命周期管理
//!
//! 服务采用分阶段启动模式：
//! 1. 应用启动：启动最小服务集支持用户登录
//! 2. 用户登录：启动完整服务集，提供完整功能
//! 3. 用户登出：停止完整服务集，保留最小服务集支持重新登录
//! 4. 应用关闭：停止所有服务

pub mod device_service;
pub mod network_service;
pub mod settings_service;
pub mod system_check;
pub mod task_acceptance_service;
pub mod update_service;
pub mod user_service;

use crate::adapters::ModuleAdapter;

use serde::{Deserialize, Serialize};
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use tokio::task::JoinHandle;
use tokio_util::sync::CancellationToken;

use crate::{
    CoreResult,
    adapters::{
        agent::{AgentAdapter, AgentConfig},
        file::FileAdapter,
        helper::{HelperAdapter, HelperConfig},
        http::HttpAdapter,
    },
    config::ContextConfig,
};

/// 类型别名：异步服务操作结果
///
/// 动态派发需要确切的类型或者使用 Box 进行类型擦出
///
/// # 实现模式
///
/// 建议使用以下模式实现 async trait 方法：
///
/// ```rust
/// struct MyService{}
/// impl CoreService for MyService {
///     // 1. trait 方法只做简单的 Box::pin 包装
///     fn start(&mut self) -> ServiceFuture<'_, ()> {
///         Box::pin(self.start_impl())
///     }
/// }
///
/// impl MyService {
///     // 2. 真正的异步实现放在 _impl 方法中
///     async fn start_impl(&mut self) -> CoreResult<()> {
///         // 正常的 async 代码，无需 Box::pin
///         Ok(())
///     }
/// }
/// ```
///
/// 这种模式的优点：
/// - 逻辑分离：trait 实现和具体逻辑分开
/// - 代码清晰：具体实现使用原生 async 语法
/// - 易于测试：可以直接测试 _impl 方法
/// - 易于维护：无需在每个方法中写 Box::pin(async move {})
type ServiceFuture<'a, T> = Pin<Box<dyn Future<Output = CoreResult<T>> + Send + 'a>>;

/// 服务状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ServiceStatus {
    /// 服务未启动
    Stopped,
    /// 服务正在启动
    Starting,
    /// 服务运行中
    Running,
    /// 服务正在停止
    Stopping,
    /// 服务出现错误
    Error(String),
}

/// 核心服务特征
///
/// 所有核心业务服务都必须实现此特征
pub trait CoreService: Send + Sync {
    /// 服务名称
    fn name(&self) -> &str;

    /// 获取当前服务状态
    fn status(&self) -> ServiceStatus;

    /// 启动服务
    fn start(&self) -> ServiceFuture<'_, ()>;

    /// 停止服务
    fn stop(&self) -> ServiceFuture<'_, ()>;

    /// 健康检查
    fn health_check(&self) -> ServiceFuture<'_, bool>;
}

/// 服务配置特征
pub trait ServiceConfig: Send + Sync + Clone {
    /// 验证配置是否有效
    fn validate(&self) -> CoreResult<()>;

    /// 获取配置名称
    fn name(&self) -> &str;
}

/// 服务工厂特征
///
/// 用于创建和管理服务实例
pub trait ServiceFactory: Send + Sync {
    type Service: CoreService;
    type Config: ServiceConfig;

    /// 创建服务实例
    fn create_service(&self, config: Self::Config) -> ServiceFuture<'_, Self::Service>;

    /// 获取默认配置
    fn default_config(&self) -> Self::Config;
}

// 重新导出服务类型
pub use device_service::{DeviceInfo, DeviceService};
pub use network_service::{NetworkService, NetworkState};
pub use settings_service::{SettingsService, SettingsServiceConfig, UserSettingsState};
pub use system_check::{SystemCheckConfig, SystemCheckService};
pub use task_acceptance_service::{
    AcceptanceTrigger, TaskAcceptanceConfig, TaskAcceptanceService, TaskAcceptanceStatus,
    TaskExecutionStatus,
};
pub use update_service::{UpdateConfig, UpdateService};
pub use user_service::{UserService, UserServiceConfig, UserState};

/// 服务管理器结构
///
/// 用于统一管理所有服务的生命周期
pub struct ServiceManager {
    pub device: Arc<DeviceService>,
    pub system_check: Arc<SystemCheckService>,
    pub update: Arc<UpdateService>,
    pub task_acceptance: Arc<TaskAcceptanceService>,
    pub user: Arc<UserService>,
    pub network: Arc<NetworkService>,
    pub settings: Arc<SettingsService>,

    pub http_adapter: Arc<HttpAdapter>,
    pub file_adapter: Arc<FileAdapter>,
    pub agent_adapter: Arc<AgentAdapter>,
    pub helper_adapter: Arc<HelperAdapter>,
    signal: CancellationToken,
}

impl ServiceManager {
    /// 创建新的服务管理器
    pub fn new(config: Arc<ContextConfig>, signal: CancellationToken) -> CoreResult<Self> {
        // === 创建适配器

        // 创建全局的 HTTP 适配器，直接传入 TokenProvider 和 BusinessErrorHandler
        let http_config = crate::adapters::http::HttpConfig {
            base_url: config.environment.api_url.clone(),
            timeout_ms: 30000,
            user_agent: "EchoWave-Client/1.0".to_string(),
            ..Default::default()
        };
        let http_adapter = std::sync::Arc::new(HttpAdapter::new(http_config).map_err(|e| {
            crate::CoreError::service_error(&format!("Failed to create HTTP adapter: {}", e))
        })?);
        // 创建文件适配器
        let file_adapter = std::sync::Arc::new(crate::adapters::file::FileAdapter::new(
            crate::adapters::file::FileConfig::default(),
        )?);
        let agent_adapter = std::sync::Arc::new(AgentAdapter::new(
            config.distro_name.clone(),
            AgentConfig::default(),
        )?);
        let helper_adapter = std::sync::Arc::new(HelperAdapter::new(HelperConfig::default())?);
        // ===创建核心服务
        let device = DeviceService::new(file_adapter.clone())?;
        let update = UpdateService::new(UpdateConfig::default())?;
        let user = UserService::new(http_adapter.clone(), file_adapter.clone())?;
        let network = NetworkService::new(agent_adapter.clone())?;
        let device_arc = Arc::new(device);
        let user_arc = Arc::new(user);
        let settings =
            SettingsService::new(SettingsServiceConfig::default(), file_adapter.clone())?;
        let task_acceptance = TaskAcceptanceService::new(
            TaskAcceptanceConfig::default(),
            http_adapter.clone(),
            agent_adapter.clone(),
            device_arc.clone(),
            user_arc.clone(),
        )?;

        // 用 Arc 包装服务以支持共享
        let update_arc = Arc::new(update);
        let task_acceptance_arc = Arc::new(task_acceptance);
        let network_arc = Arc::new(network);
        let settings_arc = Arc::new(settings);
        let system_check = SystemCheckService::new(
            SystemCheckConfig::default(),
            agent_adapter.clone(),
            helper_adapter.clone(),
            settings_arc.clone(),
            config.clone(),
        )?;
        let system_check_arc = Arc::new(system_check);

        http_adapter.set_interceptor(user_arc.clone());

        Ok(Self {
            device: device_arc,
            system_check: system_check_arc,
            update: update_arc,
            task_acceptance: task_acceptance_arc,
            user: user_arc,
            network: network_arc,
            settings: settings_arc,
            http_adapter: http_adapter,
            file_adapter: file_adapter,
            agent_adapter: agent_adapter,
            helper_adapter: helper_adapter,
            signal,
        })
    }

    /// 启动最小服务集（应用启动时）
    ///
    /// 最小服务集包括设置管理、设备服务和用户管理服务，
    /// 这些服务在应用启动时就需要可用，用于支持基本功能如用户登录
    pub async fn start_basic_services(&self) -> CoreResult<()> {
        tracing::info!("Starting minimal services for application bootstrap");

        // 按依赖顺序启动最小服务集
        // 1. 设置服务 - 应用需要读取基础配置
        if self.settings.status() != ServiceStatus::Running {
            tracing::info!("Starting settings service");
            self.settings.start().await?;
            tracing::info!("Settings service started");
        }

        // 2. 设备服务 - 设备识别和机器ID管理
        if self.device.status() != ServiceStatus::Running {
            tracing::info!("Starting device service");
            self.device.start().await?;
            tracing::info!("Device service started");
        }

        // 3. 用户服务 - 提供登录功能
        if self.user.status() != ServiceStatus::Running {
            tracing::info!("Starting user service");
            self.user.start().await?;
            tracing::info!("User service started");
        }

        tracing::info!("Minimal services started successfully");
        Ok(())
    }

    /// 启动完整服务集（用户登录后）
    ///
    /// 完整服务集包括网络状态、系统检查、更新管理, 不包括任务接单服务，
    /// 这些服务只有在用户成功登录后才会启动
    pub async fn start_full_services(&self) -> CoreResult<()> {
        tracing::info!("Starting full services after user login");

        // 按依赖顺序启动完整服务集
        // 1. 网络服务 - 网络状态检查，需要用户身份验证
        if self.network.status() != ServiceStatus::Running {
            tracing::info!("Starting network service");
            self.network.start().await?;
            tracing::info!("Network service started");
        }

        // 2. 系统检查服务 - 系统环境检查
        if self.system_check.status() != ServiceStatus::Running {
            tracing::info!("Starting system check service");
            self.system_check.start().await?;
            tracing::info!("System check service started");
        }

        // 3. 更新服务 - 更新管理
        if self.update.status() != ServiceStatus::Running {
            tracing::info!("Starting update service");
            self.update.start().await?;
            tracing::info!("Update service started");
        }

        tracing::info!("Full services started successfully");
        Ok(())
    }

    /// 停止基础服务
    ///
    /// 按相反顺序停止基础服务（用户服务 -> 设置服务）
    pub async fn stop_basic_services(&self) -> CoreResult<()> {
        tracing::info!("Stopping basic services");

        // 按相反顺序停止基础服务
        let _ = self.user.stop().await;
        let _ = self.device.stop().await;
        let _ = self.settings.stop().await;

        tracing::info!("Basic services stopped");
        Ok(())
    }

    /// 停止完整服务集（用户登出时）
    ///
    /// 按相反顺序停止完整服务集，保留最小服务集以支持重新登录
    pub async fn stop_full_services(&self) -> CoreResult<()> {
        tracing::info!("Stopping full services after user logout");

        // 按相反顺序停止完整服务集
        let _ = self.task_acceptance.stop().await;
        let _ = self.update.stop().await;
        let _ = self.system_check.stop().await;
        let _ = self.network.stop().await;

        tracing::info!("Full services stopped");
        Ok(())
    }

    /// 获取所有服务的健康状态
    pub async fn health_check_all(&self) -> CoreResult<Vec<(String, bool)>> {
        let mut results = Vec::new();

        results.push((
            self.device.name().to_string(),
            self.device.health_check().await?,
        ));
        results.push((
            self.system_check.name().to_string(),
            self.system_check.health_check().await?,
        ));
        results.push((
            self.update.name().to_string(),
            self.update.health_check().await?,
        ));
        results.push((
            self.task_acceptance.name().to_string(),
            self.task_acceptance.health_check().await?,
        ));
        results.push((
            self.user.name().to_string(),
            self.user.health_check().await?,
        ));
        results.push((
            self.network.name().to_string(),
            self.network.health_check().await?,
        ));
        results.push((
            self.settings.name().to_string(),
            self.settings.health_check().await?,
        ));

        Ok(results)
    }

    /// 启动服务生命周期管理器
    ///
    /// 监听用户登录状态变化，自动管理完整服务集的启动和停止
    pub fn start_lifecycle_manager(mgr: ServiceManager) -> JoinHandle<()> {
        tokio::spawn(async move {
            tracing::info!("Starting service lifecycle manager");
            // 先启动基础服务
            if let Err(err) = mgr.start_basic_services().await {
                tracing::error!("Failed to start basic services: {}", err);
                return;
            }
            tracing::info!("Basic services started successfully");

            let mut user_state_rx = mgr.user.state_receiver();
            let mut system_state_rx = mgr.system_check.state_receiver();
            let mut agent_state_rx = mgr.agent_adapter.state_receiver();
            let mut is_logged_in = false;
            let mut auto_accept_tasks = mgr.settings.get_current_settings().auto_accept_tasks;
            let mut is_system_check_passed = false;
            let mut last_agent_error_count = 0u32;
            let mut is_mirror_restarting = false;
            loop {
                tokio::select! {
                    // 用户登录状态变化，停止和启动完整服务集
                    result = user_state_rx.changed() => {
                        if let Ok(()) = result {
                            let n_is_logged_in = user_state_rx.borrow().is_logged_in;

                            // 检查登录状态变化
                            if n_is_logged_in != is_logged_in && n_is_logged_in {
                                tracing::info!("用户已登录，启动完整服务集");
                                if let Err(err) = mgr.start_full_services().await {
                                    tracing::error!("启动完整服务集失败: {}", err);
                                }
                                is_logged_in = true;
                            } else if n_is_logged_in != is_logged_in && !n_is_logged_in {
                                tracing::info!("用户登出，停止完整服务集");
                                if let Err(err) = mgr.stop_full_services().await {
                                    tracing::error!("Failed to stop full services: {}", err);
                                }
                                // 重置状态
                                is_logged_in = false;
                                is_system_check_passed = false;
                                auto_accept_tasks = mgr.settings.get_current_settings().auto_accept_tasks;
                            }
                        } else {
                            tracing::info!("用户状态接收器关闭");
                            break;
                        }
                    }
                    // 系统检查状态变化，启动和停止任务接单服务
                    result = system_state_rx.changed() => {
                        if let Ok(()) = result {
                            let n_is_system_check_passed = system_state_rx.borrow().all_checks_passed();
                            if n_is_system_check_passed && auto_accept_tasks {
                                // 自动接单仅触发一次
                                auto_accept_tasks = false;
                                tracing::info!("系统检查通过，开始自动接单服务");
                                if let Err(err) = mgr.task_acceptance.start_auto_accepting_tasks().await {
                                    tracing::error!("启动任务接单服务失败: {}", err);
                                }
                            } else if n_is_system_check_passed != is_system_check_passed && !n_is_system_check_passed {
                                tracing::info!("系统检查失败，停止任务接单服务");
                                if let Err(err) = mgr.task_acceptance.stop().await {
                                    tracing::error!("停止任务接单服务失败: {}", err);
                                }
                            }
                            is_system_check_passed = n_is_system_check_passed;
                        }
                    }
                    // Agent状态变化，监听连接失败并触发WSL镜像重启机制
                    result = agent_state_rx.changed() => {
                        if let Ok(()) = result {
                            let agent_state = agent_state_rx.borrow().clone();

                            // 仅在用户已登录且系统检查服务运行时才处理Agent错误
                            if is_logged_in && mgr.system_check.status() == ServiceStatus::Running {
                                match &agent_state {
                                    crate::adapters::agent::AgentState::Error(error_msg) => {
                                        last_agent_error_count += 1;
                                        tracing::warn!(
                                            "检测到Agent连接错误 (第{}次): {}",
                                            last_agent_error_count, error_msg
                                        );

                                        // 如果连续错误且不在重启过程中，触发WSL镜像重启
                                        if last_agent_error_count >= 3 && !is_mirror_restarting {
                                            tracing::error!(
                                                "Agent连接连续失败{}次，开始WSL镜像重启恢复流程",
                                                last_agent_error_count
                                            );
                                            is_mirror_restarting = true;

                                            // 异步执行WSL镜像重启和完整系统检查验证
                                            let system_check_service = mgr.system_check.clone();
                                            let agent_adapter = mgr.agent_adapter.clone();
                                            let trace_id = uuid::Uuid::new_v4();
                                            tokio::spawn(async move {
                                                tracing::info!(
                                                    "开始执行WSL镜像重启恢复流程 (trace_id: {})",
                                                    trace_id
                                                );

                                                // 第一阶段：触发完整系统检查，这将自动修复WSL镜像问题
                                                system_check_service.run_all_checks(trace_id).await;

                                                // 第二阶段：验证系统检查结果
                                                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                                                let system_state = system_check_service.get_current_state();

                                                if system_state.all_checks_passed() {
                                                    tracing::info!(
                                                        "系统检查验证通过，WSL镜像重启成功 (trace_id: {})",
                                                        trace_id
                                                    );

                                                    // 第三阶段：验证Agent连接恢复
                                                    let mut retry_count = 0;
                                                    let max_retries = 5;

                                                    while retry_count < max_retries {
                                                        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

                                                        if agent_adapter.is_connected().await {
                                                            tracing::info!(
                                                                "Agent连接已恢复，WSL镜像重启完全成功 (trace_id: {}, 重试次数: {})",
                                                                trace_id, retry_count + 1
                                                            );
                                                            break;
                                                        }

                                                        retry_count += 1;
                                                        tracing::warn!(
                                                            "Agent连接尚未恢复，等待重试 ({}/{}) (trace_id: {})",
                                                            retry_count, max_retries, trace_id
                                                        );
                                                    }

                                                    if retry_count >= max_retries {
                                                        tracing::error!(
                                                            "WSL镜像重启后Agent连接仍未恢复，可能需要手动干预 (trace_id: {})",
                                                            trace_id
                                                        );
                                                    }
                                                } else {
                                                    // 系统检查失败，记录详细错误信息
                                                    let failed_checks: Vec<String> = system_state.check_results
                                                        .iter()
                                                        .enumerate()
                                                        .filter_map(|(i, result)| {
                                                            if matches!(result.status, crate::services::system_check::CheckStatus::AutoFixFailed) {
                                                                Some(format!("{}({})",
                                                                    crate::services::system_check::SystemCheckKind::from_index(i).name(),
                                                                    result.message
                                                                ))
                                                            } else {
                                                                None
                                                            }
                                                        })
                                                        .collect();

                                                    tracing::error!(
                                                        "WSL镜像重启验证失败，系统检查未通过 (trace_id: {}), 失败项目: {:?}",
                                                        trace_id, failed_checks
                                                    );

                                                    // 可以在这里添加更多的恢复策略，比如：
                                                    // - 通知用户需要手动干预
                                                    // - 尝试其他修复方案
                                                    // - 记录详细的故障报告供技术支持分析
                                                }

                                                tracing::info!(
                                                    "WSL镜像重启恢复流程完成 (trace_id: {})",
                                                    trace_id
                                                );
                                            });
                                        }
                                    }
                                    crate::adapters::agent::AgentState::Connected => {
                                        // Agent重新连接成功，重置错误计数和重启标志
                                        if last_agent_error_count > 0 || is_mirror_restarting {
                                            tracing::info!(
                                                "Agent重新连接成功，重置错误恢复状态 (之前错误次数: {})",
                                                last_agent_error_count
                                            );
                                            last_agent_error_count = 0;
                                            is_mirror_restarting = false;
                                        }
                                    }
                                    crate::adapters::agent::AgentState::RestartingWslMirror { progress_message, .. } => {
                                        tracing::info!(
                                            "WSL镜像重启进行中: {}",
                                            progress_message
                                        );
                                        // 保持is_mirror_restarting = true直到连接恢复
                                    }
                                    _ => {
                                        // 其他状态（Disconnected, Connecting, Reconnecting）不重置计数
                                        // 让重连逻辑自然进行
                                    }
                                }
                            }
                        }
                    }
                    _ = mgr.signal.cancelled() => {
                        tracing::info!("Service lifecycle manager cancelled");
                        break;
                    }
                }
            }

            // 优雅关闭：停止所有服务
            if let Err(err) = mgr.stop_full_services().await {
                tracing::error!("Failed to stop full services during shutdown: {}", err);
            }
            if let Err(err) = mgr.stop_basic_services().await {
                tracing::error!("Failed to stop basic services during shutdown: {}", err);
            }
            tracing::info!("Service lifecycle manager stopped");
        })
    }

    /// 获取全局 HTTP 适配器的 Arc 引用
    pub fn get_http_adapter(&self) -> Arc<HttpAdapter> {
        self.http_adapter.clone()
    }
}

impl Drop for ServiceManager {
    fn drop(&mut self) {
        self.signal.cancel();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_service_status_serialization() {
        let statuses = vec![
            ServiceStatus::Stopped,
            ServiceStatus::Starting,
            ServiceStatus::Running,
            ServiceStatus::Stopping,
            ServiceStatus::Error("test error".to_string()),
        ];

        for status in statuses {
            let json = serde_json::to_string(&status).unwrap();
            let deserialized: ServiceStatus = serde_json::from_str(&json).unwrap();
            assert_eq!(status, deserialized);
        }
    }

    #[tokio::test]
    async fn test_service_manager_creation() {
        let signal = CancellationToken::new();
        let result = ServiceManager::new(Arc::new(ContextConfig::default()), signal);
        // 这个测试可能因为依赖问题而失败，但至少验证了接口
        println!("Service manager creation result: {:?}", result.is_ok());
    }
}
