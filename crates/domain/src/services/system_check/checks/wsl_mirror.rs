//! WSL镜像检查模块
//!
//! 负责检查WSL镜像的下载、安装、验证和Agent健康状态

use super::super::types::{CheckStatus, ManualAction, ProgressInfo, WSLMirrorStatus};
use crate::CoreError;
use crate::CoreResult;
use crate::adapters::agent::AgentAdapter;
use crate::services::settings_service::SettingsService;
use crate::utils::wsl_manager::WslManager;
use shared::disk_capacity;
use shared::downloader::{ChunkedDownloader, DownloadProgress};
use shared::hash::{HashAlgorithm, HashValidator};
use std::{path::Path, sync::Arc};
use tokio::sync::watch;
use tokio_util::sync::CancellationToken;
use uuid::Uuid;

/// WSL镜像检查结果
pub struct WslMirrorCheckResult {
    pub status: CheckStatus,
    pub message: String,
    pub manual_action: Option<ManualAction>,
    pub progress: Option<ProgressInfo>,
    pub details: Option<serde_json::Value>,
    pub wsl_mirror_status: WSLMirrorStatus,
}

/// 检查 WSL 镜像（本地检查 和 通过 Agent）
///
/// ## 详细执行步骤
///
/// 1. 根据 settings_service 里面的 distro_install_path，检查该目录所在磁盘是否大于 50 GB
/// 2. 检查 WSL 是否导入镜像
/// 3. 启动 WSL 镜像，连接 agent 服务，发送 health 指令，如果不成功，给出镜像已经损坏
///
/// ## 自动解决步骤
///
/// - 如果磁盘检查未通过，则指引用户跳转到设置页面更改数据安装目录
/// - 如果镜像未下载，则开始下载（先检查本地是否下载过，如果下载过，则跳过下载, 或断点续传）
pub async fn check_wsl_mirror(
    agent_adapter: Arc<AgentAdapter>,
    settings_service: Arc<SettingsService>,
    context_config: &crate::config::ContextConfig,
    auto_fix_enabled: bool,
    state_sender: &watch::Sender<super::super::types::SystemState>,
    trace_id: &Uuid,
) -> CoreResult<WslMirrorCheckResult> {
    tracing::info!("Checking WSL mirror");

    // 非Windows系统不支持WSL
    #[cfg(not(target_os = "windows"))]
    {
        tracing::warn!("WSL mirror check not supported on non-Windows systems");
        return Ok(WslMirrorCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: "不支持的系统".to_string(),
            manual_action: Some(ManualAction::ContactSupport),
            progress: None,
            details: Some(serde_json::json!({
                "platform": std::env::consts::OS,
                "error": "WSL only supported on Windows"
            })),
            wsl_mirror_status: WSLMirrorStatus::default(),
        });
    }

    #[cfg(target_os = "windows")]
    {
        tracing::info!("Starting WSL mirror check on Windows");

        // 1. 获取安装路径配置
        let settings = settings_service.get_current_settings();
        let install_path = std::path::Path::new(&settings.distro_install_path);
        tracing::info!("WSL镜像安装路径: {}", install_path.display());

        // 2. 检查磁盘空间
        match check_disk_space_for_mirror(install_path, context_config.min_disk_space).await {
            Ok(()) => {
                tracing::info!("磁盘空间检查通过");
            }
            Err(e) => {
                tracing::warn!("磁盘空间检查失败: {}", e);
                return Ok(WslMirrorCheckResult {
                    status: CheckStatus::AutoFixFailed,
                    message: format!("磁盘空间不足: {}", e),
                    manual_action: Some(ManualAction::ChangeInstallPath),
                    progress: None,
                    details: Some(serde_json::json!({
                        "install_path": settings.distro_install_path,
                        "required_space_gb": context_config.min_disk_space / (1024 * 1024 * 1024),
                        "error": e.to_string()
                    })),
                    wsl_mirror_status: WSLMirrorStatus::default(),
                });
            }
        }

        // 4. 检查WSL镜像状态
        match check_wsl_mirror_status(&context_config.distro_name, state_sender).await {
            Ok(Some(mirror_status)) => {
                // 镜像已安装，检查Agent健康状态
                match check_agent_health(agent_adapter.clone(), &context_config.distro_name).await {
                    Ok(()) => {
                        tracing::info!("Agent健康检查通过");
                        Ok(WslMirrorCheckResult {
                            status: CheckStatus::Passed,
                            message: "WSL镜像运行正常".to_string(),
                            manual_action: None,
                            progress: None,
                            details: Some(serde_json::json!({
                                "distro_name": context_config.distro_name,
                                "install_path": settings.distro_install_path,
                                "agent_health": "ok"
                            })),
                            wsl_mirror_status: mirror_status,
                        })
                    }
                    Err(e) => {
                        tracing::warn!("Agent健康检查失败: {}", e);
                        if auto_fix_enabled {
                            handle_mirror_repair(
                                install_path,
                                context_config,
                                settings_service,
                                agent_adapter,
                                state_sender,
                                &trace_id,
                            )
                            .await
                        } else {
                            Ok(WslMirrorCheckResult {
                                status: CheckStatus::AutoFixFailed,
                                message: "WSL镜像已损坏".to_string(),
                                manual_action: None,
                                progress: None,
                                details: Some(serde_json::json!({
                                    "distro_name": context_config.distro_name,
                                    "agent_error": e.to_string()
                                })),
                                wsl_mirror_status: mirror_status,
                            })
                        }
                    }
                }
            }
            Ok(None) => {
                // 镜像未安装
                tracing::info!("WSL分发未导入: {}", context_config.distro_name);

                state_sender.send_modify(|state| {
                    state.wsl_mirror_status.is_installed = false;
                    state.wsl_mirror_status.is_downloaded = false;
                });

                if auto_fix_enabled {
                    // 开始下载和导入流程
                    download_and_import_mirror(
                        &settings_service.get_distro_install_path(),
                        context_config,
                        settings_service,
                        agent_adapter,
                        state_sender,
                        &trace_id,
                    )
                    .await
                } else {
                    Ok(WslMirrorCheckResult {
                        status: CheckStatus::AutoFixFailed,
                        message: "WSL镜像未安装".to_string(),
                        manual_action: None,
                        progress: None,
                        details: Some(serde_json::json!({
                            "distro_name": context_config.distro_name,
                            "status": "not_installed"
                        })),
                        wsl_mirror_status: WSLMirrorStatus::default(),
                    })
                }
            }
            Err(e) => {
                tracing::warn!("WSL镜像状态检查失败: {}", e);
                Err(e)
            }
        }
    }
}

/// 检查安装路径的磁盘空间
async fn check_disk_space_for_mirror(install_path: &Path, required_space: u64) -> CoreResult<()> {
    tracing::info!("检查磁盘空间: {}", install_path.display());

    let disk_info = disk_capacity::get_disk_capacity(install_path)
        .map_err(|e| CoreError::operation_failed(&format!("获取磁盘信息失败: {}", e)))?;

    let available_space = disk_info.available_bytes;

    tracing::debug!(
        "磁盘空间检查: 需要 {} GB, 可用 {} GB",
        required_space / (1024 * 1024 * 1024),
        available_space / (1024 * 1024 * 1024)
    );

    if available_space < required_space {
        return Err(CoreError::operation_failed(&format!(
            "磁盘空间不足，需要 {} GB，可用 {} GB",
            required_space / (1024 * 1024 * 1024),
            available_space / (1024 * 1024 * 1024)
        )));
    }

    Ok(())
}

/// 检查WSL镜像状态
async fn check_wsl_mirror_status(
    distro_name: &str,
    state_sender: &watch::Sender<super::super::types::SystemState>,
) -> CoreResult<Option<WSLMirrorStatus>> {
    tracing::info!("检查WSL镜像状态: {}", distro_name);

    // 检查分发是否已导入并且运行
    match WslManager::is_distribution_imported(distro_name).await? {
        Some(distribution) => {
            tracing::info!("WSL分发已导入: {:?}", distribution);

            let mirror_status = WSLMirrorStatus {
                is_installed: true,
                is_downloaded: true,
                download_progress: 100,
                install_progress: 100,
                version: Some(
                    distribution
                        .version
                        .map(|v| v.to_string())
                        .unwrap_or_default(),
                ),
            };

            // 更新状态
            state_sender.send_modify(|state| {
                state.wsl_mirror_status = mirror_status.clone();
            });

            Ok(Some(mirror_status))
        }
        None => {
            tracing::info!("WSL分发未导入: {}", distro_name);

            // 更新状态
            state_sender.send_modify(|state| {
                state.wsl_mirror_status.is_installed = false;
                state.wsl_mirror_status.is_downloaded = false;
            });

            Ok(None)
        }
    }
}

/// 检查Agent健康状态
async fn check_agent_health(agent_adapter: Arc<AgentAdapter>, distro_name: &str) -> CoreResult<()> {
    tracing::info!("检查Agent健康状态: {}", distro_name);

    // 连接Agent适配器并发送health指令
    agent_adapter.start().await.map_err(|e| {
        tracing::error!("启动Agent失败: {}", e);
        CoreError::operation_failed(&format!("启动Agent失败: {}", e))
    })?;

    match agent_adapter.check_health().await {
        Ok(()) => {
            tracing::info!("Agent健康检查成功");
            Ok(())
        }
        Err(e) => {
            tracing::error!("Agent健康检查失败: {}", e);
            Err(CoreError::operation_failed(&format!(
                "Agent健康检查失败: {}",
                e
            )))
        }
    }
}

/// 处理镜像修复
async fn handle_mirror_repair(
    install_path: &Path,
    context_config: &crate::config::ContextConfig,
    settings_service: Arc<SettingsService>,
    agent_adapter: Arc<AgentAdapter>,
    state_sender: &watch::Sender<super::super::types::SystemState>,
    trace_id: &Uuid,
) -> CoreResult<WslMirrorCheckResult> {
    tracing::info!("开始镜像修复流程");

    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            check_result.status = CheckStatus::AutoFixing;
            check_result.message = "正在修复WSL镜像...".to_string();
            check_result.progress = Some(ProgressInfo {
                operation: "检测镜像问题".to_string(),
                percent: 10,
                current_step: "分析镜像状态".to_string(),
            });
        }
    });

    // 1. 尝试删除损坏的分发
    if let Err(e) = WslManager::unregister_distribution(&context_config.distro_name).await {
        tracing::warn!("删除损坏分发失败: {}", e);
    }

    // 2. 检查镜像文件是否存在
    let distro_install_path = settings_service.get_distro_install_path();
    let cache_dir = distro_install_path
        .parent()
        .unwrap_or_else(|| std::path::Path::new("."))
        .join("download_cache");

    let mirror_path = cache_dir.join(format!("{}.tar", context_config.distro_name));

    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            if let Some(progress) = &mut check_result.progress {
                progress.operation = "检查镜像文件".to_string();
                progress.percent = 30;
                progress.current_step = "验证本地缓存".to_string();
            }
        }
    });

    if !mirror_path.exists() {
        // 镜像文件不存在，需要重新下载
        tracing::info!("镜像文件不存在，需要重新下载: {}", mirror_path.display());

        download_mirror(
            context_config,
            &settings_service.get_distro_install_path(),
            state_sender,
            &trace_id,
        )
        .await?;
    }

    // 3. 重新导入镜像
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            if let Some(progress) = &mut check_result.progress {
                progress.operation = "重新导入镜像".to_string();
                progress.percent = 70;
                progress.current_step = "导入WSL分发".to_string();
            }
        }
    });

    WslManager::import_distribution(&context_config.distro_name, &mirror_path, install_path)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("重新导入镜像失败: {}", e)))?;

    // 4. 设置为WSL2
    WslManager::set_wsl2_version(&context_config.distro_name)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("设置WSL2版本失败: {}", e)))?;

    // 5. 启动分发
    agent_adapter.get_version().await.map_err(|e| {
        tracing::error!("启动Agent失败: {}", e);
        CoreError::operation_failed(&format!("启动Agent失败: {}", e))
    })?;

    state_sender.send_modify(|state| {
        state.wsl_mirror_status.is_installed = true;
        state.wsl_mirror_status.is_downloaded = true;
        state.wsl_mirror_status.download_progress = 100;
        state.wsl_mirror_status.install_progress = 100;
    });

    tracing::info!("镜像修复完成");
    Ok(WslMirrorCheckResult {
        status: CheckStatus::Passed,
        message: "WSL镜像修复完成".to_string(),
        manual_action: None,
        progress: None,
        details: Some(serde_json::json!({
            "distro_name": context_config.distro_name,
            "install_path": install_path.to_string_lossy(),
            "repair_completed": true
        })),
        wsl_mirror_status: WSLMirrorStatus {
            is_installed: true,
            is_downloaded: true,
            download_progress: 100,
            install_progress: 100,
            version: Some("修复完成".to_string()),
        },
    })
}

fn parse_filename_from_url<'a>(url: &'a str, extension: &str) -> Option<&'a str> {
    url.split("/")
        .last()
        .filter(|path| path.ends_with(extension))
}

/// 下载WSL镜像
async fn download_mirror(
    context_config: &crate::config::ContextConfig,
    cache_dir: &std::path::Path,
    state_sender: &watch::Sender<super::super::types::SystemState>,
    trace_id: &Uuid,
) -> CoreResult<()> {
    let mirror_url = context_config.environment.mirror_url.clone();
    let expected_hash = context_config.environment.mirror_hash.clone();
    let filename = parse_filename_from_url(&mirror_url, ".tar.gz")
        .or_else(|| parse_filename_from_url(&mirror_url, ".tar"))
        .unwrap_or("echowave-engine.tar");

    tracing::info!("开始下载WSL镜像: {}", mirror_url);

    // 创建缓存目录
    tokio::fs::create_dir_all(&cache_dir)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("创建缓存目录失败: {}", e)))?;

    let mirror_path = cache_dir.join(filename);
    let download_url = mirror_url.clone();

    // 1. 创建分块下载器
    let downloader = ChunkedDownloader::new()
        .map_err(|e| CoreError::operation_failed(&format!("创建下载器失败: {}", e)))?;

    // 3. 设置进度回调
    let state_sender_clone = state_sender.clone();
    let progress_callback = Arc::new(move |progress: DownloadProgress| {
        let percent = if progress.total_bytes > 0 {
            ((progress.downloaded_bytes as f64 / progress.total_bytes as f64) * 100.0) as u8
        } else {
            0
        };

        let speed_mb = progress.current_speed / (1024.0 * 1024.0);
        let eta_text = if let Some(eta) = progress.eta_seconds {
            format!("剩余 {} 秒", eta)
        } else {
            "计算中...".to_string()
        };

        state_sender_clone.send_modify(|state| {
            state.wsl_mirror_status.download_progress = percent;

            // 更新检查结果进度
            if let Some(check_result) = state.check_results.get_mut(5) {
                check_result.progress = Some(ProgressInfo {
                    operation: "正在下载WSL镜像".to_string(),
                    percent,
                    current_step: format!(
                        "速度 {:.1} MB/s, 已完成 {}/{} 块, {}",
                        speed_mb, progress.completed_chunks, progress.total_chunks, eta_text
                    ),
                });
            }
        });
    });

    // 4. 执行下载
    let cancellation_token = CancellationToken::new();

    match downloader
        .download(
            &download_url,
            &mirror_path,
            Some(&expected_hash),
            progress_callback,
            cancellation_token,
            &trace_id,
        )
        .await
    {
        Ok(actual_hash) => {
            tracing::info!("WSL镜像下载成功: {}", mirror_path.display());
            tracing::info!("文件哈希: {}", actual_hash);

            // 更新下载状态
            state_sender.send_modify(|state| {
                state.wsl_mirror_status.is_downloaded = true;
                state.wsl_mirror_status.download_progress = 100;
            });

            Ok(())
        }
        Err(e) => {
            tracing::error!("WSL镜像下载失败: {}", e);

            // 更新失败状态
            state_sender.send_modify(|state| {
                state.wsl_mirror_status.download_progress = 0;
            });

            Err(CoreError::operation_failed(&format!("镜像下载失败: {}", e)))
        }
    }
}

/// 下载并导入WSL镜像
async fn download_and_import_mirror(
    install_path: &std::path::Path,
    context_config: &crate::config::ContextConfig,
    settings_service: Arc<SettingsService>,
    agent_adapter: Arc<AgentAdapter>,
    state_sender: &watch::Sender<super::super::types::SystemState>,
    trace_id: &Uuid,
) -> CoreResult<WslMirrorCheckResult> {
    tracing::info!("开始完整的WSL镜像获取和安装流程");

    // 准备缓存目录
    let cache_dir = install_path
        .parent()
        .unwrap_or_else(|| std::path::Path::new("."))
        .join("download_cache");

    tokio::fs::create_dir_all(&cache_dir)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("创建缓存目录失败: {}", e)))?;

    let mirror_path = cache_dir.join(format!("{}.tar", context_config.distro_name));

    // 第一阶段：下载镜像（如果需要）
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            check_result.status = CheckStatus::AutoFixing;
            check_result.message = "正在获取WSL镜像...".to_string();
            check_result.progress = Some(ProgressInfo {
                operation: "检查镜像文件".to_string(),
                percent: 5,
                current_step: "验证本地缓存".to_string(),
            });
        }
    });

    if !mirror_path.exists() {
        tracing::info!("镜像文件不存在，开始下载");
        download_mirror(context_config, &cache_dir, state_sender, &trace_id).await?;
    } else {
        tracing::info!("镜像文件已存在，跳过下载: {}", mirror_path.display());

        // 验证现有文件完整性
        let expected_hash = context_config.environment.mirror_hash.clone();
        let hash_algorithm = context_config.environment.mirror_algorithm;
        match verify_mirror_integrity(&mirror_path, &expected_hash, hash_algorithm).await {
            Ok(valid) => {
                if !valid {
                    tracing::warn!("镜像文件校验失败，重新下载");
                    download_mirror(context_config, &cache_dir, state_sender, &trace_id).await?;
                } else {
                    tracing::info!("镜像文件校验通过");
                }
            }
            Err(e) => {
                tracing::warn!("镜像校验失败: {}，重新下载", e);
                download_mirror(context_config, &cache_dir, state_sender, &trace_id).await?;
            }
        }

        // 更新下载状态
        state_sender.send_modify(|state| {
            state.wsl_mirror_status.is_downloaded = true;
            state.wsl_mirror_status.download_progress = 100;
        });
    }

    // 导入和启动镜像的其余逻辑...
    import_and_start_mirror(
        &mirror_path,
        install_path,
        context_config,
        agent_adapter,
        state_sender,
    )
    .await
}

/// 导入并启动WSL镜像
async fn import_and_start_mirror(
    mirror_path: &std::path::Path,
    install_path: &std::path::Path,
    context_config: &crate::config::ContextConfig,
    agent_adapter: Arc<AgentAdapter>,
    state_sender: &watch::Sender<super::super::types::SystemState>,
) -> CoreResult<WslMirrorCheckResult> {
    // 第二阶段：导入镜像
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            check_result.message = "正在安装WSL镜像...".to_string();
            check_result.progress = Some(ProgressInfo {
                operation: "导入WSL分发".to_string(),
                percent: 40,
                current_step: "准备导入".to_string(),
            });
        }
    });

    // 重要：导入前检查并删除已存在的数据卷目录
    if let Some(existing_distribution) =
        WslManager::is_distribution_imported(&context_config.distro_name).await?
    {
        tracing::warn!("分发已存在，删除旧版本: {}", existing_distribution.name);

        state_sender.send_modify(|state| {
            if let Some(check_result) = state.check_results.get_mut(5) {
                if let Some(progress) = &mut check_result.progress {
                    progress.current_step = "删除旧版本".to_string();
                    progress.percent = 45;
                }
            }
        });

        WslManager::unregister_distribution(&context_config.distro_name).await?;
    }

    // 执行导入
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            if let Some(progress) = &mut check_result.progress {
                progress.current_step = "正在导入分发".to_string();
                progress.percent = 50;
            }
        }
        state.wsl_mirror_status.install_progress = 50;
    });

    WslManager::import_distribution(&context_config.distro_name, mirror_path, install_path)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("导入镜像失败: {}", e)))?;

    // 第三阶段：设置WSL2版本
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            if let Some(progress) = &mut check_result.progress {
                progress.current_step = "设置WSL2版本".to_string();
                progress.percent = 70;
            }
        }
        state.wsl_mirror_status.install_progress = 70;
    });

    WslManager::set_wsl2_version(&context_config.distro_name)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("设置WSL2版本失败: {}", e)))?;

    // 第四阶段：启动分发
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            if let Some(progress) = &mut check_result.progress {
                progress.current_step = "启动WSL分发".to_string();
                progress.percent = 85;
            }
        }
        state.wsl_mirror_status.install_progress = 85;
    });

    WslManager::start_distribution(&context_config.distro_name)
        .await
        .map_err(|e| CoreError::operation_failed(&format!("启动分发失败: {}", e)))?;

    // 第五阶段：验证Agent健康状态
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(5) {
            if let Some(progress) = &mut check_result.progress {
                progress.current_step = "验证Agent服务".to_string();
                progress.percent = 95;
            }
        }
        state.wsl_mirror_status.install_progress = 95;
    });

    // 等待一下让WSL完全启动
    tokio::time::sleep(std::time::Duration::from_secs(3)).await;

    let final_status = match check_agent_health(agent_adapter, &context_config.distro_name).await {
        Ok(()) => {
            tracing::info!("Agent健康检查通过，安装成功");

            state_sender.send_modify(|state| {
                state.wsl_mirror_status.is_installed = true;
                state.wsl_mirror_status.install_progress = 100;
            });

            WslMirrorCheckResult {
                status: CheckStatus::Passed,
                message: "WSL镜像安装成功".to_string(),
                manual_action: None,
                progress: None,
                details: Some(serde_json::json!({
                    "distro_name": context_config.distro_name,
                    "install_path": install_path.to_string_lossy(),
                    "installation_completed": true,
                    "agent_health": "ok"
                })),
                wsl_mirror_status: WSLMirrorStatus {
                    is_installed: true,
                    is_downloaded: true,
                    download_progress: 100,
                    install_progress: 100,
                    version: Some("完整安装".to_string()),
                },
            }
        }
        Err(e) => {
            tracing::warn!("Agent健康检查失败，但镜像已安装: {}", e);

            // 标记为部分成功（镜像已安装但Agent可能需要时间启动）
            state_sender.send_modify(|state| {
                state.wsl_mirror_status.is_installed = true;
                state.wsl_mirror_status.install_progress = 100;
            });

            WslMirrorCheckResult {
                status: CheckStatus::Passed,
                message: "WSL镜像安装成功，Agent启动中...".to_string(),
                manual_action: None,
                progress: None,
                details: Some(serde_json::json!({
                    "distro_name": context_config.distro_name,
                    "install_path": install_path.to_string_lossy(),
                    "installation_completed": true,
                    "agent_health": "pending",
                    "agent_warning": e.to_string()
                })),
                wsl_mirror_status: WSLMirrorStatus {
                    is_installed: true,
                    is_downloaded: true,
                    download_progress: 100,
                    install_progress: 100,
                    version: Some("安装完成".to_string()),
                },
            }
        }
    };

    // 清理临时文件（可选，保留镜像文件供下次使用）
    tracing::info!("WSL镜像安装流程完成");
    Ok(final_status)
}

/// 验证镜像文件完整性
async fn verify_mirror_integrity(
    mirror_path: &std::path::Path,
    expected_hash: &str,
    hash_algorithm: HashAlgorithm,
) -> CoreResult<bool> {
    tracing::info!("验证镜像文件完整性: {}", mirror_path.display());

    let validator = HashValidator::new(Some(1024 * 1024)); // 1MB 缓冲区

    match validator
        .verify_hash(mirror_path, expected_hash, hash_algorithm)
        .await
    {
        Ok(valid) => {
            if valid {
                tracing::info!("镜像文件校验通过");
            } else {
                tracing::warn!("镜像文件校验失败");
            }
            Ok(valid)
        }
        Err(e) => {
            tracing::error!("镜像文件校验错误: {}", e);
            Err(CoreError::operation_failed(&format!(
                "镜像文件校验错误: {}",
                e
            )))
        }
    }
}
