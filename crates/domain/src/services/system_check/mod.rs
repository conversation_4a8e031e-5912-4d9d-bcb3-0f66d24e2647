//! 系统检查服务
//!
//! 负责执行系统环境检查，包括平台、OS、PowerShell、虚拟化、WSL、镜像等检查
//!
//! - 平台：检查是否为 Windows 平台，开发模式运行 MacOS Arm 通过
//! - OS：检查是否为 Windows 10/11，开发模式跳过 MacOS 的检查
//! - PowerShell：检查是否存在 PowerShell，开发模式跳过 MacOs 的检查
//! - 虚拟化：检查是否启用虚拟化，通过 helper-svc adapter
//! - WSL 环境：检查是否为 WSL 是否启用（WSL 可选功能）和安装，通过 helper-svc adapter
//! - WSL 镜像：检查镜像是否已安装，没有需要检测可用磁盘（容量大于 50GB），然后从 CDN 下载 WSL 镜像并导入
//!    - 检测磁盘可用容量

// 模块导入
mod checks;
mod types;
mod utils;

// 重新导出公共类型和接口
pub use types::*;

use super::settings_service::SettingsService;
use super::{CoreService, ServiceConfig, ServiceFuture, ServiceStatus};
use crate::{
    CoreError, CoreResult,
    adapters::{agent::AgentAdapter, helper::HelperAdapter},
    context::get_global_trace_id,
};
use std::sync::Arc;
use tokio::sync::watch;
use tokio_util::sync::CancellationToken;
use tracing::{info, warn};
use uuid::Uuid;

/// 检查结果缓存项
#[derive(Debug, Clone)]
struct CachedCheckResult {
    result: CheckResult,
    timestamp: chrono::DateTime<chrono::Utc>,
}

/// 系统检查服务
pub struct SystemCheckService {
    /// 配置
    config: SystemCheckConfig,
    /// Agent 适配器
    agent_adapter: Arc<AgentAdapter>,
    /// Helper 适配器
    helper_adapter: Arc<HelperAdapter>,
    /// 设置服务
    settings_service: Arc<SettingsService>,
    /// 上下文配置
    context_config: Arc<crate::config::ContextConfig>,
    /// 当前系统状态
    state: watch::Sender<SystemState>,
    /// 服务状态
    status: watch::Sender<ServiceStatus>,
    /// 终止信号
    signal: CancellationToken,
}

impl SystemCheckService {
    /// 创建新的系统检查服务
    pub fn new(
        config: SystemCheckConfig,
        agent_adapter: Arc<AgentAdapter>,
        helper_adapter: Arc<HelperAdapter>,
        settings_service: Arc<SettingsService>,
        context_config: Arc<crate::config::ContextConfig>,
    ) -> CoreResult<Self> {
        config.validate()?;

        Ok(Self {
            config,
            agent_adapter,
            helper_adapter,
            settings_service,
            context_config,
            status: watch::Sender::new(ServiceStatus::Stopped),
            state: watch::Sender::new(SystemState::default()),
            signal: CancellationToken::new(),
        })
    }

    /// 获取当前系统状态
    pub fn get_current_state(&self) -> SystemState {
        self.state.borrow().clone()
    }

    pub fn state_receiver(&self) -> watch::Receiver<SystemState> {
        self.state.subscribe()
    }

    /// 执行所有系统检查
    #[tracing::instrument(skip(self))]
    pub async fn run_all_checks(&self, trace_id: Uuid) -> () {
        info!("Starting comprehensive system check");
        if self.state.borrow().is_check_running {
            return;
        }
        self.state.send_modify(|state| {
            state.is_check_running = true;
        });
        for (i, kind) in SystemState::EXECUTION_ORDER.iter().enumerate() {
            if self.signal.is_cancelled() {
                break;
            }
            if self.state.borrow().check_results[i].status == CheckStatus::Passed {
                continue;
            }
            self.state.send_modify(|state| {
                state.check_results[i].status = CheckStatus::Checking;
            });
            let r = match kind {
                SystemCheckKind::Platform => self.check_platform().await,
                SystemCheckKind::OsVersion => self.check_os_version().await,
                SystemCheckKind::PowerShell => self.check_powershell().await,
                SystemCheckKind::Virtualization => self.check_virtualization().await,
                SystemCheckKind::WslEnvironment => self.check_wsl_environment(&trace_id).await,
                SystemCheckKind::WslMirror => self.check_wsl_mirror(&trace_id).await,
            };
            if let Err(err) = r {
                warn!("Check {} failed: {:?}", kind.name(), err);
                self.state.send_if_modified(|state| {
                    if state.check_results[i].status != CheckStatus::Checking
                        && state.check_results[i].status != CheckStatus::AutoFixing
                    {
                        return false;
                    }
                    state.check_results[i].status = CheckStatus::AutoFixFailed;
                    state.check_results[i].message = err.to_string();
                    true
                });
                break;
            }
            if self.state.borrow().check_results[i].status == CheckStatus::AutoFixFailed {
                break;
            }
        }

        self.state.send_modify(|state| {
            state.is_check_running = false;
        });

        info!("System check completed");
    }

    /// 执行单个检查
    #[tracing::instrument(skip(self))]
    async fn run_single_check(&self, kind: SystemCheckKind, trace_id: Uuid) -> CoreResult<()> {
        if self.state.borrow().is_check_running {
            return Err(CoreError::InternalError(anyhow::anyhow!(
                "System check is running"
            )));
        }
        let Some(i) = SystemState::EXECUTION_ORDER.iter().position(|k| *k == kind) else {
            tracing::error!("Check {} is not in execution order", kind.name());
            return Err(CoreError::InternalError(anyhow::anyhow!(
                "Check {} is not in execution order",
                kind.name()
            )));
        };
        self.state.send_modify(|state| {
            state.check_results[i].status = CheckStatus::Checking;
            state.check_results[i].message = "".to_string();
            state.check_results[i].details = None;
        });
        //
        let result = match kind {
            SystemCheckKind::Platform => self.check_platform().await,
            SystemCheckKind::OsVersion => self.check_os_version().await,
            SystemCheckKind::PowerShell => self.check_powershell().await,
            SystemCheckKind::Virtualization => self.check_virtualization().await,
            SystemCheckKind::WslEnvironment => self.check_wsl_environment(&trace_id).await,
            SystemCheckKind::WslMirror => self.check_wsl_mirror(&trace_id).await,
        };
        if let Err(err) = result {
            tracing::info!("Check {} failed: {:?}", kind.name(), err);
            self.state.send_modify(|state| {
                state.check_results[i].status = CheckStatus::AutoFixFailed;
                state.check_results[i].message = err.to_string();
            });
        }
        Ok(())
    }

    /// 检查平台（调用子模块）
    async fn check_platform(&self) -> CoreResult<()> {
        let result = checks::check_platform().await?;
        utils::update_check_result(
            &self.state,
            0,
            result.status,
            result.message,
            None,
            result.manual_action,
        );
        Ok(())
    }

    /// 检查操作系统版本（调用子模块）
    async fn check_os_version(&self) -> CoreResult<()> {
        let result = checks::check_os_version().await?;
        utils::update_check_result(
            &self.state,
            1,
            result.status,
            result.message,
            None,
            result.manual_action,
        );
        Ok(())
    }

    /// 检查 PowerShell（调用子模块）
    async fn check_powershell(&self) -> CoreResult<()> {
        let result = checks::check_powershell(self.config.auto_fix_enabled, &self.state).await?;

        // 更新检查结果
        utils::update_check_result(
            &self.state,
            2,
            result.status,
            result.message,
            result.progress,
            result.manual_action,
        );

        // 更新PowerShell状态
        self.state.send_modify(|state| {
            state.powershell_status = result.powershell_status;
        });

        Ok(())
    }

    /// 检查虚拟化支持（调用子模块）
    async fn check_virtualization(&self) -> CoreResult<()> {
        let result = checks::check_virtualization().await?;
        utils::update_check_result_with_details(
            &self.state,
            3,
            result.status,
            result.message,
            None,
            result.manual_action,
            result.details,
        );
        Ok(())
    }

    /// 检查 WSL 环境（调用子模块）
    async fn check_wsl_environment(&self, _trace_id: &Uuid) -> CoreResult<()> {
        let helper_adapter: Option<Arc<HelperAdapter>> = if self.config.helper_enabled {
            Some(self.helper_adapter.clone())
        } else {
            None
        };

        let result = checks::check_wsl_environment(
            helper_adapter,
            self.config.auto_fix_enabled,
            self.config.helper_fallback_enabled,
            &self.state,
            &self.context_config,
        )
        .await?;

        // 更新检查结果
        utils::update_check_result_with_details(
            &self.state,
            4,
            result.status,
            result.message,
            result.progress,
            result.manual_action,
            result.details,
        );

        // 更新WSL installer状态
        self.state.send_modify(|state| {
            state.wsl_installer_status = result.wsl_installer_status;
        });

        Ok(())
    }

    /// 检查 WSL 镜像（调用子模块）
    async fn check_wsl_mirror(&self, trace_id: &Uuid) -> CoreResult<()> {
        let result = checks::check_wsl_mirror(
            self.agent_adapter.clone(),
            self.settings_service.clone(),
            &self.context_config,
            self.config.auto_fix_enabled,
            &self.state,
            trace_id,
        )
        .await?;

        // 更新检查结果
        utils::update_check_result_with_details(
            &self.state,
            5,
            result.status,
            result.message,
            result.progress,
            result.manual_action,
            result.details,
        );

        // 更新WSL镜像状态
        self.state.send_modify(|state| {
            state.wsl_mirror_status = result.wsl_mirror_status;
        });

        Ok(())
    }

    /// 请求系统重启（通过工具模块）
    pub async fn request_system_restart(&self) -> CoreResult<()> {
        let helper_manager = utils::HelperManager::new(
            self.helper_adapter.clone(),
            self.config.helper_enabled,
            self.config.helper_fallback_enabled,
        );
        helper_manager.request_system_restart().await
    }

    // ===== 真正的异步实现方法 =====

    /// 异步启动实现
    async fn start_impl(&self) -> CoreResult<()> {
        info!("Starting system check service");

        self.status.send_modify(|status: &mut ServiceStatus| {
            *status = ServiceStatus::Running;
        });
        self.run_all_checks(get_global_trace_id()).await;
        self.status.send_modify(|status: &mut ServiceStatus| {
            *status = ServiceStatus::Stopped;
        });

        info!("System check service started successfully");
        Ok(())
    }

    /// 异步停止实现
    async fn stop_impl(&self) -> CoreResult<()> {
        self.status.send_modify(|status: &mut ServiceStatus| {
            *status = ServiceStatus::Stopping;
        });
        self.signal.cancel();
        info!("System check service stopped successfully");
        self.status.send_modify(|status: &mut ServiceStatus| {
            *status = ServiceStatus::Stopped;
        });
        Ok(())
    }
}

impl ServiceConfig for SystemCheckConfig {
    fn validate(&self) -> CoreResult<()> {
        if self.check_timeout == 0 {
            return Err(CoreError::config_error(
                "check_timeout must be greater than 0",
            ));
        }
        Ok(())
    }

    fn name(&self) -> &str {
        "system_check"
    }
}

impl CoreService for SystemCheckService {
    fn name(&self) -> &str {
        "system_check"
    }

    fn status(&self) -> ServiceStatus {
        // 同步方法，返回默认状态
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(async { Ok(true) })
    }
}
