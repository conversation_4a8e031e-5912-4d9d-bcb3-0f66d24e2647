//! 用户管理服务
//!
//! 本服务提供完整的用户认证、凭据管理和自动登录功能。
//!
//! ## 主要功能
//!
//! ### 用户认证
//! - **密码登录**: 支持手机号+密码的标准登录方式
//! - **验证码登录**: 支持手机号+验证码的快速登录方式
//! - **自动登录**: 基于保存的JWT凭据实现自动登录
//! - **安全登出**: 清除本地凭据和用户状态
//!
//! ### 用户凭据管理
//! - **凭据存储**: 自动保存用户凭据到 `%APPDATA%\echowave_client\user-credentials.json`
//! - **过期检查**: 支持30天自动过期机制，过期后自动清除
//! - **JWT解析**: 解析JWT token获取用户信息和过期时间
//! - **安全性**: 支持配置是否记住凭据和自动登录
//!
//! ### 用户信息管理
//! - **用户状态**: 维护用户登录状态和基本信息
//! - **用户信息**: 包含用户ID、手机号、用户名、邮箱、头像等
//! - **钱包余额**: 支持钱包余额查询和刷新
//! - **实时同步**: 状态变化实时同步到上层应用
//!
//! ## 技术特性
//!
//! ### Actor模型
//! - 采用Actor模型处理并发请求，避免锁竞争
//! - 支持异步操作，提高系统响应性
//! - 内置状态管理和事件通知机制
//!
//! ### HTTP集成
//! - 基于reqwest的HTTP客户端
//! - 支持API请求重试和错误处理
//! - 预留HTTP适配器钩子，支持统一的登录失效处理
//!
//! ### 配置管理
//! - 支持灵活的服务配置
//! - 可配置自动登录、凭据记住、过期时间等
//! - 运行时配置更新支持
//!
//! ## 使用示例
//!
//! ```rust
//! use crate::services::user_service::UserService;
//! use uuid::Uuid;
//!
//! // 创建用户服务
//! let user_service = UserService::new().await?;
//!
//! // 密码登录
//! let trace_id = Uuid::new_v4();
//! let user_state = user_service.login(
//!     "13800138000".to_string(),
//!     "password".to_string(),
//!     trace_id
//! ).await?;
//!
//! // 验证码登录
//! let user_state = user_service.login_with_code(
//!     "13800138000".to_string(),
//!     "123456".to_string(),
//!     trace_id
//! ).await?;
//!
//! // 自动登录
//! if let Some(user_state) = user_service.auto_login(trace_id).await? {
//!     println!("Auto login successful");
//! }
//!
//! // 登出
//! user_service.logout(trace_id).await?;
//! ```
//!
//! ## 错误处理
//!
//! 服务使用 `CoreResult<T>` 作为返回类型，支持：
//! - 网络错误处理
//! - 认证失败处理
//! - JWT解析错误处理
//! - 文件操作错误处理
//!
//! ## 注意事项
//!
//! - 用户凭据文件存储在系统数据目录，确保有相应的读写权限
//! - JWT token解析目前未验证签名，生产环境应启用签名验证
//! - 服务启动时会自动创建必要的目录结构
//! - 建议在应用启动时调用 `auto_login` 实现无感知登录体验

use base64::{Engine as _, engine::general_purpose};
use chrono::Utc;
use serde::{Deserialize, Deserializer, Serialize};
use serde_json::json;
use shared::match_actor_command;
use std::ptr;
use std::sync::atomic::{AtomicPtr, Ordering};
use std::sync::{Arc, OnceLock};
use tokio::sync::{oneshot, watch};
use tracing::{error, info, instrument};
use uuid::Uuid;

use crate::CoreResult;
use crate::adapters::file::FileAdapter;
use crate::adapters::http::{HttpAdapter, Interceptor};
use crate::context::get_global_trace_id;
use crate::services::{CoreService, ServiceFuture, ServiceStatus};
use shared::actor::{Actor, ActorHandle, ActorMessage, Identifiable, spawn_actor};
use std::future::Future;
use std::pin::Pin;

/// 类型别名：Actor 响应回调
type ActorRespondTo<T> = oneshot::Sender<CoreResult<T>>;

/// 用户状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserState {
    pub is_logged_in: bool,
    pub is_logging_in: bool,
    pub next_code_send_available_at: i64,
    pub user_info: Option<UserInfo>,
    /// 钱包余额
    pub wallet_balance: f64,
    pub last_sync_time: i64,
}

impl Default for UserState {
    fn default() -> Self {
        Self {
            is_logged_in: false,
            is_logging_in: false,
            next_code_send_available_at: 0,
            user_info: None,
            wallet_balance: 0.0,
            last_sync_time: 0,
        }
    }
}
/// 用户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: Option<i64>,
    pub username: Option<String>,
    pub phone: String,
    /// 保留，暂无邮箱
    pub email: Option<String>,
    pub avatar: Option<String>,
}

/// 用户凭据（与 legacy 代码兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserCredentials {
    pub phone: String,
    pub token: String,
    /// 保存时间戳（Unix 时间戳，毫秒）
    #[serde(rename = "savedAt")]
    pub saved_at: u64,
    /// 自动登录固定为 true（与 legacy 兼容）
    #[serde(rename = "autoLogin")]
    pub auto_login: bool,
}

impl UserCredentials {
    /// 创建新的用户凭据
    pub fn new(phone: String, token: String) -> Self {
        Self {
            phone,
            token,
            saved_at: chrono::Utc::now().timestamp() as u64,
            auto_login: true, // 默认始终启用自动登录
        }
    }

    /// 检查凭据中的 token 是否过期（从 JWT 中解析）
    pub fn is_token_expired(&self) -> bool {
        match self.parse_jwt_claims() {
            Ok(claims) => {
                let now = chrono::Utc::now().timestamp() as usize;
                claims.exp <= now
            }
            Err(err) => {
                // 如果无法解析 token，认为已过期
                error!("Failed to parse JWT claims: {}", err);
                true
            }
        }
    }

    /// 从 JWT token 中解析 Claims
    pub fn parse_jwt_claims(&self) -> Result<Claims, anyhow::Error> {
        // JWT 格式: header.payload.signature
        let parts: Vec<&str> = self.token.split('.').collect();
        if parts.len() != 3 {
            return Err(anyhow::anyhow!("Invalid JWT format"));
        }

        // 解析 payload 部分
        let payload_base64 = parts[1];
        let payload_bytes = base64::engine::general_purpose::URL_SAFE_NO_PAD
            .decode(payload_base64)
            .map_err(|e| anyhow::anyhow!("Failed to decode JWT payload: {}", e))?;

        let claims: Claims = serde_json::from_slice(&payload_bytes)
            .map_err(|e| anyhow::anyhow!("Failed to parse JWT claims: {}", e))?;

        Ok(claims)
    }
}

/// 登录请求
#[derive(Debug, Serialize)]
pub struct LoginRequest {
    pub phone: String,
    pub password: Option<String>,
    pub code: Option<String>,
    pub login_model: u8, // 0-密码登录 1-验证码登录
    pub ts: u64,
}

/// 登录响应
#[derive(Debug, Deserialize)]
pub struct LoginResponse {
    pub success: bool,
    pub data: Option<String>, // JWT token
    pub err_code: Option<u32>,
    pub err_message: Option<String>,
}

/// 发送验证码请求
#[derive(Debug, Serialize)]
pub struct SendVerificationCodeRequest {
    pub phone: String,
    pub ts: u64,
}

/// 发送验证码响应
#[derive(Debug, Deserialize)]
pub struct SendVerificationCodeResponse {
    pub success: bool,
    pub err_code: Option<u32>,
    pub err_message: Option<String>,
}

/// 钱包余额响应
#[derive(Debug, Deserialize)]
pub struct WalletResponse {
    pub success: bool,
    pub data: Option<WalletData>,
    pub err_code: Option<u32>,
    pub err_message: Option<String>,
}

/// 钱包数据
#[derive(Debug, Deserialize)]
pub struct WalletData {
    pub balance: f64,
    pub currency: Option<String>,
    #[serde(rename = "update_time")]
    pub last_updated: Option<String>,
}

/// 用户信息响应
#[derive(Debug, Deserialize)]
pub struct UserInfoResponse {
    pub success: bool,
    pub data: Option<UserInfoData>,
    pub err_code: Option<u32>,
    pub err_message: Option<String>,
}

/// 自定义反序列化函数：将字符串形式的数字转换为 i64
fn deserialize_string_to_i64<'de, D>(deserializer: D) -> Result<i64, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::Error;

    #[derive(Deserialize)]
    #[serde(untagged)]
    enum StringOrInt {
        String(String),
        Int(i64),
    }

    match StringOrInt::deserialize(deserializer)? {
        StringOrInt::Int(i) => Ok(i),
        StringOrInt::String(s) => s
            .parse::<i64>()
            .map_err(|_| Error::custom(format!("无法将字符串 '{}' 转换为数字", s))),
    }
}

/// 用户信息数据
#[derive(Debug, Deserialize)]
pub struct UserInfoData {
    #[serde(rename = "id", deserialize_with = "deserialize_string_to_i64")]
    pub user_id: i64,
    #[serde(rename = "name")]
    pub username: Option<String>,
    pub phone: String,
    pub email: Option<String>,
    pub avatar: Option<String>,
    #[serde(rename = "created_time")]
    pub created_at: Option<String>,
    #[serde(rename = "update_time")]
    pub updated_at: Option<String>,
}
fn default_expire_time() -> usize {
    chrono::Utc::now().timestamp() as usize + 3600 * 24 * 30 // 30 days
}

/// JWT Claims
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    #[serde(rename = "phone")]
    pub sub: String,
    #[serde(default = "default_expire_time")]
    pub exp: usize,
    #[serde(rename = "created")]
    pub iat: usize,
    // pub nbf: usize,
    pub jti: usize,
}

/// 用户服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserServiceConfig {
    pub remember_credentials: bool,
}

impl Default for UserServiceConfig {
    fn default() -> Self {
        Self {
            remember_credentials: true,
        }
    }
}

/// 用户服务命令
#[derive(Debug)]
enum UserCommand {
    Login {
        username: String,
        password: String,
        trace_id: Uuid,
        respond_to: ActorRespondTo<UserState>,
    },
    LoginWithCode {
        phone: String,
        code: String,
        trace_id: Uuid,
        respond_to: ActorRespondTo<UserState>,
    },
    SendVerificationCode {
        phone: String,
        trace_id: Uuid,
        respond_to: ActorRespondTo<()>,
    },
    AutoLogin {
        trace_id: Uuid,
        respond_to: ActorRespondTo<Option<UserState>>,
    },
    Logout {
        trace_id: Uuid,
        respond_to: ActorRespondTo<()>,
    },
    RefreshWalletBalance {
        trace_id: Uuid,
        respond_to: ActorRespondTo<f64>,
    },
    UpdateConfig {
        config: UserServiceConfig,
        respond_to: ActorRespondTo<()>,
    },
    HandleLoginExpired {
        error_message: String,
    },
}

impl Identifiable for UserCommand {
    fn name(&self) -> &'static str {
        match self {
            UserCommand::Login { .. } => "Login",
            UserCommand::LoginWithCode { .. } => "LoginWithCode",
            UserCommand::SendVerificationCode { .. } => "SendVerificationCode",
            UserCommand::AutoLogin { .. } => "AutoLogin",
            UserCommand::Logout { .. } => "Logout",
            UserCommand::RefreshWalletBalance { .. } => "RefreshWalletBalance",
            UserCommand::UpdateConfig { .. } => "UpdateConfig",
            UserCommand::HandleLoginExpired { .. } => "HandleLoginExpired",
        }
    }
}

impl From<UserCommand> for ActorMessage<UserCommand, SystemEvent> {
    fn from(value: UserCommand) -> Self {
        ActorMessage::Cmd(value)
    }
}

/// 内部事件（暂时为空，保留扩展性）
#[derive(Debug)]
enum SystemEvent {}

impl Identifiable for SystemEvent {
    fn name(&self) -> &'static str {
        match *self {}
    }
}

impl From<SystemEvent> for ActorMessage<UserCommand, SystemEvent> {
    fn from(value: SystemEvent) -> Self {
        ActorMessage::Evt(value)
    }
}

/// 用户服务 Actor
struct UserServiceActor {
    config: UserServiceConfig,
    current_user: watch::Sender<UserState>,
    http_adapter: Arc<HttpAdapter>,
    file_adapter: Arc<FileAdapter>,
    token_ptr: Arc<AtomicPtr<String>>,
    /// Actor 自己的 handle，用于发送内部事件
    self_handle: OnceLock<ActorHandle<ActorMessage<UserCommand, SystemEvent>>>,
}

impl Actor<ActorMessage<UserCommand, SystemEvent>> for UserServiceActor {
    fn name(&self) -> &'static str {
        "UserService"
    }
    fn on_start(&mut self, handle: ActorHandle<ActorMessage<UserCommand, SystemEvent>>) {
        if let Err(_err) = self.self_handle.set(handle) {
            error!("Failed to set self_handle for {}", self.name());
        };
    }
    fn handle(
        &mut self,
        msg: ActorMessage<UserCommand, SystemEvent>,
    ) -> Pin<Box<dyn Future<Output = ()> + Send + '_>> {
        Box::pin(async move {
            match msg {
                ActorMessage::Cmd(cmd) => Self::handle_command(self, cmd).await,
                ActorMessage::Evt(evt) => Self::handle_event(self, evt).await,
            }
        })
    }
}

impl UserServiceActor {
    fn new(
        config: UserServiceConfig,
        http_adapter: Arc<HttpAdapter>,
        file_adapter: Arc<FileAdapter>,
        token_ptr: Arc<AtomicPtr<String>>,
    ) -> Self {
        Self {
            config,
            current_user: watch::Sender::new(UserState::default()), // 直接初始化为默认状态
            http_adapter,
            file_adapter,
            token_ptr,
            self_handle: OnceLock::new(),
        }
    }

    /// 获取用户凭据文件路径（用于测试）
    #[cfg(test)]
    fn get_credentials_file_path() -> CoreResult<std::path::PathBuf> {
        let data_dir = dirs::data_dir()
            .ok_or_else(|| crate::CoreError::service_error("Cannot find data directory"))?;
        let app_dir = data_dir.join("echowave_client");
        Ok(app_dir.join("user-credentials.json"))
    }

    /// 保存用户凭据
    async fn save_credentials(&self, credentials: &UserCredentials) -> CoreResult<()> {
        self.file_adapter
            .write_json_by_name("user-credentials.json", credentials)
            .await
            .map_err(|e| {
                crate::CoreError::service_error(&format!("Failed to save credentials: {}", e))
            })?;

        info!("User credentials saved successfully");
        Ok(())
    }

    /// 加载用户凭据
    async fn load_credentials(&self) -> CoreResult<Option<UserCredentials>> {
        if !self
            .file_adapter
            .exists_by_name("user-credentials.json")
            .await
        {
            info!("No user credentials file found");
            return Ok(None);
        }
        match self
            .file_adapter
            .read_json_by_name::<UserCredentials>("user-credentials.json")
            .await
        {
            Ok(credentials) => {
                // 检查凭据是否过期（使用 JWT 中的过期时间）
                if credentials.is_token_expired() {
                    info!("User credentials expired, removing file");
                    self.clear_credentials().await?;
                    return Ok(None);
                }
                Ok(Some(credentials))
            }
            Err(e) => {
                // 如果文件不存在或读取失败，返回 None
                info!(
                    "Failed to load credentials file ({}), assuming not logged in",
                    e
                );
                Ok(None)
            }
        }
    }

    /// 清除用户凭据
    async fn clear_credentials(&self) -> CoreResult<()> {
        match self
            .file_adapter
            .remove_file_by_name("user-credentials.json")
            .await
        {
            Ok(()) => {
                info!("User credentials cleared");
                Ok(())
            }
            Err(e) => {
                // 如果文件不存在，也认为是成功的
                info!(
                    "Failed to remove credentials file ({}), assuming already cleared",
                    e
                );
                Ok(())
            }
        }
    }

    /// 解析JWT token（不验证签名，仅获取 payload）
    fn parse_jwt_token(&self, token: &str) -> CoreResult<Claims> {
        // JWT 格式: header.payload.signature
        // 我们只需要 payload 部分来获取失效时间
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() != 3 {
            return Err(crate::CoreError::service_error("Invalid JWT format"));
        }

        let payload_part = parts[1];

        // Base64 解码 payload（JWT 使用 base64url 编码）
        let payload_bytes = general_purpose::URL_SAFE_NO_PAD
            .decode(payload_part)
            .map_err(|e| {
                crate::CoreError::service_error(&format!("Failed to decode JWT payload: {}", e))
            })?;

        // 解析 JSON
        let claims: Claims = serde_json::from_slice(&payload_bytes).map_err(|e| {
            crate::CoreError::service_error(&format!("Failed to parse JWT claims: {}", e))
        })?;

        Ok(claims)
    }

    /// 检查JWT token是否过期
    fn is_token_expired(&self, claims: &Claims) -> bool {
        let now = Utc::now().timestamp() as usize;
        claims.exp <= now
    }

    #[instrument(skip(self, password))]
    async fn login(
        &mut self,
        username: String,
        password: String,
        trace_id: Uuid,
    ) -> CoreResult<UserState> {
        info!("Attempting login for user: {}", username);

        // 使用通用登录方法
        self.perform_login(Some(password), None, username.clone(), trace_id)
            .await?;

        info!("User login successful for: {}", username);
        Ok(self.current_user.borrow().clone())
    }

    /// 通用登录方法（不区分密码还是验证码）
    async fn perform_login(
        &mut self,
        password: Option<String>,
        code: Option<String>,
        phone: String,
        trace_id: Uuid,
    ) -> CoreResult<()> {
        self.current_user.send_modify(|state| {
            state.is_logging_in = true;
            state.last_sync_time = Utc::now().timestamp_millis();
        });
        self.update_token_atomic(None);

        // 创建登录请求
        let login_model = if password.is_some() { 0 } else { 1 }; // 0-密码登录, 1-验证码登录
        let login_request = LoginRequest {
            phone: phone.clone(),
            password,
            code,
            login_model,
            ts: Utc::now().timestamp() as u64,
        };

        // 发送登录请求
        let login_response: LoginResponse = self
            .http_adapter
            .post("/api/user/login", &login_request)
            .await
            .map_err(|e| {
                tracing::error!("Login request failed: {}", e);
                self.current_user.send_modify(|state| {
                    state.is_logging_in = false;
                    state.last_sync_time = Utc::now().timestamp_millis();
                });
                crate::CoreError::service_error(&e.to_string())
            })?;

        if !login_response.success {
            let error_msg = login_response
                .err_message
                .unwrap_or_else(|| "Login failed".to_string());
            tracing::error!("Login failed: {}", error_msg);
            return Err(crate::CoreError::service_error(&error_msg));
        }

        let token = login_response.data.ok_or_else(|| {
            tracing::error!("No token received from login response");
            crate::CoreError::service_error("No token received from login response")
        })?;

        tracing::debug!("Login success for phone: {}", phone);
        // 调用登录成功后的处理逻辑
        self.handle_login_success(token, phone, trace_id)
            .await
            .map_err(|err| {
                tracing::error!("Failed to handle login success: {}", err);
                self.current_user.send_modify(|state| {
                    state.is_logging_in = false;
                });
                err
            })?;

        Ok(())
    }

    /// 登录成功后的公共处理逻辑（处理token后的所有操作）
    #[instrument(skip(self, token))]
    async fn handle_login_success(
        &mut self,
        token: String,
        phone: String,
        trace_id: Uuid,
    ) -> CoreResult<()> {
        // 存储token到原子存储
        tracing::trace!("Storing token to atomic storage...");
        self.update_token_atomic(Some(token.clone()));

        // 从 API 获取完整的用户信息
        tracing::trace!("Fetching user info...");
        let user_info = self.fetch_user_info().await?;

        // 从 API 获取钱包余额
        tracing::trace!("Fetching wallet balance...");
        let wallet_balance = self.fetch_wallet_balance(trace_id).await?;

        // 更新用户状态
        self.current_user.send_modify(|state| {
            state.is_logged_in = true;
            state.is_logging_in = false;
            state.user_info = Some(user_info);
            state.wallet_balance = wallet_balance;
            state.last_sync_time = Utc::now().timestamp_millis();
        });

        // 如果配置允许，保存用户凭据
        tracing::trace!("Saving credentials...");
        if self.config.remember_credentials {
            let credentials = UserCredentials::new(phone, token);
            if let Err(e) = self.save_credentials(&credentials).await {
                error!("Failed to save credentials: {}", e);
            }
        }

        Ok(())
    }

    #[instrument(skip(self))]
    async fn logout(&mut self, _trace_id: Uuid) -> CoreResult<()> {
        // 重置为默认的未登录状态
        self.current_user.send_replace(UserState::default());
        self.update_token_atomic(None);

        // 清除用户凭据
        if let Err(e) = self.clear_credentials().await {
            error!("Failed to clear credentials during logout: {}", e);
        }

        info!("User logout successful");
        Ok(())
    }

    /// 尝试自动登录（简化的串行版本）
    #[instrument(skip(self))]
    async fn auto_login(&mut self, trace_id: Uuid) -> CoreResult<Option<UserState>> {
        info!("Attempting auto-login");

        self.current_user.send_modify(|state| {
            state.is_logging_in = true;
            state.last_sync_time = Utc::now().timestamp_millis();
        });

        // 加载保存的凭据
        let credentials = match self.load_credentials().await? {
            Some(creds) => creds,
            None => {
                info!("No saved credentials found for auto-login");
                self.current_user.send_modify(|state| {
                    state.is_logging_in = false;
                });
                return Ok(None);
            }
        };

        // 检查是否启用了自动登录
        if !credentials.auto_login {
            info!("Auto-login disabled in credentials");
            self.current_user.send_modify(|state| {
                state.is_logging_in = false;
            });
            return Ok(None);
        }

        // 解析JWT token
        let claims = match self.parse_jwt_token(&credentials.token) {
            Ok(claims) => claims,
            Err(e) => {
                error!("Failed to parse saved JWT token: {}", e);
                self.clear_credentials().await?;
                self.current_user.send_modify(|state| {
                    state.is_logging_in = false;
                });
                return Ok(None);
            }
        };

        // 检查token是否过期
        if self.is_token_expired(&claims) {
            info!("Saved token is expired, clearing credentials");
            self.clear_credentials().await?;
            self.current_user.send_modify(|state| {
                state.is_logging_in = false;
            });
            return Ok(None);
        }

        // 使用公共的登录成功处理逻辑
        if let Err(e) = self
            .handle_login_success(credentials.token, claims.sub.clone(), trace_id)
            .await
        {
            error!("Failed to complete auto-login: {}", e);
            self.current_user.send_modify(|state| {
                state.is_logging_in = false;
            });
            return Ok(None);
        }

        info!("Auto-login successful for: {}", claims.sub);
        Ok(Some(self.current_user.borrow().clone()))
    }

    /// 验证码登录
    #[instrument(skip(self))]
    async fn login_with_code(
        &mut self,
        phone: String,
        code: String,
        trace_id: Uuid,
    ) -> CoreResult<UserState> {
        info!("Attempting login with code for user: {}", phone);

        // 使用通用登录方法
        self.perform_login(None, Some(code), phone.clone(), trace_id)
            .await?;

        info!("User login with code successful for: {}", phone);
        Ok(self.current_user.borrow().clone())
    }

    /// 发送验证码
    #[instrument(skip(self))]
    async fn send_verification_code(&mut self, phone: String, trace_id: Uuid) -> CoreResult<()> {
        info!("Attempting to send verification code to: {}", phone);

        // 检查60秒间隔限制
        let next_code_send_available_at = self.current_user.borrow().next_code_send_available_at;
        let now = Utc::now().timestamp();
        if next_code_send_available_at > now {
            return Err(crate::CoreError::service_error(&format!(
                "验证码发送间隔限制，请等待 {} 秒后重试",
                next_code_send_available_at - now
            )));
        }

        // 创建发送验证码请求
        let send_code_request = SendVerificationCodeRequest {
            phone: phone.clone(),
            ts: now as u64,
        };

        // 发送验证码请求到 /api/user/valid 端点
        let send_code_response: SendVerificationCodeResponse = self
            .http_adapter
            .post("/api/user/valid", &send_code_request)
            .await
            .map_err(|e| {
                crate::CoreError::service_error(&format!(
                    "Send verification code request failed: {}",
                    e
                ))
            })?;

        if !send_code_response.success {
            let error_msg = send_code_response
                .err_message
                .unwrap_or_else(|| "发送验证码失败".to_string());
            return Err(crate::CoreError::service_error(&error_msg));
        }

        // 记录发送时间
        self.current_user.send_modify(|state| {
            state.next_code_send_available_at = now + 60;
        });

        info!("Verification code sent successfully to: {}", phone);
        Ok(())
    }

    #[instrument(skip(self), fields(trace_id = %trace_id))]
    async fn fetch_wallet_balance(&self, trace_id: Uuid) -> anyhow::Result<f64> {
        let wallet_response: WalletResponse = self
            .http_adapter
            .post(
                "/api/wallet/get",
                &json!({
                    "type": "SUPPLIER"
                }),
            )
            .await
            .map_err(|e| {
                crate::CoreError::service_error(&format!("Failed to get wallet balance: {}", e))
            })?;

        if !wallet_response.success {
            let error_msg = wallet_response
                .err_message
                .unwrap_or_else(|| "Failed to get wallet balance".to_string());
            tracing::error!("Failed to get wallet balance: {}", error_msg);
            anyhow::bail!(error_msg);
        }

        let new_balance = wallet_response.data.map(|data| data.balance).unwrap_or(0.0);

        Ok(new_balance)
    }

    #[instrument(skip(self))]
    async fn refresh_wallet_balance(&mut self, trace_id: Uuid) -> CoreResult<f64> {
        if !self.is_logged_in() {
            return Err(crate::CoreError::service_error("User not logged in"));
        }
        // 调用真实的钱包余额API
        let new_balance = self.fetch_wallet_balance(trace_id).await?;

        self.current_user.send_modify(|state| {
            state.wallet_balance = new_balance;
            state.last_sync_time = chrono::Utc::now().timestamp_millis();
        });
        info!("Wallet balance refreshed: {}", new_balance);
        Ok(new_balance)
    }

    fn get_current_user(&self) -> UserState {
        self.current_user.borrow().clone()
    }

    fn is_logged_in(&self) -> bool {
        self.current_user.borrow().is_logged_in
    }

    /// 获取用户信息
    async fn fetch_user_info(&mut self) -> CoreResult<UserInfo> {
        // 调用用户信息API
        let user_info_response: UserInfoResponse =
            self.http_adapter.get("/api/user/get").await.map_err(|e| {
                tracing::error!("Failed to get user info: {}", e);
                crate::CoreError::service_error(&format!("Failed to get user info: {}", e))
            })?;

        if !user_info_response.success {
            let error_msg = user_info_response
                .err_message
                .unwrap_or_else(|| "Failed to get user info".to_string());
            return Err(crate::CoreError::service_error(&error_msg));
        }

        let user_data = user_info_response.data.ok_or_else(|| {
            tracing::error!("No user data received");
            crate::CoreError::service_error("No user data received")
        })?;

        Ok(UserInfo {
            user_id: Some(user_data.user_id),
            username: user_data.username,
            phone: user_data.phone,
            email: user_data.email,
            avatar: user_data.avatar,
        })
    }

    fn update_config(&mut self, config: UserServiceConfig) -> CoreResult<()> {
        self.config = config;
        Ok(())
    }

    /// 处理登录失效
    async fn handle_login_expired(&mut self, error_message: &str) {
        tracing::warn!("Login expired: {}", error_message);

        // 重置为默认的未登录状态
        self.current_user.send_replace(UserState::default());

        // 清除用户凭据
        if let Err(e) = self.clear_credentials().await {
            tracing::error!(
                "Failed to clear credentials during login expired handling: {}",
                e
            );
        }

        tracing::info!("User logged out due to authentication expiration");
    }

    // 原子地更新 token
    fn update_token_atomic(&self, token: Option<String>) {
        let new_ptr = match token {
            Some(token) => Box::into_raw(Box::new(token)),
            None => ptr::null_mut(),
        };

        // 原子性替换指针
        let old_ptr = self.token_ptr.swap(new_ptr, Ordering::AcqRel);

        // 释放旧的内存
        if !old_ptr.is_null() {
            unsafe { drop(Box::from_raw(old_ptr)) };
        }
    }

    /// 处理公共命令的静态方法
    fn handle_command(
        actor: &mut Self,
        cmd: UserCommand,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send + '_>> {
        match_actor_command!(cmd, actor, {
            UserCommand::Login { username, password, trace_id, respond_to } => async move {
                let result = actor.login(username, password, trace_id).await;
                let _ = respond_to.send(result);
            },
            UserCommand::LoginWithCode { phone, code, trace_id, respond_to } => async move {
                let result = actor.login_with_code(phone, code, trace_id).await;
                let _ = respond_to.send(result);
            },
            UserCommand::AutoLogin { trace_id, respond_to } => async move {
                let result = actor.auto_login(trace_id).await;
                let _ = respond_to.send(result);
            },
            UserCommand::SendVerificationCode { phone, trace_id, respond_to } => async move {
                let result = actor.send_verification_code(phone, trace_id).await;
                let _ = respond_to.send(result);
            },
            UserCommand::Logout { trace_id, respond_to } => async move {
                let result = actor.logout(trace_id).await;
                let _ = respond_to.send(result);
            },
            UserCommand::RefreshWalletBalance { trace_id, respond_to } => async move {
                let result = actor.refresh_wallet_balance(trace_id).await;
                let _ = respond_to.send(result);
            },
            UserCommand::UpdateConfig { config, respond_to } => async move {
                let result = actor.update_config(config);
                let _ = respond_to.send(result);
            },
            UserCommand::HandleLoginExpired { error_message } => async move {
                actor.handle_login_expired(&error_message).await;
            },
        })
    }

    /// 处理内部事件的静态方法（暂时为空）
    fn handle_event(
        _actor: &mut Self,
        evt: SystemEvent,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send + '_>> {
        Box::pin(async move { match evt {} })
    }
}

/// 用户服务公开接口
pub struct UserService {
    handle: ActorHandle<ActorMessage<UserCommand, SystemEvent>>,
    state: watch::Receiver<UserState>,
    status: watch::Sender<ServiceStatus>,
    /// 使用 AtomicPtr 存储当前 token，避免锁竞争
    token_ptr: Arc<AtomicPtr<String>>,
}

impl UserService {
    /// 创建新的用户服务实例
    pub fn new(http_adapter: Arc<HttpAdapter>, file_adapter: Arc<FileAdapter>) -> CoreResult<Self> {
        Self::with_config(UserServiceConfig::default(), http_adapter, file_adapter)
    }

    /// 使用指定配置创建新的用户服务实例
    pub fn with_config(
        config: UserServiceConfig,
        http_adapter: Arc<HttpAdapter>,
        file_adapter: Arc<FileAdapter>,
    ) -> CoreResult<Self> {
        let token_ptr = Arc::new(AtomicPtr::new(ptr::null_mut()));

        let actor = UserServiceActor::new(config, http_adapter, file_adapter, token_ptr.clone());

        Ok(Self {
            state: actor.current_user.subscribe(),
            status: watch::Sender::new(ServiceStatus::Stopped),
            token_ptr,
            handle: spawn_actor(actor, 100),
        })
    }

    pub fn state_receiver(&self) -> watch::Receiver<UserState> {
        self.state.clone()
    }

    /// 用户登录
    #[instrument(skip(self, password))]
    pub async fn login(
        &self,
        username: String,
        password: String,
        trace_id: Uuid,
    ) -> CoreResult<UserState> {
        let result = self
            .handle
            .call(|respond_to| UserCommand::Login {
                username,
                password,
                trace_id,
                respond_to,
            })
            .await??;

        Ok(result)
    }

    /// 验证码登录
    #[instrument(skip(self))]
    pub async fn login_with_code(
        &self,
        phone: String,
        code: String,
        trace_id: Uuid,
    ) -> CoreResult<UserState> {
        let result = self
            .handle
            .call(|respond_to| UserCommand::LoginWithCode {
                phone,
                code,
                trace_id,
                respond_to,
            })
            .await??;

        Ok(result)
    }

    /// 发送验证码
    #[instrument(skip(self))]
    pub async fn send_verification_code(&self, phone: String, trace_id: Uuid) -> CoreResult<()> {
        let result = self
            .handle
            .call(|respond_to| UserCommand::SendVerificationCode {
                phone,
                trace_id,
                respond_to,
            })
            .await??;

        Ok(result)
    }

    /// 自动登录
    #[instrument(skip(self))]
    pub async fn auto_login(&self, trace_id: Uuid) -> CoreResult<Option<UserState>> {
        tracing::info!("Auto login start");
        let result = self
            .handle
            .call(|respond_to| UserCommand::AutoLogin {
                trace_id,
                respond_to,
            })
            .await??;

        Ok(result)
    }

    /// 用户登出
    #[instrument(skip(self))]
    pub async fn logout(&self, trace_id: Uuid) -> CoreResult<()> {
        self.handle
            .call(|respond_to| UserCommand::Logout {
                trace_id,
                respond_to,
            })
            .await??;

        Ok(())
    }

    /// 刷新钱包余额
    #[instrument(skip(self))]
    pub async fn refresh_wallet_balance(&self, trace_id: Uuid) -> CoreResult<f64> {
        self.handle
            .call(|respond_to| UserCommand::RefreshWalletBalance {
                trace_id,
                respond_to,
            })
            .await?
    }

    /// 获取当前用户状态
    pub fn get_current_user(&self) -> UserState {
        self.state.borrow().clone()
    }

    /// 获取当前用户ID
    pub fn get_user_id(&self) -> Option<i64> {
        self.state
            .borrow()
            .user_info
            .as_ref()
            .and_then(|user_info| user_info.user_id)
    }

    /// 检查用户是否已登录
    pub fn is_logged_in(&self) -> bool {
        self.state.borrow().is_logged_in
    }

    /// 更新用户配置
    pub async fn update_config(&self, config: UserServiceConfig) -> CoreResult<()> {
        self.handle
            .call(|respond_to| UserCommand::UpdateConfig { config, respond_to })
            .await?
    }

    /// 获取当前用户的有效token（无锁实现）
    pub fn get_current_token(&self) -> Option<String> {
        let ptr = self.token_ptr.load(Ordering::Acquire);
        if ptr.is_null() {
            None
        } else {
            unsafe { Some((*ptr).clone()) }
        }
    }

    // 原子地更新 token
    // fn update_token_atomic(&self, token: Option<String>) {
    //     let new_ptr = match token {
    //         Some(token) => Box::into_raw(Box::new(token)),
    //         None => ptr::null_mut(),
    //     };

    //     // 原子性替换指针
    //     let old_ptr = self.token_ptr.swap(new_ptr, Ordering::AcqRel);

    //     // 释放旧的内存
    //     if !old_ptr.is_null() {
    //         unsafe { drop(Box::from_raw(old_ptr)) };
    //     }
    // }

    // 从 Actor 同步 token 到原子存储
    // 注意：此方法只应在登录成功后调用，避免死锁
    // async fn sync_token_to_atomic(&self) {
    //     if let Ok(token) = self
    //         .handle
    //         .call(|respond_to| UserCommand::GetCurrentToken { respond_to })
    //         .await
    //     {
    //         self.update_token_atomic(token);
    //     } else {
    //         tracing::warn!("Failed to sync token to atomic storage");
    //     }
    // }
}

impl CoreService for UserService {
    fn name(&self) -> &str {
        "user_service"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        self.status.send_replace(ServiceStatus::Running);
        Box::pin(async move {
            // 进行自动登录
            if let Err(err) = self.auto_login(get_global_trace_id()).await {
                tracing::info!("Failed to auto login: {}", err);
            };
            tracing::info!("User service started");
            Ok(())
        })
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        self.status.send_replace(ServiceStatus::Stopped);
        Box::pin(async {
            tracing::info!("User service stopped");
            Ok(())
        })
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        let status = self.status();
        Box::pin(async move { Ok(matches!(status, ServiceStatus::Running)) })
    }
}

impl Interceptor for UserService {
    fn get_token(&self) -> Pin<Box<dyn Future<Output = Option<String>> + Send + '_>> {
        // 直接使用原子存储，无需 async
        let token = self.get_current_token();
        Box::pin(async move { token })
    }

    fn handle_login_expired(
        &self,
        error_message: &str,
    ) -> Pin<Box<dyn Future<Output = ()> + Send + '_>> {
        let error_message = error_message.to_string();

        // 异步发送命令到 UserService 处理其他逻辑
        Box::pin(async move {
            if let Err(e) = self
                .handle
                .send(UserCommand::HandleLoginExpired { error_message })
                .await
            {
                tracing::error!("Failed to send login expired to UserService: {}", e);
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::adapters::file::FileConfig;
    use crate::adapters::http::HttpConfig;
    use tracing_subscriber;

    #[test]
    fn test_user_info_data_string_to_int_conversion() {
        // 测试字符串形式的用户ID能够正确转换为i64
        let json_with_string_id = r#"{
            "id": "123",
            "name": "测试用户",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "avatar": "avatar.jpg",
            "created_time": "2024-01-01 00:00:00",
            "update_time": "2024-01-01 00:00:00"
        }"#;

        let user_data: UserInfoData = serde_json::from_str(json_with_string_id).unwrap();
        assert_eq!(user_data.user_id, 123);

        // 测试数字形式的用户ID也能正确处理
        let json_with_int_id = r#"{
            "id": 456,
            "name": "测试用户2",
            "phone": "13800138001",
            "email": "<EMAIL>",
            "avatar": "avatar2.jpg",
            "created_time": "2024-01-01 00:00:00",
            "update_time": "2024-01-01 00:00:00"
        }"#;

        let user_data2: UserInfoData = serde_json::from_str(json_with_int_id).unwrap();
        assert_eq!(user_data2.user_id, 456);

        // 测试无效的字符串应该失败
        let json_with_invalid_id = r#"{
            "id": "invalid",
            "name": "测试用户3",
            "phone": "13800138002",
            "email": "<EMAIL>",
            "avatar": "avatar3.jpg",
            "created_time": "2024-01-01 00:00:00",
            "update_time": "2024-01-01 00:00:00"
        }"#;

        let result = serde_json::from_str::<UserInfoData>(json_with_invalid_id);
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_user_service_creation() {
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(HttpConfig::default()).unwrap());
        let service = UserService::new(http_adapter, file_adapter).unwrap();
        assert_eq!(service.name(), "user_service");

        // 启动服务
        service.start().await.unwrap();
        // 等待 actor 启动
        tokio::time::sleep(std::time::Duration::from_millis(50)).await;
        assert_eq!(service.status(), ServiceStatus::Running);
        assert!(!service.is_logged_in());
        assert!(!service.get_current_user().is_logged_in);
    }

    #[tokio::test]
    async fn test_user_login_logout() {
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(HttpConfig::default()).unwrap());
        let service = UserService::new(http_adapter, file_adapter).unwrap();
        let trace_id = Uuid::new_v4();

        // 测试登录
        let user_state = service
            .login("testuser".to_string(), "password".to_string(), trace_id)
            .await
            .unwrap();
        assert!(user_state.is_logged_in);
        assert!(service.is_logged_in());

        let current_user = service.get_current_user();
        let user_info = current_user.user_info.unwrap();
        assert_eq!(user_info.phone, "testuser");

        // 测试登出
        service.logout(trace_id).await.unwrap();
        assert!(!service.is_logged_in());
        assert!(!service.get_current_user().is_logged_in);
    }

    #[tokio::test]
    async fn test_wallet_balance_refresh() {
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(HttpConfig::default()).unwrap());
        let service = UserService::new(http_adapter, file_adapter).unwrap();
        let trace_id = Uuid::new_v4();

        // 未登录时刷新余额应该失败
        assert!(service.refresh_wallet_balance(trace_id).await.is_err());

        // 登录后刷新余额
        service
            .login("testuser".to_string(), "password".to_string(), trace_id)
            .await
            .unwrap();
        let balance = service.refresh_wallet_balance(trace_id).await.unwrap();
        assert_eq!(balance, 12345.0);
    }

    #[tokio::test]
    async fn test_config_update() {
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(HttpConfig::default()).unwrap());
        let service = UserService::new(http_adapter, file_adapter).unwrap();
        let new_config = UserServiceConfig {
            remember_credentials: false,
        };

        service.update_config(new_config).await.unwrap();
        // 配置更新应该成功，实际测试需要根据具体业务逻辑
    }

    #[tokio::test]
    async fn test_service_with_custom_config() {
        let custom_config = UserServiceConfig {
            remember_credentials: false,
        };
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(HttpConfig::default()).unwrap());
        let service = UserService::with_config(custom_config, http_adapter, file_adapter).unwrap();
        assert_eq!(service.name(), "user_service");
    }
    #[test]
    fn test_user_credentials_creation() {
        let credentials = UserCredentials::new(
            "13800138000".to_string(),
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1c2VyMTIzIiwicGhvbmUiOiIxMzgwMDEzODAwMCIsImV4cCI6MTc0MDg2NzQwMCwiaWF0IjoxNzM5NTcxNDAwLCJuYmYiOjE3Mzk1NzE0MDB9.xxx".to_string(),
        );

        assert_eq!(credentials.phone, "13800138000");
        assert!(credentials.auto_login); // 默认为 true
        assert!(credentials.saved_at > 0); // 应该有时间戳
    }

    #[test]
    fn test_user_credentials_serialization() {
        let credentials = UserCredentials::new("13800138000".to_string(), "test_token".to_string());

        let json = serde_json::to_string_pretty(&credentials).unwrap();

        // 验证 JSON 包含正确的字段名（与 legacy 兼容）
        assert!(json.contains("\"savedAt\""));
        assert!(json.contains("\"autoLogin\""));
        assert!(json.contains("\"phone\""));
        assert!(json.contains("\"token\""));

        // 验证 autoLogin 默认为 true
        assert!(json.contains("\"autoLogin\": true"));

        println!("用户凭据 JSON: {}", json);
    }

    #[test]
    fn test_user_credentials_deserialization() {
        let json = r#"{
            "phone": "13800138000",
            "token": "test_token",
            "savedAt": 1739571400000,
            "autoLogin": true
        }"#;

        let credentials: UserCredentials = serde_json::from_str(json).unwrap();
        assert_eq!(credentials.phone, "13800138000");
        assert_eq!(credentials.token, "test_token");
        assert_eq!(credentials.saved_at, 1739571400000u64);
        assert!(credentials.auto_login);
    }

    #[test]
    fn test_user_service_config() {
        let config = UserServiceConfig::default();
        assert!(config.remember_credentials); // 默认记住凭据

        let custom_config = UserServiceConfig {
            remember_credentials: false,
        };
        assert!(!custom_config.remember_credentials);
    }

    #[test]
    fn test_app_data_path() {
        use crate::adapters::file::FileConfig;

        let path = FileConfig::get_app_data_path();
        let path_str = path.to_string_lossy();

        // 验证路径包含 echowave_client
        assert!(path_str.contains("echowave_client"));

        println!("应用数据目录: {:?}", path);

        // 测试文件路径
        let files = vec!["user-credentials.json", "user-settings.json"];

        for file in files {
            let full_path = path.join(file);
            println!("文件路径 {}: {:?}", file, full_path);

            // 验证路径构建正确
            assert!(full_path.to_string_lossy().contains("echowave_client"));
            assert!(full_path.to_string_lossy().contains(file));
        }
    }

    /// 真实登录测试
    ///
    /// 该测试使用真实的 API 端点和用户凭据进行登录测试
    /// 需要确保后端服务可用并且用户凭据有效
    #[tokio::test]
    #[ignore] // 默认忽略，需要显式运行：cargo test test_real_login -- --ignored
    async fn test_real_login() {
        use crate::adapters::file::FileConfig;
        use crate::adapters::http::HttpConfig;
        use std::collections::HashMap;
        use tracing::info;
        unsafe { std::env::set_var("RUST_LOG", "trace") };
        tracing_subscriber::fmt::init();

        // 创建真实的 HTTP 配置
        let mut default_headers = HashMap::new();
        default_headers.insert("Content-Type".to_string(), "application/json".to_string());
        default_headers.insert("Accept".to_string(), "application/json".to_string());

        let http_config = HttpConfig {
            base_url: "http://api.echowave.cn:8090".to_string(),
            timeout_ms: 30000,
            user_agent: "EchoWave-Client/1.0".to_string(),
            default_headers,
            retry_config: crate::adapters::RetryConfig::default(),
        };

        let file_config = FileConfig::default();

        // 创建适配器
        let file_adapter = Arc::new(FileAdapter::new(file_config).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(http_config).unwrap());

        // 创建用户服务
        let service = UserService::new(http_adapter.clone(), file_adapter).unwrap();
        let service = Arc::new(service);
        http_adapter.set_interceptor(service.clone());

        // 启动服务
        service.start().await.unwrap();
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let trace_id = Uuid::new_v4();
        let phone = "13011129301".to_string();
        let password = "123456".to_string();

        info!("开始真实登录测试 - 手机号: {}", phone);

        // 尝试登录
        match service
            .login(phone.clone(), password.clone(), trace_id)
            .await
        {
            Ok(user_state) => {
                info!("✅ 登录成功！");

                // 验证用户状态
                assert!(user_state.is_logged_in, "用户应该处于登录状态");
                assert!(user_state.user_info.is_some(), "用户信息应该存在");

                let user_info = user_state.user_info.unwrap();
                assert_eq!(user_info.phone, phone, "手机号应该匹配");

                info!("用户信息: {:?}", user_info);
                info!("钱包余额: {}", user_state.wallet_balance);

                // 验证服务状态
                assert!(service.is_logged_in(), "服务应该显示已登录");

                let current_user = service.get_current_user();
                assert!(current_user.is_logged_in, "应该能获取到当前用户");

                // 测试刷新钱包余额
                info!("测试刷新钱包余额...");
                match service.refresh_wallet_balance(trace_id).await {
                    Ok(balance) => {
                        info!("✅ 钱包余额刷新成功: {} 元", balance);
                    }
                    Err(e) => {
                        info!("⚠️ 钱包余额刷新失败: {}", e);
                        panic!("钱包余额刷新失败: {}", e);
                    }
                }

                // 测试获取当前token
                info!("测试获取当前token...");
                match service.get_current_token() {
                    Some(token) => {
                        info!("✅ 获取到有效token: {}...", &token[..50]);
                    }
                    None => {
                        info!("⚠️ 没有获取到token");
                        panic!("没有获取到token");
                    }
                }

                // 测试登出
                info!("测试登出...");
                service.logout(trace_id).await.unwrap();
                assert!(!service.is_logged_in(), "登出后应该未登录");
                info!("✅ 登出成功！");
            }
            Err(e) => {
                info!("❌ 登录失败: {}", e);
                panic!("真实登录测试失败: {}", e);
            }
        }
    }

    /// 真实验证码登录测试
    ///
    /// 该测试使用真实的 API 端点和验证码进行登录测试
    /// 需要手动获取验证码并更新代码中的验证码值
    #[tokio::test]
    #[ignore] // 默认忽略，需要显式运行：cargo test test_real_login_with_code -- --ignored
    async fn test_real_login_with_code() {
        use crate::adapters::file::FileConfig;
        use crate::adapters::http::HttpConfig;
        use std::collections::HashMap;
        use tracing::info;

        // 初始化日志（测试环境）
        tracing_subscriber::fmt::init();

        // 创建真实的 HTTP 配置
        let mut default_headers = HashMap::new();
        default_headers.insert("Content-Type".to_string(), "application/json".to_string());
        default_headers.insert("Accept".to_string(), "application/json".to_string());

        let http_config = HttpConfig {
            base_url: "https://api.echowave.ai".to_string(),
            timeout_ms: 30000,
            user_agent: "EchoWave-Client/1.0".to_string(),
            default_headers,
            retry_config: crate::adapters::RetryConfig::default(),
        };

        let file_config = FileConfig::default();

        // 创建适配器
        let file_adapter = Arc::new(FileAdapter::new(file_config).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(http_config).unwrap());

        // 创建用户服务
        let service = UserService::new(http_adapter, file_adapter).unwrap();

        // 启动服务
        service.start().await.unwrap();
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let trace_id = Uuid::new_v4();
        let phone = "13011129301".to_string();
        let code = "123456".to_string(); // 需要替换为真实的验证码

        info!("开始真实验证码登录测试 - 手机号: {}", phone);
        info!("⚠️ 注意：需要先获取真实验证码并更新代码中的验证码值");

        // 尝试验证码登录
        match service
            .login_with_code(phone.clone(), code.clone(), trace_id)
            .await
        {
            Ok(user_state) => {
                info!("✅ 验证码登录成功！");

                // 验证用户状态
                assert!(user_state.is_logged_in, "用户应该处于登录状态");
                assert!(user_state.user_info.is_some(), "用户信息应该存在");

                let user_info = user_state.user_info.unwrap();
                assert_eq!(user_info.phone, phone, "手机号应该匹配");

                info!("用户信息: {:?}", user_info);

                // 测试登出
                info!("测试登出...");
                service.logout(trace_id).await.unwrap();
                assert!(!service.is_logged_in(), "登出后应该未登录");
                info!("✅ 登出成功！");
            }
            Err(e) => {
                info!("❌ 验证码登录失败: {}", e);
                // 验证码登录失败可能是因为验证码过期或无效，这是正常情况
                info!("💡 这可能是因为验证码无效或过期，请获取真实验证码后重试");
            }
        }
    }

    /// 真实自动登录测试
    ///
    /// 该测试依赖于之前的登录留下的凭据文件
    /// 需要先运行 test_real_login 使其保存凭据
    #[tokio::test]
    #[ignore] // 默认忽略，需要显式运行：cargo test test_real_auto_login -- --ignored
    async fn test_real_auto_login() {
        use crate::adapters::file::FileConfig;
        use crate::adapters::http::HttpConfig;
        use std::collections::HashMap;
        use tracing::info;

        // 初始化日志（测试环境）
        tracing_subscriber::fmt::init();

        // 创建真实的 HTTP 配置
        let mut default_headers = HashMap::new();
        default_headers.insert("Content-Type".to_string(), "application/json".to_string());
        default_headers.insert("Accept".to_string(), "application/json".to_string());

        let http_config = HttpConfig {
            base_url: "https://api.echowave.ai".to_string(),
            timeout_ms: 30000,
            user_agent: "EchoWave-Client/1.0".to_string(),
            default_headers,
            retry_config: crate::adapters::RetryConfig::default(),
        };

        let file_config = FileConfig::default();

        // 创建适配器
        let file_adapter = Arc::new(FileAdapter::new(file_config).unwrap());
        let http_adapter = Arc::new(HttpAdapter::new(http_config).unwrap());

        // 创建用户服务
        let service = UserService::new(http_adapter, file_adapter).unwrap();

        // 启动服务
        service.start().await.unwrap();
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;

        let trace_id = Uuid::new_v4();

        info!("开始真实自动登录测试");

        // 尝试自动登录
        match service.auto_login(trace_id).await {
            Ok(Some(user_state)) => {
                info!("✅ 自动登录成功！");

                // 验证用户状态
                assert!(user_state.is_logged_in, "用户应该处于登录状态");
                assert!(user_state.user_info.is_some(), "用户信息应该存在");

                let user_info = user_state.user_info.unwrap();
                info!("用户信息: {:?}", user_info);

                // 验证服务状态
                assert!(service.is_logged_in(), "服务应该显示已登录");
            }
            Ok(None) => {
                info!("⚠️ 没有保存的凭据或凭据已过期");
                info!("💡 请先运行 test_real_login 测试以保存凭据");
            }
            Err(e) => {
                info!("❌ 自动登录失败: {}", e);
                panic!("真实自动登录测试失败: {}", e);
            }
        }
    }
}
