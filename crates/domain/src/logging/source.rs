/// 日志来源枚举，强制约束 source 类型，避免滥用
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>py, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize)]
pub enum LogSource {
    /// 核心业务逻辑
    Core,
    /// 守护进程服务  
    Daemon,
    /// 桌面适配器（Tauri）
    DesktopAdapter,
    /// 桌面（Vue）
    Desktop,
    /// 容器运行时
    Runtime,
    /// 网络组件
    Network,
    /// 任务调度器
    Scheduler,
}

impl LogSource {
    /// 获取字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            LogSource::Core => "core",
            LogSource::Daemon => "daemon",
            LogSource::DesktopAdapter => "desktop-adapter",
            LogSource::Desktop => "desktop",
            LogSource::Runtime => "runtime",
            LogSource::Network => "network",
            LogSource::Scheduler => "scheduler",
        }
    }

    /// 根据 target 和其他信息推断 source
    pub fn from_target(target: &str, explicit_source: Option<&str>) -> LogSource {
        // 如果明确指定了 source，优先使用
        if let Some(source) = explicit_source {
            return source.parse().unwrap_or(LogSource::Core);
        }

        // 根据 target 推断 source
        if target.starts_with("echowave_client::agent") || target.contains("agent") {
            LogSource::Daemon
        } else if target.starts_with("echowave_client::desktop_adapter") || target.contains("tauri")
        {
            LogSource::DesktopAdapter
        } else if target.contains("vue") || target.contains("frontend") || target.contains("react")
        {
            LogSource::Desktop
        } else if target.contains("dockerd") {
            LogSource::Runtime
        } else if target.contains("tailscaled") {
            LogSource::Network
        } else if target.contains("nomad") {
            LogSource::Scheduler
        } else if target.starts_with("echowave_client::core")
            || target.starts_with("echowave_client::domain")
        {
            LogSource::Core
        } else {
            // 默认 source
            LogSource::Core
        }
    }
}

impl std::fmt::Display for LogSource {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

impl std::str::FromStr for LogSource {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> anyhow::Result<Self> {
        match s {
            "core" => Ok(LogSource::Core),
            "domain" => Ok(LogSource::Core),
            "daemon" => Ok(LogSource::Daemon),
            "desktop-adapter" => Ok(LogSource::DesktopAdapter),
            "desktop" => Ok(LogSource::Desktop),
            "runtime" => Ok(LogSource::Runtime),
            "network" => Ok(LogSource::Network),
            "scheduler" => Ok(LogSource::Scheduler),
            // 兼容旧的命名方式
            "agent" => Ok(LogSource::Daemon),
            "ui-tauri" => Ok(LogSource::DesktopAdapter),
            "tauri" => Ok(LogSource::DesktopAdapter),
            "ui-vue" => Ok(LogSource::Desktop),
            "frontend" => Ok(LogSource::Desktop),
            "vue" => Ok(LogSource::Desktop),
            "react" => Ok(LogSource::Desktop),
            "docker" => Ok(LogSource::Runtime),
            "tailscale" => Ok(LogSource::Network),
            "nomad" => Ok(LogSource::Scheduler),
            _ => Err(anyhow::anyhow!("不支持的日志来源: {}", s)),
        }
    }
}
