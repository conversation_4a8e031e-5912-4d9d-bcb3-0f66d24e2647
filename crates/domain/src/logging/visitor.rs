use std::{fmt::Debug, str::FromStr};
use tracing::field::{Field, Visit};

use crate::logging::TraceId;

/// 用于从 Span 字段中提取 trace_id 的 Visitor
pub struct TraceIdVisitor<'a> {
    pub trace_id: &'a mut Option<TraceId>,
}

/// 用于从 Event 字段中提取消息和 source 的 Visitor
#[derive(Debug)]
pub struct MessageVisitor<'a> {
    pub message: &'a mut String,
    pub source: &'a mut Option<String>,
    pub trace_id: &'a mut Option<TraceId>,
}

impl<'a> Visit for TraceIdVisitor<'a> {
    fn record_str(&mut self, field: &tracing::field::Field, value: &str) {
        if field.name() == "trace_id" {
            if let Ok(id) = TraceId::from_str(value) {
                *self.trace_id = Some(id);
            }
        }
    }

    fn record_debug(&mut self, field: &Field, value: &dyn Debug) {
        if field.name() == "trace_id" {
            if let Ok(id) = TraceId::from_str(&format!("{value:?}")) {
                *self.trace_id = Some(id);
            }
        }
    }
}

impl Visit for MessageVisitor<'_> {
    fn record_str(&mut self, field: &tracing::field::Field, value: &str) {
        if field.name() == "message" {
            *self.message = value.to_string();
        } else if field.name() == "source" {
            *self.source = Some(value.to_string());
        } else if field.name() == "trace_id" {
            if let Ok(id) = TraceId::from_str(value) {
                *self.trace_id = Some(id);
            }
        }
    }

    fn record_debug(&mut self, field: &Field, value: &dyn Debug) {
        if field.name() == "message" {
            *self.message = format!("{value:?}");
        } else if field.name() == "source" {
            *self.source = Some(format!("{value:?}"));
        } else if field.name() == "trace_id" {
            if let Ok(id) = TraceId::from_str(format!("{value:?}").as_str()) {
                *self.trace_id = Some(id);
            }
        }
    }
}
