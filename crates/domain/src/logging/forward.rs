//! # 日志转发器
//!
//! 该模块提供将其他系统的日志转发到日志系统的功能。
//! 支持从 `crates/protocol/src/events/log.rs` 定义的 `LogEntry` 结构转发到当前的日志基础设施。
//!
//! ## 核心功能
//!
//! ### LogForwarder
//! - 接收来自其他系统的 `LogEntry` 结构化日志
//! - 将外部日志转换为结构化日志并写入到现有的日志系统中
//! - 确保所有元数据字段都能正确传递（module、target、level、file、line）
//! - 支持所有日志级别 (Error, Warn, Info, Debug, Trace)
//! - 保持原始日志的所有元数据 (timestamp, trace_id, file, line 等)
//!
//! ## 使用场景
//!
//! - 从守护进程 (Agent) 转发日志到核心系统
//! - 接收来自其他服务的结构化日志
//! - 统一多系统的日志输出到日志基础设施中
//!
//! ## 架构设计
//!
//! ```text
//! 外部系统 (Agent/其他服务)
//!     │
//!     ├─ LogEntry (protocol/events/log.rs)
//!     │
//!     ├─ LogForwarder::forward_log()
//!     │
//!     ├─ 转换为 StructuredLogEntry
//!     │
//!     ├─ 直接写入到日志写入器
//!     │
//!     └─ 日志系统 (FileLayer, UILayer, Console)
//! ```

use crate::logging::{LogLevel, StructuredLogEntry};
use protocol::events::log::{LogEntry, LogLevel as ProtocolLogLevel};
use std::sync::{Arc, OnceLock, RwLock};

/// 日志写入器 trait
///
/// 定义了日志写入器需要实现的接口，用于直接写入日志而不通过 tracing 宏
pub trait LogWriter: Send + Sync {
    /// 写入结构化日志条目
    fn write_log(&self, entry: StructuredLogEntry) -> anyhow::Result<()>;
}

/// 全局日志写入器注册表
///
/// 存储所有注册的日志写入器，用于日志转发
static LOG_WRITERS: OnceLock<RwLock<Vec<Arc<dyn LogWriter>>>> = OnceLock::new();

/// 注册日志写入器
///
/// 在日志系统初始化时调用，注册各种日志写入器（FileLayer、UILayer 等）
pub fn register_log_writer(writer: Arc<dyn LogWriter>) {
    let writers = LOG_WRITERS.get_or_init(|| RwLock::new(Vec::new()));
    if let Ok(mut writers_guard) = writers.write() {
        writers_guard.push(writer);
    }
}

/// 日志转发器
///
/// 负责将外部系统的 `LogEntry` 转换为结构化日志并转发到当前的日志系统。
/// 直接操作底层的日志写入器，确保所有元数据字段都能正确传递。
pub struct LogForwarder;

impl LogForwarder {
    /// 创建新的日志转发器实例
    pub fn new() -> Self {
        Self
    }

    /// 将 `LogEntry` 转发到日志系统
    ///
    /// 这是核心方法，接收外部日志条目并将其转换为结构化日志。
    /// 直接写入到注册的日志写入器，完全绕过 tracing 宏。
    ///
    /// # 参数
    /// * `log_entry` - 要转发的日志条目
    ///
    /// # 返回值
    /// * `Ok(())` - 转发成功
    /// * `Err(anyhow::Error)` - 转发失败，包含错误信息
    pub fn forward_log(&self, log_entry: LogEntry) -> anyhow::Result<()> {
        // 转换协议日志级别为内部日志级别
        let level = self.convert_protocol_level_to_internal(&log_entry.level);

        // 获取目标，如果没有则使用模块名
        let target = log_entry.target.as_deref().unwrap_or(&log_entry.module);

        // 创建结构化日志条目
        let mut structured_entry = StructuredLogEntry::new(
            level,
            &log_entry.module, // module 字段
            target,            // target 字段
            &log_entry.message,
            log_entry.trace_id,
        );

        // 设置来源信息
        structured_entry = structured_entry.with_source(&log_entry.source);

        // 设置文件和行号信息
        // 如果没有提供，使用转发源的默认值
        let (file, line) = if let Some(file) = &log_entry.file {
            (file.clone(), log_entry.line.unwrap_or(0))
        } else {
            // 根据源设置默认文件名
            let default_file = match log_entry.source.as_str() {
                "UI" | "ui" => "UI",
                "AGENT" | "agent" => "AGENT",
                "DAEMON" | "daemon" => "DAEMON",
                "DESKTOP" | "desktop" => "DESKTOP",
                source => source,
            };
            (default_file.to_string(), 0)
        };
        structured_entry = structured_entry.with_file_info(&file, line);

        // 添加额外的元数据字段
        structured_entry = structured_entry
            .with_field("forwarded", serde_json::json!(true))
            .with_field("original_timestamp", serde_json::json!(log_entry.timestamp));

        // 直接调用日志转发函数
        forward_to_writers(structured_entry)?;

        Ok(())
    }

    /// 批量转发多个日志条目
    ///
    /// 为了提高性能，支持批量处理多个日志条目。
    /// 每个日志条目独立处理，单个失败不会影响其他条目。
    ///
    /// # 参数
    /// * `log_entries` - 要转发的日志条目列表
    ///
    /// # 返回值
    /// * `Ok(usize)` - 成功转发的日志条目数量
    /// * `Err(anyhow::Error)` - 批量处理过程中的关键错误
    pub fn forward_logs(&self, log_entries: Vec<LogEntry>) -> anyhow::Result<usize> {
        let mut success_count = 0;
        let total_count = log_entries.len();

        for (index, log_entry) in log_entries.into_iter().enumerate() {
            match self.forward_log(log_entry) {
                Ok(()) => {
                    success_count += 1;
                }
                Err(e) => {
                    // 创建警告日志条目
                    let warning_entry = StructuredLogEntry::new(
                        LogLevel::Warn,
                        "domain::logging::forward",
                        "domain::logging::forward",
                        &format!(
                            "转发第 {} 个日志条目失败 ({}/{}): {}",
                            index + 1,
                            index + 1,
                            total_count,
                            e
                        ),
                        Some(crate::context::get_global_trace_id()),
                    );

                    // 尝试记录警告（忽略错误）
                    let _ = forward_to_writers(warning_entry);
                }
            }
        }

        if success_count < total_count {
            // 创建信息日志条目
            let info_entry = StructuredLogEntry::new(
                LogLevel::Info,
                "domain::logging::forward",
                "domain::logging::forward",
                &format!(
                    "批量日志转发完成: 成功 {}/{} 条",
                    success_count, total_count
                ),
                Some(crate::context::get_global_trace_id()),
            );

            // 尝试记录信息（忽略错误）
            let _ = forward_to_writers(info_entry);
        }

        Ok(success_count)
    }

    /// 将协议日志级别转换为内部日志级别
    fn convert_protocol_level_to_internal(&self, level: &ProtocolLogLevel) -> LogLevel {
        match level {
            ProtocolLogLevel::Error => LogLevel::Error,
            ProtocolLogLevel::Warn => LogLevel::Warn,
            ProtocolLogLevel::Info => LogLevel::Info,
            ProtocolLogLevel::Debug => LogLevel::Debug,
            ProtocolLogLevel::Trace => LogLevel::Trace,
        }
    }
}

impl Default for LogForwarder {
    fn default() -> Self {
        Self::new()
    }
}

/// 直接转发到日志写入器的函数
///
/// 这个函数完全绕过 tracing 宏，直接将结构化日志条目写入到注册的日志写入器。
///
/// # 参数
/// * `entry` - 结构化日志条目
///
/// # 返回值
/// * `Ok(())` - 转发成功
/// * `Err(anyhow::Error)` - 转发失败
fn forward_to_writers(entry: StructuredLogEntry) -> anyhow::Result<()> {
    // 获取注册的日志写入器
    let writers = LOG_WRITERS.get_or_init(|| RwLock::new(Vec::new()));

    // 读取写入器列表
    let writers_list = if let Ok(guard) = writers.read() {
        guard.clone()
    } else {
        // 如果无法获取读锁，返回错误
        return Err(anyhow::anyhow!("无法获取日志写入器读锁"));
    };

    // 如果没有注册任何写入器，直接返回成功
    if writers_list.is_empty() {
        return Ok(());
    }

    // 转发到所有注册的写入器
    let mut errors = Vec::new();
    for writer in writers_list.iter() {
        if let Err(e) = writer.write_log(entry.clone()) {
            errors.push(format!("写入器错误: {}", e));
        }
    }

    // 如果有错误，返回聚合错误
    if !errors.is_empty() {
        return Err(anyhow::anyhow!("日志转发失败: {}", errors.join("; ")));
    }

    Ok(())
}

/// 全局日志转发器实例
///
/// 为了方便使用，提供一个全局的日志转发器实例。
/// 这避免了在需要转发日志的地方重复创建转发器实例。
static GLOBAL_LOG_FORWARDER: OnceLock<LogForwarder> = OnceLock::new();

/// 获取全局日志转发器实例
///
/// 线程安全的单例访问，返回全局日志转发器的引用。
pub fn global_log_forwarder() -> &'static LogForwarder {
    GLOBAL_LOG_FORWARDER.get_or_init(LogForwarder::new)
}

/// 便捷函数：转发单个日志条目
///
/// 使用全局转发器转发单个日志条目的快捷函数。
pub fn forward_log(log_entry: LogEntry) -> anyhow::Result<()> {
    global_log_forwarder().forward_log(log_entry)
}

/// 便捷函数：批量转发日志条目
///
/// 使用全局转发器批量转发日志条目的快捷函数。
pub fn forward_logs(log_entries: Vec<LogEntry>) -> anyhow::Result<usize> {
    global_log_forwarder().forward_logs(log_entries)
}
