use crate::logging::TraceId;
use crate::logging::level::LogLevel;
use crate::logging::source::LogSource;
use crate::logging::{StructuredLogEntry, forward::LogWriter, visitor::MessageVisitor};
use anyhow::Result;
use chrono::Local;
use dashmap::DashMap;
use shared::lru_cache::LruCache;
use std::io::Write;
use std::path::PathBuf;
use std::sync::atomic::AtomicI64;
use std::sync::{
    Arc, Mutex,
    atomic::{AtomicBool, AtomicU64, Ordering},
};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tokio_util::sync::CancellationToken;
use tracing_subscriber::layer::Context;
use tracing_subscriber::{Layer, registry::LookupSpan};

/// 性能统计信息
#[derive(Debug, <PERSON>lone)]
pub struct PerformanceStats {
    pub source: LogSource,
    pub write_count: u64,
    pub rotation_count: u64,
    pub total_rotation_time_ms: u64,
    pub avg_rotation_time_ms: u64,
    pub current_file_size: u64,
    pub last_rotation_time: i64,
}

/// 每个 source 的文件状态
///
/// 注意：虽然外层用了 DashMap，但单个 source 内部仍需要同步原语：
/// - DashMap: 解决不同 source 间的并发访问
/// - 内部锁: 解决同一 source 内的文件写入、轮转等操作的原子性
pub struct SourceFileState {
    writer: Mutex<Option<std::fs::File>>,
    current_date: Mutex<String>,
    file_size: AtomicU64,    // 原子操作避免文件大小统计的竞态条件
    is_rotating: AtomicBool, // 防止递归轮转的标记
    write_count: AtomicU64,  // 写入计数，用于优化轮转检查频率

    // 性能监控指标
    rotation_count: AtomicU64,         // 轮转次数统计
    total_rotation_time_ms: AtomicU64, // 总轮转时间（毫秒）
    last_rotation_time: AtomicI64,     // 上次轮转时间（Timestamp）
}

impl SourceFileState {
    fn new() -> Self {
        Self {
            writer: Mutex::new(None),
            current_date: Mutex::new(FileLayer::get_current_date_string()),
            file_size: AtomicU64::new(0),
            is_rotating: AtomicBool::new(false),
            write_count: AtomicU64::new(0),

            // 初始化性能监控指标
            rotation_count: AtomicU64::new(0),
            total_rotation_time_ms: AtomicU64::new(0),
            last_rotation_time: AtomicI64::new(0),
        }
    }
}

/// 文件日志层实现 - 统一平面目录存储
#[derive(Clone)]
pub struct FileLayer {
    log_dir: PathBuf,
    source_states: Arc<DashMap<LogSource, SourceFileState>>,
    debug_cache: Arc<LruCache<TraceId, Vec<StructuredLogEntry>>>,
    rotation_manager: Arc<BackgroundRotationManager>,
}

/// 日志文件最大大小（10MB）
const MAX_LOG_FILE_SIZE: u64 = 10 * 1024 * 1024;

/// 日志保留天数（7天）
const LOG_RETENTION_DAYS: u32 = 7;

/// 轮转请求类型
#[derive(Debug, Clone)]
pub enum RotationRequest {
    /// 立即轮转请求
    Immediate {
        source: LogSource,
        reason: RotationReason,
    },
    /// 定时检查所有source的轮转条件
    PeriodicCheck,
}

/// 轮转原因
#[derive(Debug, Clone)]
pub enum RotationReason {
    /// 文件大小超过限制
    SizeExceeded(u64),
    /// 日期发生变化
    DateChanged(String),
    /// 手动触发轮转
    Manual,
}

/// 后台轮转管理器
///
/// 负责在独立的异步任务中处理所有的日志轮转操作，避免阻塞写入线程。
/// 支持三种轮转触发机制：
/// 1. 阈值触发：文件大小超过限制时立即轮转
/// 2. 定时检查：每30秒检查所有source的轮转条件
/// 3. 事件驱动：日期变更时立即轮转
pub struct BackgroundRotationManager {
    /// 轮转请求发送端
    request_sender: mpsc::Sender<RotationRequest>,
    /// 关闭信号
    shutdown_signal: CancellationToken,
    /// 后台任务句柄
    join_handle: Option<JoinHandle<()>>,
}

impl BackgroundRotationManager {
    /// 创建新的后台轮转管理器
    pub fn new(source_states: Arc<DashMap<LogSource, SourceFileState>>, log_dir: PathBuf) -> Self {
        let (request_sender, request_receiver) = mpsc::channel(64);
        let shutdown_signal = CancellationToken::new();

        let join_handle = Some(tokio::spawn(Self::rotation_loop(
            request_receiver,
            source_states,
            log_dir,
            shutdown_signal.clone(),
        )));

        Self {
            request_sender,
            shutdown_signal,
            join_handle,
        }
    }

    /// 发送轮转请求（非阻塞）
    pub fn request_rotation(&self, request: RotationRequest) -> Result<()> {
        self.request_sender
            .try_send(request)
            .map_err(|err| anyhow::anyhow!("无法发送轮转请求，因为: {}", err))?;
        Ok(())
    }

    /// 关闭轮转管理器
    pub async fn shutdown(mut self) -> Result<()> {
        // 发送关闭信号
        self.shutdown_signal.cancel();

        // 等待后台任务完成
        if let Some(handle) = self.join_handle.take() {
            handle.await.map_err(|e| {
                tracing::error!("轮转任务异常结束: {}", e);
                anyhow::anyhow!("轮转任务异常结束: {}", e)
            })?;
        }

        Ok(())
    }

    /// 后台轮转循环
    async fn rotation_loop(
        mut request_receiver: mpsc::Receiver<RotationRequest>,
        source_states: Arc<DashMap<LogSource, SourceFileState>>,
        log_dir: PathBuf,
        shutdown_signal: CancellationToken,
    ) {
        let mut periodic_timer = tokio::time::interval(Duration::from_secs(30));

        // 跳过第一次触发，因为我们刚启动
        periodic_timer.tick().await;

        loop {
            tokio::select! {
                // 处理轮转请求
                request = request_receiver.recv() => {
                    match request {
                        Some(RotationRequest::Immediate { source, reason }) => {
                            if let Err(e) = Self::handle_immediate_rotation(
                                &source_states,
                                &log_dir,
                                source,
                                reason
                            ).await {
                                tracing::error!(
                                    source = ?source,
                                    error = %e,
                                    "立即轮转失败"
                                );
                            }
                        }
                        Some(RotationRequest::PeriodicCheck) => {
                            Self::handle_periodic_check(&source_states, &log_dir).await;
                        }
                        None => {
                            tracing::warn!("轮转请求通道已关闭");
                            break;
                        }
                    }
                }

                // 定时检查
                _ = shutdown_signal.cancelled() => {
                    tracing::info!("后台轮转管理器收到关闭信号");
                    break
                }
            }
        }

        tracing::info!("后台轮转管理器已退出");
    }

    /// 处理立即轮转请求
    async fn handle_immediate_rotation(
        source_states: &Arc<DashMap<LogSource, SourceFileState>>,
        log_dir: &PathBuf,
        source: LogSource,
        _reason: RotationReason,
    ) -> Result<()> {
        if let Some(state_ref) = source_states.get(&source) {
            // 检查是否已经在轮转中
            if state_ref
                .is_rotating
                .compare_exchange(false, true, Ordering::SeqCst, Ordering::Relaxed)
                .is_err()
            {
                return Ok(()); // 已在轮转中，直接返回
            }

            let _guard = RotationGuard::new(&state_ref.is_rotating);

            // 执行轮转操作
            FileLayer::execute_rotation_internal(&state_ref, log_dir, source).await?;
        }

        Ok(())
    }

    /// 处理定时检查
    async fn handle_periodic_check(
        source_states: &Arc<DashMap<LogSource, SourceFileState>>,
        log_dir: &PathBuf,
    ) {
        for state_entry in source_states.iter() {
            let source = *state_entry.key();
            let state = state_entry.value();

            // 检查是否需要轮转
            let should_rotate = {
                let current_size = state.file_size.load(Ordering::Relaxed);
                let current_date = FileLayer::get_current_date_string();
                let stored_date = state.current_date.lock().unwrap().clone();

                current_size >= MAX_LOG_FILE_SIZE || current_date != stored_date
            };

            if should_rotate {
                if let Err(e) = Self::handle_immediate_rotation(
                    source_states,
                    log_dir,
                    source,
                    RotationReason::Manual, // 定时检查触发的轮转
                )
                .await
                {
                    tracing::error!(
                        source = ?source,
                        error = %e,
                        "定时检查轮转失败"
                    );
                }
            }
        }
    }
}

/// RAII 守卫，确保轮转标记在离开作用域时被清除
struct RotationGuard<'a> {
    flag: &'a AtomicBool,
}

impl<'a> RotationGuard<'a> {
    fn new(flag: &'a AtomicBool) -> Self {
        Self { flag }
    }
}

impl<'a> Drop for RotationGuard<'a> {
    fn drop(&mut self) {
        self.flag.store(false, Ordering::Relaxed);
    }
}

impl FileLayer {
    pub fn new(
        log_dir: PathBuf,
        debug_cache: Arc<LruCache<TraceId, Vec<StructuredLogEntry>>>,
    ) -> Self {
        let source_states = Arc::new(DashMap::new());
        let rotation_manager = Arc::new(BackgroundRotationManager::new(
            Arc::clone(&source_states),
            log_dir.clone(),
        ));

        Self {
            log_dir,
            source_states,
            debug_cache,
            rotation_manager,
        }
    }

    /// 获取当前日期字符串 (YYYY-MM-DD 格式)
    fn get_current_date_string() -> String {
        Local::now().format("%Y-%m-%d").to_string()
    }

    /// 获取当前日志文件路径（统一平面目录）
    fn get_current_log_file_path(&self, source: LogSource, date: &str) -> PathBuf {
        self.log_dir.join(format!("{}-{}.jsonl", source, date))
    }

    /// 确保日志目录存在
    fn ensure_log_dir(&self) -> Result<()> {
        std::fs::create_dir_all(&self.log_dir)?;
        Ok(())
    }

    /// 从文件名中解析日期
    ///
    /// 支持的文件名格式：
    /// - {source}-YYYY-MM-DD.jsonl
    /// - {source}-YYYY-MM-DD.zst
    /// - {source}-YYYY-MM-DD.zst.{counter}
    /// - {source}-YYYY-MM-DD-HHMMSS.jsonl
    pub fn parse_date_from_filename(filename: &str) -> Option<chrono::NaiveDate> {
        // 移除可能的扩展名和序号
        let name_without_ext = filename.replace(".jsonl", "").replace(".zst", "");

        // 移除可能的序号后缀 (如 .1, .2, .3...)
        let name_clean = if let Some(last_dot_pos) = name_without_ext.rfind('.') {
            let suffix = &name_without_ext[last_dot_pos + 1..];
            if suffix.chars().all(|c| c.is_ascii_digit()) {
                &name_without_ext[..last_dot_pos]
            } else {
                &name_without_ext
            }
        } else {
            &name_without_ext
        };

        // 寻找日期模式 YYYY-MM-DD
        // 可能的格式：
        // - source-YYYY-MM-DD (4 个部分)
        // - source-YYYY-MM-DD-HHMMSS (5 个部分)
        // - complex-source-YYYY-MM-DD (>4 个部分)
        let parts: Vec<&str> = name_clean.split('-').collect();

        if parts.len() == 4 {
            // 处理 "source-YYYY-MM-DD" 格式
            let year_str = parts[1];
            let month_str = parts[2];
            let day_str = parts[3];

            // 检查是否为有效的日期格式
            if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                if let (Ok(year), Ok(month), Ok(day)) = (
                    year_str.parse::<i32>(),
                    month_str.parse::<u32>(),
                    day_str.parse::<u32>(),
                ) {
                    // 使用 chrono 解析日期
                    if let Some(date) = chrono::NaiveDate::from_ymd_opt(year, month, day) {
                        return Some(date);
                    }
                }
            }
        } else if parts.len() >= 5 {
            // 处理复杂格式，如 "complex-source-YYYY-MM-DD" 或 "source-YYYY-MM-DD-HHMMSS"
            // 先尝试解析倒数第四、三、二个部分作为日期 (适用于 "source-YYYY-MM-DD-HHMMSS" 格式)
            if parts.len() >= 5 {
                let year_str = parts[parts.len() - 4];
                let month_str = parts[parts.len() - 3];
                let day_str = parts[parts.len() - 2];

                // 检查是否为有效的日期格式
                if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                    if let (Ok(year), Ok(month), Ok(day)) = (
                        year_str.parse::<i32>(),
                        month_str.parse::<u32>(),
                        day_str.parse::<u32>(),
                    ) {
                        // 使用 chrono 解析日期
                        if let Some(date) = chrono::NaiveDate::from_ymd_opt(year, month, day) {
                            return Some(date);
                        }
                    }
                }
            }

            // 如果倒数第四、三、二个部分不是有效日期，尝试倒数第三、二、一个部分
            // 这处理 "complex-source-YYYY-MM-DD" 格式
            let year_str = parts[parts.len() - 3];
            let month_str = parts[parts.len() - 2];
            let day_str = parts[parts.len() - 1];

            // 检查是否为有效的日期格式
            if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                if let (Ok(year), Ok(month), Ok(day)) = (
                    year_str.parse::<i32>(),
                    month_str.parse::<u32>(),
                    day_str.parse::<u32>(),
                ) {
                    // 使用 chrono 解析日期
                    if let Some(date) = chrono::NaiveDate::from_ymd_opt(year, month, day) {
                        return Some(date);
                    }
                }
            }
        }

        None
    }

    /// 清理超过保留期的日志文件
    ///
    /// 扫描日志目录，删除超过 LOG_RETENTION_DAYS 天的日志文件
    /// 支持删除原始日志文件(.jsonl)和压缩文件(.zst)
    pub async fn cleanup_old_logs_in_dir(log_dir: &PathBuf) -> Result<()> {
        let current_date = chrono::Local::now().date_naive();
        let retention_days = chrono::Duration::days(LOG_RETENTION_DAYS as i64);
        let cutoff_date = current_date - retention_days;

        tracing::info!(
            "开始清理旧日志文件，保留期：{} 天，截止日期：{}",
            LOG_RETENTION_DAYS,
            cutoff_date
        );

        // 扫描日志目录
        let mut entries = match tokio::fs::read_dir(log_dir).await {
            Ok(entries) => entries,
            Err(e) => {
                tracing::error!("无法读取日志目录 {}: {}", log_dir.display(), e);
                return Err(e.into());
            }
        };

        let mut deleted_count = 0;
        let mut total_size_freed = 0u64;
        let mut files_to_delete = Vec::new();
        let mut files_to_compress = Vec::new();

        // 收集需要删除的文件
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();

            // 只处理文件，跳过目录
            if !path.is_file() {
                continue;
            }

            let filename = match path.file_name().and_then(|name| name.to_str()) {
                Some(name) => name,
                None => {
                    tracing::debug!("跳过无效文件名的文件: {}", path.display());
                    continue;
                }
            };

            // 只处理日志文件（.jsonl 或 .zst）
            if !filename.ends_with(".jsonl") && !filename.ends_with(".zst") {
                continue;
            }

            // 解析文件日期
            if let Some(file_date) = Self::parse_date_from_filename(filename) {
                // 检查是否超过保留期
                if file_date < cutoff_date {
                    // 获取文件大小
                    let file_size = match tokio::fs::metadata(&path).await {
                        Ok(metadata) => metadata.len(),
                        Err(e) => {
                            tracing::warn!("无法获取文件大小 {}: {}", path.display(), e);
                            0
                        }
                    };

                    files_to_delete.push((path.clone(), file_size));
                    total_size_freed += file_size;

                    tracing::debug!(
                        "标记删除过期日志文件: {} (日期: {}, 大小: {} 字节)",
                        filename,
                        file_date,
                        file_size
                    );
                }
                // 如果文件日期不是当前日期，并且文件是 .jsonl 文件，则标记为需要压缩
                else if file_date != current_date && filename.ends_with(".jsonl") {
                    files_to_compress.push(path.clone());
                    tracing::debug!("标记压缩日志文件: {} (日期: {})", filename, file_date);
                } else {
                    tracing::debug!("保留日志文件: {} (日期: {})", filename, file_date);
                }
            } else {
                tracing::debug!("无法解析文件日期，跳过: {}", filename);
            }
        }

        // 执行删除操作
        for (path, size) in files_to_delete {
            match tokio::fs::remove_file(&path).await {
                Ok(()) => {
                    deleted_count += 1;
                    tracing::debug!(
                        "成功删除过期日志文件: {} (大小: {} 字节)",
                        path.display(),
                        size
                    );
                }
                Err(e) => {
                    tracing::warn!("删除过期日志文件失败: {} - {}", path.display(), e);
                    // 删除失败时，从释放的总大小中减去这个文件的大小
                    total_size_freed -= size;
                }
            }
        }
        if deleted_count > 0 {
            tracing::info!(
                "日志清理完成：删除了 {} 个过期文件，释放了 {} 字节 ({:.2} MB) 空间",
                deleted_count,
                total_size_freed,
                total_size_freed as f64 / (1024.0 * 1024.0)
            );
        } else {
            tracing::info!("日志清理完成：未发现需要清理的过期文件");
        }

        // 执行压缩操作
        for path in files_to_compress {
            Self::compress_log_file_by_path(log_dir, &path).await?;
        }

        Ok(())
    }

    /// 执行轮转的内部实现（供后台轮转管理器使用）
    ///
    /// 这是从原有的 `rotate_log_if_needed` 方法中提取出的核心轮转逻辑，
    /// 专门用于后台轮转管理器的异步调用，避免递归风险。
    pub(crate) async fn execute_rotation_internal(
        state: &SourceFileState,
        log_dir: &PathBuf,
        source: LogSource,
    ) -> Result<()> {
        // 开始性能监控
        let rotation_start = Instant::now();
        let current_date = Self::get_current_date_string();

        tracing::info!("后台轮转开始执行({}), 当前日期: {}", source, current_date);

        // 确保日志目录存在
        std::fs::create_dir_all(log_dir)?;

        // 关闭当前文件写入器
        let Ok(mut writer) = state.writer.lock() else {
            tracing::error!("获取写入器锁失败({}), 无法执行轮转", source);
            return Err(anyhow::anyhow!("获取写入器锁失败"));
        };

        if writer.is_none() {
            tracing::warn!("日志文件写入器意外关闭({})", source);
        } else {
            *writer = None;
            tracing::debug!("已关闭日志文件写入器({})", source);
        }

        // 获取存储的日期并确定轮转策略
        let stored_date = if let Ok(mut date_guard) = state.current_date.lock() {
            let old_date = date_guard.clone();
            *date_guard = current_date.clone();
            old_date
        } else {
            tracing::error!("获取日期锁失败({}), 无法执行轮转", source);
            return Err(anyhow::anyhow!("获取日期锁失败"));
        };

        let get_current_log_file_path = |source: LogSource, date: &str| -> PathBuf {
            log_dir.join(format!("{}-{}.jsonl", source, date))
        };

        let get_timestamped_log_file_path = |source: LogSource, date: &str| -> PathBuf {
            let timestamp = Local::now().format("%H%M%S").to_string();
            log_dir.join(format!("{}-{}-{}.jsonl", source, date, timestamp))
        };

        // 确定要轮转的文件路径和新文件路径
        let (old_log_path, new_log_path, rotation_type) = if stored_date != current_date {
            // 日期轮转：旧文件用旧日期，新文件用新日期
            let old_path = if !stored_date.is_empty() {
                Some(get_current_log_file_path(source, &stored_date))
            } else {
                None
            };
            let new_path = get_current_log_file_path(source, &current_date);
            (old_path, new_path, "日期轮转")
        } else {
            // 大小轮转：将当前文件重命名为带时间戳的文件，新文件仍用当前日期
            let old_path = get_current_log_file_path(source, &current_date);
            let timestamped_path = get_timestamped_log_file_path(source, &current_date);

            // 重命名当前文件
            if old_path.exists() {
                std::fs::rename(&old_path, &timestamped_path)?;
                tracing::debug!(
                    "文件重命名成功({}): {} -> {}",
                    source,
                    old_path.display(),
                    timestamped_path.display()
                );
            }

            (Some(timestamped_path), old_path, "大小轮转")
        };

        tracing::info!(
            "执行{}({}), 新文件路径: {}",
            rotation_type,
            source,
            new_log_path.display()
        );

        // 重置文件大小
        let old_size = state.file_size.swap(0, Ordering::Relaxed);
        tracing::debug!("重置文件大小计数器({}), 原大小: {} 字节", source, old_size);

        // 如果有旧的日志文件，启动后台压缩任务
        if let Some(old_path) = old_log_path {
            if old_path.exists() {
                let source_name = source.to_string();
                let log_dir_clone = log_dir.clone();

                tracing::info!("启动后台压缩任务({}): {}", source_name, old_path.display());

                tokio::spawn(async move {
                    let span = tracing::info_span!(
                        "日志压缩",
                        trace_id = %crate::context::get_global_trace_id()
                    );
                    let _enter = span.enter();

                    if let Err(e) = Self::compress_log_file_by_path(&log_dir_clone, &old_path).await
                    {
                        tracing::warn!(
                            "压缩日志文件失败({}) {}: {}",
                            source_name,
                            old_path.display(),
                            e
                        );
                    }
                });
            }
        }

        // 在日志轮转时触发清理任务（仅在日期轮转时执行，避免频繁清理）
        if rotation_type == "日期轮转" {
            let log_dir_clone = log_dir.clone();
            tokio::spawn(async move {
                let span = tracing::info_span!(
                    "日期轮转",
                    trace_id = %crate::context::get_global_trace_id()
                );
                let _enter = span.enter();

                if let Err(e) = Self::cleanup_old_logs_in_dir(&log_dir_clone).await {
                    tracing::warn!("清理旧日志文件失败: {}", e);
                }
            });
        }

        // 创建新的日志文件
        let new_file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&new_log_path)?;

        tracing::debug!("创建新日志文件成功({}): {}", source, new_log_path.display());

        *writer = Some(new_file);

        // 更新性能统计
        let rotation_duration = rotation_start.elapsed();
        state.rotation_count.fetch_add(1, Ordering::Relaxed);
        state
            .total_rotation_time_ms
            .fetch_add(rotation_duration.as_millis() as u64, Ordering::Relaxed);

        // 更新最后轮转时间
        state
            .last_rotation_time
            .store(chrono::Utc::now().timestamp_millis(), Ordering::Relaxed);

        tracing::info!(
            "后台日志轮转完成({}), 已切换到新文件, 耗时: {:?}",
            source,
            rotation_duration
        );

        Ok(())
    }

    /// 获取指定 source 的性能统计信息
    pub fn get_performance_stats(&self, source: LogSource) -> Option<PerformanceStats> {
        self.source_states.get(&source).map(|state| {
            let rotation_count = state.rotation_count.load(Ordering::Relaxed);
            let total_rotation_time_ms = state.total_rotation_time_ms.load(Ordering::Relaxed);
            let write_count = state.write_count.load(Ordering::Relaxed);
            let file_size = state.file_size.load(Ordering::Relaxed);

            let avg_rotation_time_ms = if rotation_count > 0 {
                total_rotation_time_ms / rotation_count
            } else {
                0
            };

            let last_rotation_time = state.last_rotation_time.load(Ordering::Relaxed);

            PerformanceStats {
                source,
                write_count,
                rotation_count,
                total_rotation_time_ms,
                avg_rotation_time_ms,
                current_file_size: file_size,
                last_rotation_time,
            }
        })
    }

    /// 获取所有 source 的性能统计信息
    pub fn get_all_performance_stats(&self) -> Vec<PerformanceStats> {
        self.source_states
            .iter()
            .filter_map(|entry| {
                let source = *entry.key();
                self.get_performance_stats(source)
            })
            .collect()
    }

    /// 重置指定 source 的性能统计
    pub fn reset_performance_stats(&self, source: LogSource) {
        if let Some(state) = self.source_states.get(&source) {
            state.rotation_count.store(0, Ordering::Relaxed);
            state.total_rotation_time_ms.store(0, Ordering::Relaxed);
            state.last_rotation_time.store(0, Ordering::Relaxed);
        }
    }

    /// 压缩指定路径的日志文件
    async fn compress_log_file_by_path(log_dir: &PathBuf, log_path: &PathBuf) -> Result<()> {
        if !log_path.exists() {
            tracing::debug!("压缩跳过：日志文件不存在 {}", log_path.display());
            return Ok(());
        }
        let Some(file_name) = log_path.file_stem().and_then(|s| s.to_str()) else {
            tracing::warn!("压缩跳过：无法获取文件名 {}", log_path.display());
            return Ok(());
        };
        let Some(ext_name) = log_path.extension().and_then(|s| s.to_str()) else {
            tracing::warn!("压缩跳过：无法获取文件扩展名 {}", log_path.display());
            return Ok(());
        };

        if ext_name == "zst" {
            tracing::debug!("压缩跳过：日志文件已压缩 {}", log_path.display());
            return Ok(());
        }

        tracing::debug!("开始压缩日志文件: {}", log_path.display());

        let compressed_path = log_dir.join(format!("{}.zst", file_name));

        // 如果压缩文件已存在，生成序号后缀
        let final_compressed_path = if compressed_path.exists() {
            let mut counter = 1;
            loop {
                let numbered_path = log_dir.join(format!("{}.zst.{}", file_name, counter));
                if !numbered_path.exists() {
                    tracing::debug!("压缩文件已存在，使用序号后缀: {}", numbered_path.display());
                    break numbered_path;
                }
                counter += 1;
            }
        } else {
            compressed_path
        };

        // 使用 zstd 压缩文件
        let input_data = tokio::fs::read(log_path).await?;
        let original_size = input_data.len();

        tracing::debug!("读取原始文件完成，大小: {} 字节", original_size);

        let compressed_data = tokio::task::spawn_blocking(move || {
            zstd::bulk::compress(&input_data, 3) // 使用压缩级别 3
        })
        .await??;

        let compressed_size = compressed_data.len();
        let compression_ratio = (compressed_size as f64 / original_size as f64) * 100.0;

        tokio::fs::write(&final_compressed_path, compressed_data).await?;

        // 删除原始文件
        tokio::fs::remove_file(log_path).await?;

        tracing::info!(
            "日志文件压缩完成: {} -> {} (原大小: {} 字节, 压缩后: {} 字节, 压缩率: {:.1}%)",
            log_path.display(),
            final_compressed_path.display(),
            original_size,
            compressed_size,
            compression_ratio
        );

        Ok(())
    }

    /// 写入日志条目到文件
    fn write_log_entry(&self, entry: &StructuredLogEntry) -> Result<()> {
        // 获取 source，如果没有则使用默认值 LogSource::Core
        let source = entry
            .source
            .as_deref()
            .and_then(|s| s.parse::<LogSource>().ok())
            .unwrap_or(LogSource::Core);

        // 获取或创建 source 状态
        let state = self
            .source_states
            .entry(source)
            .or_insert_with(SourceFileState::new);

        // 增加写入计数（用于统计）
        let _current_write_count = state.write_count.fetch_add(1, Ordering::Relaxed) + 1;

        let log_line = serde_json::to_string(entry)?;
        let log_line_with_newline = format!("{}\n", log_line);
        let bytes_to_write = log_line_with_newline.len() as u64;

        // 如果当前没有写入器，创建一个
        if let Ok(mut writer) = state.writer.lock() {
            if writer.is_none() {
                // 确保日志目录存在
                self.ensure_log_dir()?;

                let current_date = Self::get_current_date_string();
                let log_path = self.get_current_log_file_path(source, &current_date);

                let file = std::fs::OpenOptions::new()
                    .create(true)
                    .append(true)
                    .open(&log_path)?;

                *writer = Some(file);

                tracing::debug!(
                    trace_id = %crate::context::get_global_trace_id(),
                    "创建新的日志文件写入器({}): {}",
                    source,
                    log_path.display()
                );

                // 更新当前日期和初始化文件大小
                if let Ok(mut date_guard) = state.current_date.lock() {
                    *date_guard = current_date.clone();
                }

                // 获取现有文件大小
                if let Ok(metadata) = std::fs::metadata(&log_path) {
                    let existing_size = metadata.len();
                    state.file_size.store(existing_size, Ordering::Relaxed);
                    tracing::debug!(
                        "检测到现有日志文件({}), 大小: {} 字节",
                        source,
                        existing_size
                    );
                } else {
                    state.file_size.store(0, Ordering::Relaxed);
                    tracing::debug!("创建新的日志文件({}), 初始大小: 0 字节", source);
                }
            }
        } else {
            tracing::error!("获取日志写入器锁失败({})", source);
            return Err(anyhow::anyhow!("获取日志写入器锁失败"));
        }

        // 写入日志
        if let Ok(mut writer_guard) = state.writer.lock() {
            if let Some(ref mut writer) = writer_guard.as_mut() {
                writer.write_all(log_line_with_newline.as_bytes())?;
                writer.flush()?;

                // 更新文件大小计数器
                let new_size =
                    state.file_size.fetch_add(bytes_to_write, Ordering::Relaxed) + bytes_to_write;

                // 定期记录文件大小状态（每1MB记录一次）
                if new_size % (1024 * 1024) < bytes_to_write {
                    tracing::debug!(
                        "日志文件当前大小({}): {} 字节 ({:.1} MB)",
                        source,
                        new_size,
                        new_size as f64 / (1024.0 * 1024.0)
                    );
                }

                // 检查是否需要发送轮转请求（非阻塞）
                self.check_and_request_rotation(source, new_size, &state);
            }
        } else {
            tracing::error!("获取日志写入器锁失败({})(写入时)", source);
            return Err(anyhow::anyhow!("获取日志写入器锁失败(写入时)"));
        }

        Ok(())
    }

    /// 检查是否需要发送轮转请求（非阻塞）
    ///
    /// 这个方法会检查文件大小和日期变更条件，如果满足轮转条件，
    /// 就向后台轮转管理器发送轮转请求。该操作是非阻塞的，
    /// 不会影响日志写入的性能。
    fn check_and_request_rotation(
        &self,
        source: LogSource,
        current_size: u64,
        state: &SourceFileState,
    ) {
        // 检查文件大小是否超过限制
        let size_exceeded = current_size >= MAX_LOG_FILE_SIZE;

        // 检查日期是否发生变化
        let date_changed = {
            let current_date = Self::get_current_date_string();
            let stored_date = state
                .current_date
                .lock()
                .map(|guard| guard.clone())
                .unwrap_or_default();
            current_date != stored_date
        };

        // 如果满足轮转条件，发送轮转请求
        if size_exceeded || date_changed {
            let reason = if size_exceeded {
                RotationReason::SizeExceeded(current_size)
            } else {
                RotationReason::DateChanged(Self::get_current_date_string())
            };

            let request = RotationRequest::Immediate { source, reason };

            // 非阻塞发送请求，失败也不影响日志写入
            if let Err(e) = self.rotation_manager.request_rotation(request) {
                tracing::warn!(
                    source = ?source,
                    error = %e,
                    "发送轮转请求失败，轮转管理器可能已关闭"
                );
            } else {
                tracing::debug!(
                    source = ?source,
                    current_size = current_size,
                    size_exceeded = size_exceeded,
                    date_changed = date_changed,
                    "已发送轮转请求"
                );
            }
        }
    }

    /// 当发生错误时，从缓存中获取相关的调试信息
    fn flush_debug_logs_for_trace(&self, trace_id: TraceId) -> Result<()> {
        if let Some(debug_entries) = self.debug_cache.remove(&trace_id) {
            for entry in debug_entries.iter() {
                self.write_log_entry(entry)?;
            }
        }
        Ok(())
    }

    /// 错误自动上报预留接口
    #[allow(dead_code)]
    fn report_error_to_server(&self, _error_entry: &StructuredLogEntry) -> Result<()> {
        // TODO: 实现错误自动上报逻辑
        // 这里可以调用 HTTP 客户端将错误信息发送到服务器
        // info!("开始上报错误日志（未实现）");
        Ok(())
    }

    /// 公共处理逻辑，用于处理日志条目
    fn process_log_entry(&self, entry: StructuredLogEntry) -> Result<()> {
        match entry.level.as_str() {
            "error" => {
                // 错误级别：写入文件并刷新调试缓存
                self.write_log_entry(&entry)?;

                // 刷新相关的调试日志
                if let Some(trace_id) = entry.trace_id {
                    self.flush_debug_logs_for_trace(trace_id)?;
                }

                // 预留：错误自动上报
                let _ = self.report_error_to_server(&entry);
            }
            "warn" | "info" => {
                // WARN/INFO 级别：直接写入文件
                self.write_log_entry(&entry)?;
            }
            "debug" | "trace" => {
                // DEBUG/TRACE 级别：缓存到 LRU
                if let Some(trace_id) = entry.trace_id {
                    if let Some(mut cached_entries) = self.debug_cache.get_mut(&trace_id) {
                        cached_entries.push(entry);
                    } else {
                        self.debug_cache.insert(trace_id, vec![entry]);
                    }
                }
            }
            _ => {
                // 未知级别，作为 INFO 处理
                self.write_log_entry(&entry)?;
            }
        }

        Ok(())
    }
}

impl LogWriter for FileLayer {
    fn write_log(&self, entry: StructuredLogEntry) -> Result<()> {
        self.process_log_entry(entry)
    }
}

impl<S> Layer<S> for FileLayer
where
    S: tracing::Subscriber + for<'lookup> LookupSpan<'lookup>,
{
    fn on_event(&self, event: &tracing::Event<'_>, _ctx: Context<'_, S>) {
        let metadata = event.metadata();
        let level = metadata.level();
        let target = metadata.target();

        // 提取消息内容和 source
        let mut message = String::new();
        let mut explicit_source = None;
        let mut trace_id = None;
        event.record(&mut MessageVisitor {
            message: &mut message,
            source: &mut explicit_source,
            trace_id: &mut trace_id,
        });

        // 获取当前的 trace_id
        let trace_id = trace_id.unwrap_or_else(|| super::current_trace_id());

        // 推断 source
        let source = LogSource::from_target(target, explicit_source.as_deref());

        // 创建结构化日志条目
        let mut entry = StructuredLogEntry::new(
            match *level {
                tracing::Level::ERROR => LogLevel::Error,
                tracing::Level::WARN => LogLevel::Warn,
                tracing::Level::INFO => LogLevel::Info,
                tracing::Level::DEBUG => LogLevel::Debug,
                tracing::Level::TRACE => LogLevel::Trace,
            },
            "core",
            target,
            &message,
            Some(trace_id.into()),
        )
        .with_source(source.as_str());

        // 添加文件信息
        if let Some(file) = metadata.file() {
            if let Some(line) = metadata.line() {
                entry = entry.with_file_info(file, line);
            }
        }

        match *level {
            tracing::Level::ERROR => {
                // 错误级别：写入文件并刷新调试缓存
                if let Err(e) = self.write_log_entry(&entry) {
                    eprintln!("Failed to write error log: {}", e);
                }

                // 刷新相关的调试日志
                if let Err(e) = self.flush_debug_logs_for_trace(trace_id) {
                    eprintln!("Failed to flush debug logs: {}", e);
                }

                // 预留：错误自动上报
                let _ = self.report_error_to_server(&entry);
            }
            tracing::Level::WARN | tracing::Level::INFO => {
                // WARN/INFO 级别：直接写入文件
                if let Err(e) = self.write_log_entry(&entry) {
                    eprintln!("Failed to write log: {}", e);
                }
            }
            tracing::Level::DEBUG | tracing::Level::TRACE => {
                // DEBUG/TRACE 级别：缓存到 LRU
                if let Some(mut cached_entries) = self.debug_cache.get_mut(&trace_id) {
                    cached_entries.push(entry);
                } else {
                    self.debug_cache.insert(trace_id, vec![entry]);
                }
            }
        }
    }
}

impl PerformanceStats {
    /// 获取轮转频率（次/分钟）
    pub fn get_rotation_frequency(&self) -> f64 {
        let elapsed_minutes =
            ((chrono::Utc::now().timestamp_millis() - self.last_rotation_time) as f64) / 60000.0;
        if elapsed_minutes > 0.0 {
            self.rotation_count as f64 / elapsed_minutes
        } else {
            0.0
        }
    }

    /// 获取写入速率（条/秒）
    pub fn get_write_rate(&self) -> f64 {
        if self.last_rotation_time > 0 {
            let elapsed_seconds =
                ((chrono::Utc::now().timestamp_millis() - self.last_rotation_time) as f64) / 1000.0;
            if elapsed_seconds > 0.0 {
                return self.write_count as f64 / elapsed_seconds as f64;
            }
        }
        0.0
    }

    /// 判断是否有性能问题
    pub fn has_performance_issues(&self) -> Vec<String> {
        let mut issues = Vec::new();

        // 轮转时间过长
        if self.avg_rotation_time_ms > 5000 {
            issues.push(format!("平均轮转时间过长: {}ms", self.avg_rotation_time_ms));
        }

        // 轮转频率过高
        let rotation_freq = self.get_rotation_frequency();
        if rotation_freq > 10.0 {
            issues.push(format!("轮转频率过高: {:.2}次/分钟", rotation_freq));
        }

        // 文件大小异常
        if self.current_file_size > MAX_LOG_FILE_SIZE * 2 {
            issues.push(format!(
                "文件大小异常: {}MB",
                self.current_file_size / 1024 / 1024
            ));
        }

        issues
    }
}
