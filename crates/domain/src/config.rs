use serde::{Deserialize, Serialize};
use shared::hash::HashAlgorithm;
use std::path::PathBuf;

/// 构建模式
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AppMode {
    Development,
    Production,
    Testing,
}

impl AppMode {
    pub fn as_str(&self) -> &'static str {
        match self {
            AppMode::Development => "development",
            AppMode::Production => "production",
            AppMode::Testing => "testing",
        }
    }
}

impl AppMode {
    #[inline]
    pub fn is_dev(&self) -> bool {
        matches!(self, AppMode::Development)
    }
    #[inline]
    pub fn is_prod(&self) -> bool {
        matches!(self, AppMode::Production)
    }
    #[inline]
    pub fn is_test(&self) -> bool {
        matches!(self, AppMode::Testing)
    }
}

#[derive(Debug, Clone)]
pub struct ContextConfig {
    pub app_mode: AppMode,
    pub version: String,

    pub userdata_dir: PathBuf,
    pub distro_name: String,
    pub product_name: String,

    pub min_disk_space: u64,

    pub environment: EnvironmentConfig,
}

impl Default for ContextConfig {
    fn default() -> Self {
        Self {
            app_mode: AppMode::Production,
            version: env!("CARGO_PKG_VERSION").to_string(),
            userdata_dir: dirs::data_dir()
                .unwrap_or_else(|| std::path::PathBuf::from("."))
                .join("echowave_client"),
            distro_name: "noble-rootfs".to_string(),
            product_name: "EchoWave".to_string(),
            min_disk_space: 1024 * 1024 * 1024 * 50, // 50GB
            environment: EnvironmentConfig::production(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct EnvironmentConfig {
    pub wsl_installer_url: String,
    pub wsl_installer_hash: String,
    pub wsl_installer_algorithm: HashAlgorithm,

    // Mirror configuration - following wsl_installer pattern
    pub mirror_url: String,
    pub mirror_hash: String,
    pub mirror_algorithm: HashAlgorithm,

    pub api_url: String,
    pub portal_url: String,
    pub client_name: String,
}

impl Default for EnvironmentConfig {
    fn default() -> Self {
        Self::production()
    }
}

impl EnvironmentConfig {
    pub fn development() -> Self {
        Self {
            wsl_installer_url: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi".to_string(),
            wsl_installer_hash: "E3E7062C39D05EF4D7D63D7AF5C61126".to_string(),
            wsl_installer_algorithm: HashAlgorithm::MD5,
            mirror_url: "https://dev-cdn.echowave.ai/wsl-mirrors/dev/echowave-engine.tar"
                .to_string(),
            mirror_hash: "".to_string(), // Will be fetched dynamically
            mirror_algorithm: HashAlgorithm::SHA256,
            api_url: "http://api.echowave.cn:8090".to_string(),
            portal_url: "https://www.echowave.cn".to_string(),
            client_name: "EchoWave 客户端(开发版)".to_string(),
        }
    }
    pub fn production() -> Self {
        Self {
            wsl_installer_url: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi".to_string(),
            wsl_installer_hash: "E3E7062C39D05EF4D7D63D7AF5C61126".to_string(),
            wsl_installer_algorithm: HashAlgorithm::MD5,
            mirror_url: "https://cdn.echowave.ai/wsl-mirrors/latest/echowave-engine.tar"
                .to_string(),
            mirror_hash: "".to_string(), // Will be fetched dynamically
            mirror_algorithm: HashAlgorithm::SHA256,
            api_url: "http://api.echowave.cn:8090".to_string(),
            portal_url: "https://www.echowave.cn".to_string(),
            client_name: "EchoWave 客户端".to_string(),
        }
    }
    pub fn testing() -> Self {
        Self {
            wsl_installer_url: "http://cdn.echowave.cn/releases/wsl-2.4.13-x64.msi".to_string(),
            wsl_installer_hash: "E3E7062C39D05EF4D7D63D7AF5C61126".to_string(),
            wsl_installer_algorithm: HashAlgorithm::MD5,
            mirror_url: "https://test-cdn.echowave.ai/wsl-mirrors/test/echowave-engine.tar"
                .to_string(),
            mirror_hash: "".to_string(), // Will be fetched dynamically
            mirror_algorithm: HashAlgorithm::SHA256,
            api_url: "http://api.echowave.cn:8090".to_string(),
            portal_url: "https://www.echowave.cn".to_string(),
            client_name: "EchoWave 客户端(测试版)".to_string(),
        }
    }
}
