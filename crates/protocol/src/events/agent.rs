use super::log::LogEntry;
pub use secrecy::{ExposeSecret, SecretString};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TailscaleAuth {
    #[serde(with = "serde_secret_string")]
    pub auth_key: SecretString,
    pub login_server: String,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct DockerAuth {
    pub registry_server: String,
    pub username: String,
    #[serde(with = "serde_secret_string")]
    pub password: SecretString,
}

mod serde_secret_string {
    use secrecy::ExposeSecret;
    use serde::{Deserializer, Serializer};

    use super::*; // 引入外部作用域的类型

    // 自定义序列化函数
    pub fn serialize<S>(secret: &SecretString, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // 调用 expose_secret() 获取内部的 &String，然后将其序列化为字符串
        serializer.serialize_str(secret.expose_secret())
    }

    // 自定义反序列化函数
    pub fn deserialize<'de, D>(deserializer: D) -> Result<SecretString, D::Error>
    where
        D: Deserializer<'de>,
    {
        // 先将输入反序列化为一个普通的 String
        let s = String::deserialize(deserializer)?;
        // 然后用 SecretString::new() 将其包装起来
        Ok(SecretString::from(s))
    }
}

/// 业务事件定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AgentEvent {
    // === Domain -> Agent (动词 - 请求/命令) ===
    /// 通知 Agent 关闭
    Shutdown { trace_id: Uuid },
    /// 停止守护进程（主要是 docker 和 nomad）
    Stop { trace_id: Uuid },
    /// 启动守护进程
    Start {
        trace_id: Uuid,
        tailscale_ip: String,
        docker_auth: Option<DockerAuth>,
    },
    /// 等待 Tailscale 就绪
    WaitForTailscaleReady {
        trace_id: Uuid,
        tailscale_auth: Option<TailscaleAuth>,
    },
    /// 获取节点状态
    GetNodeStatus { trace_id: Uuid },
    /// 获取网络延迟信息
    GetNetworkLatency { trace_id: Uuid },
    /// 获取 Agent 健康状态
    GetHealthStatus { trace_id: Uuid },

    /// 开始 Agent 更新（发送更新元数据）
    StartSelfUpdate {
        trace_id: Uuid,
        transfer_id: u32,
        version: String,
    },

    // === Agent -> Domain (名词 - 响应/数据) ===
    /// 日志转发事件
    LogForward(LogEntry),

    /// 批量日志转发
    LogBatchForward(Vec<LogEntry>),

    /// Agent 自我更新就绪，请求重启
    SelfUpdateReady {
        trace_id: Uuid,
        transfer_id: u32,
        success: bool,
        error_message: Option<String>,
    },

    /// === Agent <-> Domain ===
    Transfer(super::TransferEvent),
}

impl AgentEvent {
    pub fn event_name(&self) -> &'static str {
        match self {
            AgentEvent::Shutdown { .. } => "shutdown",
            AgentEvent::Stop { .. } => "stop",
            AgentEvent::Start { .. } => "start",
            AgentEvent::WaitForTailscaleReady { .. } => "wait_for_tailscale_ready",
            AgentEvent::GetNodeStatus { .. } => "get_node_status",
            AgentEvent::GetNetworkLatency { .. } => "get_network_latency",
            AgentEvent::GetHealthStatus { .. } => "get_health_status",
            AgentEvent::StartSelfUpdate { .. } => "start_self_update",
            AgentEvent::SelfUpdateReady { .. } => "self_update_ready",
            AgentEvent::LogForward(_) => "log_forward",
            AgentEvent::LogBatchForward(_) => "log_batch_forward",
            AgentEvent::Transfer(_) => "transfer",
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AgentResponse {
    /// 节点状态数据
    NodeStatus {
        trace_id: Uuid,
        idle: bool,
        tasks: Vec<TaskInfo>,
        node_info: NodeInfo,
    },
    /// 网络延迟数据
    NetworkLatency {
        trace_id: Uuid,
        latency_info: LatencyInfo,
    },
    /// Tailscale 就绪响应
    TailscaleReady {
        trace_id: Uuid,
        is_ready: bool,
        ip: Option<String>,
    },
    /// 健康检查响应
    HealthStatus {
        trace_id: Uuid,
        healthy: bool,
        version: String,
    },
    /// 成功响应，没有额外数据
    Success { trace_id: Uuid },
    Error {
        trace_id: Uuid,
        code: u32,
        message: String,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeInfo {
    pub node_id: String,
    pub ip_address: String,
    pub hostname: String,
    pub os_info: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskInfo {
    pub task_id: String,
    pub status: String,
    pub allocated_resources: ResourceInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceInfo {
    pub cpu: f64,
    pub memory: u64,
    pub disk: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LatencyInfo {
    /// Tailscale 到服务器的延迟 (毫秒)
    pub server_latency: Option<u64>,
    /// 测量时间戳
    pub timestamp: i64,
    /// 错误信息
    pub error: Option<String>,
}
