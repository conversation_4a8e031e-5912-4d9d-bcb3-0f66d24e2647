use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LogEntry {
    pub source: String,
    pub level: LogLevel,
    pub message: String,
    pub timestamp: u64,
    pub trace_id: Option<Uuid>,
    pub module: String,
    pub target: Option<String>,
    pub file: Option<String>,
    pub line: Option<u32>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

impl LogLevel {
    pub fn as_str(&self) -> &'static str {
        match self {
            LogLevel::Error => "ERROR",
            LogLevel::Warn => "WARN",
            LogLevel::Info => "INFO",
            LogLevel::Debug => "DEBUG",
            LogLevel::Trace => "TRACE",
        }
    }
}
