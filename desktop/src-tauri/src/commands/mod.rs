use crate::state::AppState;
use std::sync::Arc;
use tauri::ipc::Channel;
use tauri::{Manager, Window};
use uuid::Uuid;

pub mod folder_picker;

// 导入核心模块类型
use domain::context::{
    self, GlobalContext, command::CommandResponse, command::CoreCommand, event::CoreEvent,
};

/// 获取GlobalContext实例的助手函数
fn get_global_context(
    app_state: &tauri::State<'_, AppState>,
) -> Result<Arc<GlobalContext>, String> {
    Ok(app_state.global_context.clone())
}

#[tauri::command]
pub fn greet(name: String) -> String {
    format!("Hello, {}!", name)
}

/// 订阅核心事件流
#[tauri::command]
pub async fn subscribe_event(
    state: tauri::State<'_, AppState>,
    event_name: String,
    trace_id: Uuid,
    on_event: Channel<CoreEvent>,
) -> Result<u32, String> {
    let id = on_event.id();
    tracing::trace!(
        "New Tauri event subscription established for {} with id: {}",
        event_name,
        id
    );

    // 获取全局上下文
    let global_context = get_global_context(&state)?;

    // 将新的 channel 添加到订阅者列表，并自动同步到核心订阅管理器
    state
        .event_subscribers
        .add_channel_subscriber(event_name, on_event, trace_id, global_context.clone())
        .map_err(|err| err.to_string())?;

    // 记录当前订阅者数量
    let subscriber_count = state.event_subscribers.subscriber_count();
    tracing::trace!("Current Tauri event subscribers: {}", subscriber_count);

    Ok(id)
}

/// 取消订阅核心事件流
///
/// todo: 在 Webview 卸载时清除所有订阅，目前可能存在订阅未清除的情况
#[tauri::command]
pub async fn unsubscribe_event(
    state: tauri::State<'_, AppState>,
    event_name: String,
    event_id: u32,
    trace_id: Uuid,
) -> Result<(), String> {
    tracing::trace!(
        "Unsubscribing from Tauri event: {} with id: {}",
        event_name,
        event_id
    );

    // 获取全局上下文
    let global_context = get_global_context(&state)?;

    // 从订阅者列表中移除 channel，并自动同步到核心订阅管理器
    state
        .event_subscribers
        .remove_channel_subscriber(event_name, event_id, trace_id, global_context)
        .map_err(|err| err.to_string())?;

    Ok(())
}

#[tauri::command]
pub async fn cleanup_subscriptions(state: tauri::State<'_, AppState>) -> Result<(), String> {
    // 获取全局上下文
    let global_context = get_global_context(&state)?;

    state
        .event_subscribers
        .cleanup(global_context)
        .map_err(|err| err.to_string())?;

    Ok(())
}

/// 生成trace_id用于该请求
#[tauri::command]
pub fn get_global_trace_id() -> Uuid {
    context::get_global_trace_id()
}

/// 统一的命令执行接口
#[tauri::command]
pub async fn execute_command(
    state: tauri::State<'_, AppState>,
    command: CoreCommand,
) -> Result<CommandResponse, String> {
    let global_context = get_global_context(&state)?;

    // 执行命令
    global_context.execute_command(command).await.map_err(|e| {
        tracing::error!("Failed to execute command: {}", e);
        e.to_string()
    })
}

/// 关闭窗口命令
#[tauri::command]
pub fn close_window(window: Window) -> Result<(), String> {
    window.hide().map_err(|e| {
        tracing::error!("Failed to hide window: {}", e);
        e.to_string()
    })
}
