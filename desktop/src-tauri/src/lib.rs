mod commands;
mod idle_watcher;
mod state;
mod tray;

use tauri::Manager;

pub async fn run() -> anyhow::Result<()> {
    let mut builder = tauri::Builder::default();

    // 单实例插件必须最先添加以确保正常工作
    #[cfg(desktop)]
    {
        builder = builder.plugin(tauri_plugin_single_instance::init(|app, args, cwd| {
            tracing::info!("检测到新实例启动: args={:?}, cwd={}", args, cwd);

            // 获取主窗口并显示、聚焦
            if let Some(window) = app.get_webview_window("main") {
                match window.is_visible() {
                    Ok(true) => {
                        // 窗口已可见，只需聚焦
                        if let Err(e) = window.set_focus() {
                            tracing::warn!("设置窗口焦点失败: {}", e);
                        } else {
                            tracing::info!("成功聚焦现有窗口");
                        }
                    }
                    Ok(false) => {
                        // 窗口隐藏，需要显示并聚焦
                        if let Err(e) = window.show() {
                            tracing::warn!("显示窗口失败: {}", e);
                        } else if let Err(e) = window.set_focus() {
                            tracing::warn!("设置窗口焦点失败: {}", e);
                        } else {
                            tracing::info!("成功显示并聚焦窗口");
                        }
                    }
                    Err(e) => {
                        tracing::error!("检查窗口可见性失败: {}", e);
                        // 尝试显示窗口
                        let _ = window.show();
                        let _ = window.set_focus();
                    }
                }
            } else {
                tracing::error!("无法获取主窗口");
            }
        }));
    }

    builder
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            commands::greet,
            commands::subscribe_event,
            commands::unsubscribe_event,
            commands::get_global_trace_id,
            commands::execute_command,
            // 暂时注释掉自定义关闭窗口命令
            // commands::close_window,
            commands::folder_picker::pick_folder,
            commands::folder_picker::validate_folder_path,
        ])
        .setup(|app| {
            tracing::info!("开始构建应用状态");
            state::build_app_state(app)?;
            tracing::info!("应用状态构建完成");

            // 设置窗口位置到右下角
            if let Some(window) = app.get_webview_window("main") {
                if let Ok(monitor) = window.current_monitor() {
                    if let Some(monitor) = monitor {
                        let monitor_size = monitor.size();
                        let window_size = window.outer_size().unwrap();

                        // 计算右下角位置，留出一些边距
                        let x = monitor_size.width as i32 - window_size.width as i32 - 20;
                        let y = monitor_size.height as i32 - window_size.height as i32 - 60; // 60像素留给任务栏

                        if let Err(e) = window.set_position(tauri::Position::Physical(
                            tauri::PhysicalPosition { x, y },
                        )) {
                            tracing::warn!("设置窗口位置失败: {}", e);
                        } else {
                            tracing::info!("窗口已移动到右下角位置: ({}, {})", x, y);
                        }
                    }
                }
            }

            tray::create_tray(app)?;
            tracing::info!("托盘创建完成");

            // 设置空闲状态监听器
            let app_handle = app.handle().clone();
            tokio::spawn(async move {
                if let Err(e) = idle_watcher::setup_idle_watcher(app_handle).await {
                    tracing::error!("设置空闲监听器失败: {}", e);
                } else {
                    tracing::info!("空闲监听器设置完成");
                }
            });

            Ok(())
        })
        .on_window_event(|window, event| match event {
            tauri::WindowEvent::CloseRequested { api, .. } => {
                api.prevent_close();
                window.hide().unwrap();
            }
            tauri::WindowEvent::Destroyed => {
                tracing::info!("窗口销毁");
            }
            tauri::WindowEvent::Moved(_) => {
                // 防止窗口移动，始终保持在右下角
                if let Ok(monitor) = window.current_monitor() {
                    if let Some(monitor) = monitor {
                        let monitor_size = monitor.size();
                        let window_size = window.outer_size().unwrap();

                        let x = monitor_size.width as i32 - window_size.width as i32 - 20;
                        let y = monitor_size.height as i32 - window_size.height as i32 - 60;

                        let _ = window.set_position(tauri::Position::Physical(
                            tauri::PhysicalPosition { x, y },
                        ));
                    }
                }
            }
            tauri::WindowEvent::Focused(focused) => {
                // 当窗口失去焦点时自动隐藏
                if !focused {
                    #[cfg(not(debug_assertions))]
                    {
                        tracing::info!("窗口失去焦点，自动隐藏");
                        let _ = window.hide();
                    }
                    #[cfg(debug_assertions)]
                    {
                        tracing::info!("窗口失去焦点，开发模式不隐藏");
                    }
                }
            }
            _ => {}
        })
        .run(tauri::generate_context!())?;
    tracing::info!("Desktop application started");
    Ok(())
}
