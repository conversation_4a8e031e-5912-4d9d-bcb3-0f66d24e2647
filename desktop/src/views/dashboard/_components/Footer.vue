<script setup lang="ts">
import { computed } from "vue";
import { useDeviceStore } from "~/stores/device";

const deviceStore = useDeviceStore();
const deviceId = computed(() => deviceStore.state.machine_id.slice(-8));
const clientVersion = __VERSION__;
const mirrorVersion = "1.0"
</script>
<template>
    <footer class="flex justify-between h-[20px] m-[16px] font-[Regular]">
        <span>设备号：{{ deviceId }}</span>
        <div class="text-[#9a9a9a] text-sm">
            <span>客户端：v{{ clientVersion }}</span>
            <span v-if="mirrorVersion">
                _{{ mirrorVersion }}
            </span>
        </div>
    </footer>
</template>