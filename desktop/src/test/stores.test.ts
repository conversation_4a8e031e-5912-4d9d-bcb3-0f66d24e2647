import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createP<PERSON> } from 'pinia'
import { useAppStore, useNetworkStore, useUserStore, useTaskStore, useSystemStore, useSettingsStore } from '@/stores'

describe('Pinia Stores', () => {
  beforeEach(() => {
    // 为每个测试创建新的Pinia实例
    setActivePinia(createPinia())
  })

  describe('AppStore', () => {
    it('should initialize with correct default state', () => {
      const appStore = useAppStore()
      
      expect(appStore.isInitialized).toBe(false)
      expect(appStore.isLoading).toBe(false)
      expect(appStore.error).toBe(null)
      expect(appStore.isReady).toBe(false)
    })

    it('should handle loading state correctly', () => {
      const appStore = useAppStore()
      
      appStore.setLoading(true)
      expect(appStore.isLoading).toBe(true)
      expect(appStore.error).toBe(null)
      
      appStore.setLoading(false)
      expect(appStore.isLoading).toBe(false)
    })

    it('should handle error state correctly', () => {
      const appStore = useAppStore()
      
      appStore.setError('Test error')
      expect(appStore.error).toBe('Test error')
      expect(appStore.isLoading).toBe(false)
      
      appStore.clearError()
      expect(appStore.error).toBe(null)
    })
  })

  describe('NetworkStore', () => {
    it('should initialize with correct default state', () => {
      const networkStore = useNetworkStore()
      
      expect(networkStore.tailscale_latency).toBe(null)
      expect(networkStore.internet_connectivity).toBe(false)
      expect(networkStore.isOnline).toBe(false)
      expect(networkStore.connectionQuality).toBe('offline')
    })

    it('should update network status correctly', () => {
      const networkStore = useNetworkStore()
      
      const mockStatus = {
        tailscale_latency: 30, // 改为30以确保是excellent质量
        internet_connectivity: true,
        last_update_time: new Date().toISOString()
      }

      networkStore.updateNetworkStatus(mockStatus)

      expect(networkStore.tailscale_latency).toBe(30)
      expect(networkStore.internet_connectivity).toBe(true)
      expect(networkStore.isOnline).toBe(true)
      expect(networkStore.connectionQuality).toBe('excellent')
    })

    it('should calculate connection quality correctly', () => {
      const networkStore = useNetworkStore()
      
      // Test excellent quality
      networkStore.updateNetworkStatus({
        tailscale_latency: 30,
        internet_connectivity: true,
        last_update_time: new Date().toISOString()
      })
      expect(networkStore.connectionQuality).toBe('excellent')
      
      // Test good quality
      networkStore.updateNetworkStatus({
        tailscale_latency: 80,
        internet_connectivity: true,
        last_update_time: new Date().toISOString()
      })
      expect(networkStore.connectionQuality).toBe('good')
      
      // Test poor quality
      networkStore.updateNetworkStatus({
        tailscale_latency: 250,
        internet_connectivity: true,
        last_update_time: new Date().toISOString()
      })
      expect(networkStore.connectionQuality).toBe('poor')
    })
  })

  describe('UserStore', () => {
    it('should initialize with correct default state', () => {
      const userStore = useUserStore()
      
      expect(userStore.is_logged_in).toBe(false)
      expect(userStore.user_info).toBe(null)
      expect(userStore.wallet_balance).toBe(0.0)
      expect(userStore.isLoggedIn).toBe(false)
      expect(userStore.username).toBe(null)
    })

    it('should update user status correctly', () => {
      const userStore = useUserStore()
      
      const mockStatus = {
        is_logged_in: true,
        user_info: { username: 'testuser', id: 'user123' },
        wallet_balance: 123.45,
        last_sync_time: new Date().toISOString()
      }
      
      userStore.updateUserStatus(mockStatus)
      
      expect(userStore.is_logged_in).toBe(true)
      expect(userStore.username).toBe('testuser')
      expect(userStore.wallet_balance).toBe(123.45)
      expect(userStore.formattedBalance).toBe('$123.45')
    })
  })

  describe('TaskStore', () => {
    it('should initialize with correct default state', () => {
      const taskStore = useTaskStore()
      
      expect(taskStore.isAccepting).toBe(false)
      expect(taskStore.isExecuting).toBe(false)
      expect(taskStore.currentTaskId).toBe(null)
      expect(taskStore.canToggleAcceptance).toBe(true)
    })

    it('should update task acceptance status correctly', () => {
      const taskStore = useTaskStore()
      
      const mockAcceptance = {
        is_accepting: true,
        auto_accept_enabled: false,
        last_toggle_time: new Date().toISOString()
      }
      
      taskStore.updateTaskAcceptance(mockAcceptance)
      
      expect(taskStore.isAccepting).toBe(true)
      expect(taskStore.autoAcceptEnabled).toBe(false)
    })

    it('should calculate task status summary correctly', () => {
      const taskStore = useTaskStore()
      
      // Test idle status
      expect(taskStore.taskStatusSummary.status).toBe('idle')
      
      // Test accepting status
      taskStore.updateTaskAcceptance({
        is_accepting: true,
        auto_accept_enabled: false,
        last_toggle_time: new Date().toISOString()
      })
      expect(taskStore.taskStatusSummary.status).toBe('accepting')
      
      // Test executing status
      taskStore.updateTaskExecution({
        is_executing: true,
        current_task_id: 'task123',
        start_time: new Date().toISOString()
      })
      expect(taskStore.taskStatusSummary.status).toBe('executing')
    })
  })

  describe('SystemStore', () => {
    it('should initialize with correct default state', () => {
      const systemStore = useSystemStore()
      
      expect(systemStore.checkResults).toEqual([])
      expect(systemStore.overallSystemStatus).toBe('unknown')
      expect(systemStore.systemHealthScore).toBe(0)
      expect(systemStore.isPowerShellReady).toBe(false)
      expect(systemStore.isWSLReady).toBe(false)
    })

    it('should calculate system health score correctly', () => {
      const systemStore = useSystemStore()
      
      const mockResults = [
        { check_name: 'Test 1', status: 'Pass', message: 'OK', auto_fixable: false, details: null, check_type: 'All' },
        { check_name: 'Test 2', status: 'Pass', message: 'OK', auto_fixable: false, details: null, check_type: 'All' },
        { check_name: 'Test 3', status: 'Fail', message: 'Error', auto_fixable: false, details: null, check_type: 'All' }
      ]
      
      systemStore.updateSystemCheckResults(mockResults)
      
      expect(systemStore.systemHealthScore).toBe(67) // 2/3 * 100 = 66.67 -> 67
      expect(systemStore.overallSystemStatus).toBe('error')
    })
  })

  describe('SettingsStore', () => {
    it('should initialize with correct default state', () => {
      const settingsStore = useSettingsStore()
      
      expect(settingsStore.auto_accept_tasks).toBe(false)
      expect(settingsStore.auto_start_on_boot).toBe(false)
      expect(settingsStore.animations_enabled).toBe(true)
      expect(settingsStore.isDirty).toBe(false)
      expect(settingsStore.hasUnsavedChanges).toBe(false)
    })

    it('should detect changes correctly', async () => {
      const settingsStore = useSettingsStore()

      // Initially no changes
      expect(settingsStore.isDirty).toBe(false)

      // Make a change
      settingsStore.toggleSetting('auto_accept_tasks')

      // Wait for next tick for watch to trigger
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(settingsStore.isDirty).toBe(true)
      expect(settingsStore.hasUnsavedChanges).toBe(true)

      // Reset changes
      settingsStore.resetSettings()
      expect(settingsStore.isDirty).toBe(false)
    })

    it('should validate settings correctly', () => {
      const settingsStore = useSettingsStore()
      
      const validSettings = {
        auto_accept_tasks: true,
        auto_start_on_boot: true,
        animations_enabled: false
      }
      
      const validation = settingsStore.validateSettings(validSettings)
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toEqual([])
    })
  })
})
