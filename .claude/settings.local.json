{"permissions": {"allow": ["Bash(cargo check:*)", "Bash(git add:*)", "Bash(git commit:*)", "mcp__promptx__promptx_action", "mcp__sequential-thinking__sequentialthinking", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__filesystem__list_directory", "mcp__filesystem__read_multiple_files", "mcp__filesystem__directory_tree", "mcp__filesystem__read_file", "mcp__filesystem__search_files", "mcp__filesystem__write_file", "mcp__filesystem__edit_file", "mcp__promptx__promptx_remember", "mcp__promptx__promptx_recall", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(cargo build:*)", "Bash(grep:*)", "Bash(cargo test:*)", "Bash(rustc:*)", "Bash(./test_stack_overflow)", "Bash(cargo run:*)", "WebFetch(domain:github.com)", "WebFetch(domain:docs.rs)", "WebFetch(domain:v2.tauri.app)", "mcp__serena__find_symbol", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__think_about_collected_information", "mcp__serena__find_referencing_symbols", "mcp__serena__find_file", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__write_memory", "mcp__serena__initial_instructions", "mcp__serena__replace_regex", "mcp__serena__read_memory", "mcp__serena__think_about_task_adherence", "mcp__serena__insert_after_symbol", "mcp__serena__replace_symbol_body", "mcp__serena__think_about_whether_you_are_done", "<PERSON><PERSON>(sed:*)"], "deny": []}}